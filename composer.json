{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "repositories": {"ophimcms/theme-iqiyi": {"type": "path", "url": "packages/ophimcms/theme-iqiyi", "options": {"symlink": true}}, "ophimcms/theme-vvmotchill": {"type": "path", "url": "packages/ophimcms/theme-vvmotchill", "options": {"symlink": true}}, "ophimcms/theme-vieon-2025": {"type": "path", "url": "packages/ophimcms/theme-vieon-2025", "options": {"symlink": true}}}, "license": "MIT", "require": {"php": "^7.3|^8.0", "ext-json": "*", "famdirksen/laravel-google-indexing": "^0.5.0", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.7", "hacoidev/ophim-core": "^1.2", "hacoidev/ophim-crawler": "^1.1", "laravel/framework": "^8.75", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "ophimcms/theme-pno": "^1.2", "ophimcms/theme-vieon-2025": "*", "predis/predis": "^2.2", "pusher/pusher-php-server": "^7.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}