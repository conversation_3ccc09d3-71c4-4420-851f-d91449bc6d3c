/**
 * Advanced Search System - Vanilla JavaScript Implementation
 * Features: Real-time suggestions, keyboard navigation, search history
 */

class AdvancedSearch {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            apiUrl: '/api/search',
            popularUrl: '/api/search/popular',
            minLength: 2,
            debounceDelay: 300,
            maxSuggestions: 10,
            maxHistory: 10,
            enableHistory: true,
            enablePopular: true,
            ...options
        };
        
        this.input = null;
        this.dropdown = null;
        this.isOpen = false;
        this.selectedIndex = -1;
        this.currentSuggestions = [];
        this.searchHistory = [];
        this.popularSearches = [];
        this.debounceTimer = null;
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.createElements();
        this.bindEvents();
        this.loadSearchHistory();
        this.loadPopularSearches();
    }
    
    createElements() {
        // Find or create input
        this.input = this.container.querySelector('input[type="text"]');
        if (!this.input) {
            console.error('Search input not found in container');
            return;
        }
        
        // Create dropdown container
        this.dropdown = document.createElement('div');
        this.dropdown.className = 'advanced-search-dropdown absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-b-lg shadow-lg z-50 hidden';
        this.dropdown.innerHTML = `
            <div class="dropdown-content max-h-96 overflow-y-auto">
                <!-- Content will be populated by JavaScript -->
            </div>
        `;
        
        // Insert dropdown after input container
        const inputContainer = this.input.closest('.relative') || this.input.parentNode;
        inputContainer.style.position = 'relative';
        inputContainer.appendChild(this.dropdown);
        
        // Add loading indicator
        this.createLoadingIndicator();
    }
    
    createLoadingIndicator() {
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'search-loading absolute right-3 top-1/2 transform -translate-y-1/2 hidden';
        loadingIndicator.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>';
        
        const inputContainer = this.input.closest('.relative') || this.input.parentNode;
        inputContainer.appendChild(loadingIndicator);
        
        this.loadingIndicator = loadingIndicator;
    }
    
    bindEvents() {
        // Input events
        this.input.addEventListener('input', (e) => this.handleInput(e));
        this.input.addEventListener('focus', (e) => this.handleFocus(e));
        this.input.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // Document events
        document.addEventListener('click', (e) => this.handleDocumentClick(e));
        
        // Form submission
        const form = this.input.closest('form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }
    
    handleInput(e) {
        const query = e.target.value.trim();
        
        // Clear previous timer
        clearTimeout(this.debounceTimer);
        
        if (query.length >= this.options.minLength) {
            // Debounced search
            this.debounceTimer = setTimeout(() => {
                this.performSearch(query);
            }, this.options.debounceDelay);
        } else if (query.length === 0) {
            // Show history/popular when empty
            this.showDefaultSuggestions();
        } else {
            // Hide dropdown for short queries
            this.hideDropdown();
        }
    }
    
    handleFocus(e) {
        const query = e.target.value.trim();
        
        if (query.length >= this.options.minLength) {
            this.performSearch(query);
        } else {
            this.showDefaultSuggestions();
        }
    }
    
    handleKeydown(e) {
        if (!this.isOpen) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.navigateDown();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.navigateUp();
                break;
            case 'Enter':
                e.preventDefault();
                this.selectCurrent();
                break;
            case 'Escape':
                e.preventDefault();
                this.hideDropdown();
                this.input.blur();
                break;
        }
    }
    
    handleDocumentClick(e) {
        if (!this.container.contains(e.target)) {
            this.hideDropdown();
        }
    }
    
    handleFormSubmit(e) {
        const query = this.input.value.trim();
        if (query) {
            this.addToHistory(query);
        }
    }
    
    async performSearch(query) {
        this.setLoading(true);
        
        try {
            const response = await fetch(`${this.options.apiUrl}?keyword=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            this.currentSuggestions = data.movies || [];
            this.displaySuggestions(this.currentSuggestions, 'search');
        } catch (error) {
            console.error('Search error:', error);
            this.displayError('Không thể tải kết quả tìm kiếm');
        } finally {
            this.setLoading(false);
        }
    }
    
    showDefaultSuggestions() {
        const suggestions = [];
        
        // Add recent searches
        if (this.options.enableHistory && this.searchHistory.length > 0) {
            suggestions.push({
                type: 'history',
                title: 'Tìm kiếm gần đây',
                items: this.searchHistory.slice(0, 5)
            });
        }
        
        // Add popular searches
        if (this.options.enablePopular && this.popularSearches.length > 0) {
            suggestions.push({
                type: 'popular',
                title: 'Tìm kiếm phổ biến',
                items: this.popularSearches.slice(0, 5)
            });
        }
        
        if (suggestions.length > 0) {
            this.displayDefaultSuggestions(suggestions);
        }
    }
    
    displaySuggestions(movies, type = 'search') {
        const content = this.dropdown.querySelector('.dropdown-content');
        
        if (movies.length === 0) {
            content.innerHTML = `
                <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                    <i class="fas fa-search mb-2"></i>
                    <p>Không tìm thấy kết quả phù hợp</p>
                </div>
            `;
        } else {
            content.innerHTML = movies.map((movie, index) => `
                <div class="suggestion-item flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0" 
                     data-index="${index}" 
                     data-url="${movie.url}"
                     data-title="${movie.name}">
                    <img src="${movie.thumb_url}" 
                         alt="${movie.name}" 
                         class="w-12 h-16 object-cover rounded mr-3 flex-shrink-0"
                         onerror="this.src='/themes/vieon-2025/images/no-image.jpg'">
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-gray-900 dark:text-white truncate">${movie.name}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300 truncate">${movie.origin_name}</p>
                        <div class="flex items-center gap-2 mt-1">
                            <span class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">${movie.quality}</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400">${movie.publish_year}</span>
                            ${movie.episode_current ? `<span class="text-xs text-gray-500 dark:text-gray-400">Tập ${movie.episode_current}</span>` : ''}
                        </div>
                    </div>
                    <i class="fas fa-arrow-right text-gray-400 ml-2"></i>
                </div>
            `).join('');
        }
        
        this.bindSuggestionEvents();
        this.showDropdown();
    }
    
    displayDefaultSuggestions(suggestions) {
        const content = this.dropdown.querySelector('.dropdown-content');
        
        content.innerHTML = suggestions.map(section => `
            <div class="suggestion-section">
                <div class="section-header p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                    <h5 class="font-medium text-gray-700 dark:text-gray-300 text-sm">
                        <i class="fas fa-${section.type === 'history' ? 'history' : 'fire'} mr-2"></i>
                        ${section.title}
                    </h5>
                </div>
                <div class="section-items">
                    ${section.items.map((item, index) => `
                        <div class="suggestion-item flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                             data-query="${typeof item === 'string' ? item : item.query}"
                             data-type="${section.type}">
                            <i class="fas fa-${section.type === 'history' ? 'clock' : 'search'} text-gray-400 mr-3"></i>
                            <span class="flex-1 text-gray-700 dark:text-gray-300">${typeof item === 'string' ? item : item.query}</span>
                            ${section.type === 'history' ? '<i class="fas fa-times text-gray-400 hover:text-red-500 ml-2" data-action="remove"></i>' : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
        
        this.bindDefaultSuggestionEvents();
        this.showDropdown();
    }
    
    displayError(message) {
        const content = this.dropdown.querySelector('.dropdown-content');
        content.innerHTML = `
            <div class="p-4 text-center text-red-500">
                <i class="fas fa-exclamation-triangle mb-2"></i>
                <p>${message}</p>
            </div>
        `;
        this.showDropdown();
    }
    
    bindSuggestionEvents() {
        const items = this.dropdown.querySelectorAll('.suggestion-item');
        items.forEach((item, index) => {
            item.addEventListener('click', () => {
                const url = item.dataset.url;
                const title = item.dataset.title;
                
                if (url && title) {
                    this.addToHistory(title);
                    window.location.href = url;
                }
            });
            
            item.addEventListener('mouseenter', () => {
                this.setSelectedIndex(index);
            });
        });
    }
    
    bindDefaultSuggestionEvents() {
        const items = this.dropdown.querySelectorAll('.suggestion-item');
        items.forEach((item, index) => {
            item.addEventListener('click', (e) => {
                if (e.target.dataset.action === 'remove') {
                    e.stopPropagation();
                    const query = item.dataset.query;
                    this.removeFromHistory(query);
                    return;
                }
                
                const query = item.dataset.query;
                const type = item.dataset.type;
                
                if (query) {
                    this.input.value = query;
                    this.addToHistory(query);
                    this.performSearch(query);
                }
            });
        });
    }
    
    navigateDown() {
        const items = this.dropdown.querySelectorAll('.suggestion-item');
        this.selectedIndex = Math.min(this.selectedIndex + 1, items.length - 1);
        this.updateSelection();
    }
    
    navigateUp() {
        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        this.updateSelection();
    }
    
    updateSelection() {
        const items = this.dropdown.querySelectorAll('.suggestion-item');
        items.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('bg-blue-50', 'dark:bg-blue-900');
            } else {
                item.classList.remove('bg-blue-50', 'dark:bg-blue-900');
            }
        });
    }
    
    setSelectedIndex(index) {
        this.selectedIndex = index;
        this.updateSelection();
    }
    
    selectCurrent() {
        const items = this.dropdown.querySelectorAll('.suggestion-item');
        if (this.selectedIndex >= 0 && items[this.selectedIndex]) {
            items[this.selectedIndex].click();
        } else {
            // Submit form if no selection
            const form = this.input.closest('form');
            if (form) {
                form.submit();
            }
        }
    }
    
    showDropdown() {
        this.dropdown.classList.remove('hidden');
        this.isOpen = true;
        this.selectedIndex = -1;
    }
    
    hideDropdown() {
        this.dropdown.classList.add('hidden');
        this.isOpen = false;
        this.selectedIndex = -1;
    }
    
    setLoading(loading) {
        this.isLoading = loading;
        
        if (this.loadingIndicator) {
            if (loading) {
                this.loadingIndicator.classList.remove('hidden');
            } else {
                this.loadingIndicator.classList.add('hidden');
            }
        }
    }
    
    // Search History Management
    loadSearchHistory() {
        if (!this.options.enableHistory) return;
        
        try {
            const history = localStorage.getItem('vieon_search_history');
            this.searchHistory = history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('Error loading search history:', error);
            this.searchHistory = [];
        }
    }
    
    addToHistory(query) {
        if (!this.options.enableHistory || !query.trim()) return;
        
        // Remove if already exists
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        
        // Add to beginning
        this.searchHistory.unshift(query);
        
        // Limit history size
        this.searchHistory = this.searchHistory.slice(0, this.options.maxHistory);
        
        // Save to localStorage
        try {
            localStorage.setItem('vieon_search_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('Error saving search history:', error);
        }
    }
    
    removeFromHistory(query) {
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        
        try {
            localStorage.setItem('vieon_search_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('Error updating search history:', error);
        }
        
        // Refresh display
        this.showDefaultSuggestions();
    }
    
    clearHistory() {
        this.searchHistory = [];
        try {
            localStorage.removeItem('vieon_search_history');
        } catch (error) {
            console.error('Error clearing search history:', error);
        }
    }
    
    // Popular Searches
    async loadPopularSearches() {
        if (!this.options.enablePopular) return;
        
        try {
            const response = await fetch(this.options.popularUrl);
            const data = await response.json();
            this.popularSearches = data.searches || [];
        } catch (error) {
            console.error('Error loading popular searches:', error);
            this.popularSearches = [];
        }
    }
    
    // Public API
    setValue(value) {
        this.input.value = value;
        if (value.trim().length >= this.options.minLength) {
            this.performSearch(value.trim());
        }
    }
    
    focus() {
        this.input.focus();
    }
    
    clear() {
        this.input.value = '';
        this.hideDropdown();
    }
}

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all search containers
    document.querySelectorAll('[data-advanced-search]').forEach(container => {
        const options = {
            apiUrl: container.dataset.apiUrl || '/api/search',
            popularUrl: container.dataset.popularUrl || '/api/search/popular',
            minLength: parseInt(container.dataset.minLength) || 2,
            debounceDelay: parseInt(container.dataset.debounceDelay) || 300,
            maxSuggestions: parseInt(container.dataset.maxSuggestions) || 10,
            enableHistory: container.dataset.enableHistory !== 'false',
            enablePopular: container.dataset.enablePopular !== 'false'
        };
        
        new AdvancedSearch(container, options);
    });
});

// Export for use in other scripts
window.AdvancedSearch = AdvancedSearch;
