/**
 * Episode Reporting System - Vanilla JavaScript Implementation
 * Integrates with EpisodeController::reportEpisode() endpoint
 */

class EpisodeReporting {
    constructor() {
        this.modal = null;
        this.form = null;
        this.isSubmitting = false;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupElements());
        } else {
            this.setupElements();
        }
    }
    
    setupElements() {
        this.modal = document.getElementById('reportModal');
        this.form = document.getElementById('reportForm');
        
        if (!this.modal || !this.form) {
            console.warn('Episode reporting elements not found');
            return;
        }
        
        this.bindEvents();
        this.setupFormValidation();
    }
    
    bindEvents() {
        // Form submission
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // Message counter
        const messageTextarea = document.getElementById('message');
        const messageCounter = document.getElementById('messageCounter');
        
        if (messageTextarea && messageCounter) {
            messageTextarea.addEventListener('input', () => {
                const length = messageTextarea.value.length;
                messageCounter.textContent = `${length}/500`;
                
                if (length > 450) {
                    messageCounter.classList.add('text-red-500');
                    messageCounter.classList.remove('text-theme-quaternary');
                } else {
                    messageCounter.classList.remove('text-red-500');
                    messageCounter.classList.add('text-theme-quaternary');
                }
            });
        }
        
        // Escape key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isModalOpen()) {
                this.closeModal();
            }
        });
        
        // Prevent modal close when clicking inside modal content
        const modalContent = document.getElementById('reportModalContent');
        if (modalContent) {
            modalContent.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    }
    
    setupFormValidation() {
        const messageField = document.getElementById('message');
        const reasonField = document.getElementById('reason');
        
        if (messageField) {
            messageField.addEventListener('blur', () => this.validateMessage());
            messageField.addEventListener('input', () => this.clearFieldError(messageField));
        }
        
        if (reasonField) {
            reasonField.addEventListener('change', () => this.clearFieldError(reasonField));
        }
    }
    
    validateMessage() {
        const messageField = document.getElementById('message');
        const message = messageField.value.trim();
        
        if (message.length < 10) {
            this.showFieldError(messageField, 'Vui lòng mô tả chi tiết hơn (tối thiểu 10 ký tự)');
            return false;
        }
        
        if (message.length > 500) {
            this.showFieldError(messageField, 'Mô tả quá dài (tối đa 500 ký tự)');
            return false;
        }
        
        this.clearFieldError(messageField);
        return true;
    }
    
    showFieldError(field, message) {
        this.clearFieldError(field);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-red-500 text-xs mt-1';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
        field.classList.add('border-red-500', 'focus:ring-red-500');
    }
    
    clearFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        field.classList.remove('border-red-500', 'focus:ring-red-500');
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        if (this.isSubmitting) return;
        
        // Validate form
        if (!this.validateForm()) {
            return;
        }
        
        this.setSubmitting(true);
        
        try {
            const formData = new FormData(this.form);
            const response = await this.submitReport(formData);
            
            if (response.status) {
                this.handleSuccess(response);
            } else {
                this.handleError(response.message || 'Có lỗi xảy ra khi gửi báo cáo');
            }
        } catch (error) {
            console.error('Report submission error:', error);
            this.handleError('Không thể kết nối đến server. Vui lòng thử lại.');
        } finally {
            this.setSubmitting(false);
        }
    }
    
    validateForm() {
        const messageField = document.getElementById('message');
        const message = messageField.value.trim();
        
        if (!message) {
            this.showFieldError(messageField, 'Vui lòng nhập mô tả vấn đề');
            messageField.focus();
            return false;
        }
        
        return this.validateMessage();
    }
    
    async submitReport(formData) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        const response = await fetch(this.form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    handleSuccess(response) {
        this.showNotification('success', 'Báo cáo thành công!', response.message);
        this.resetForm();
        this.closeModal();
    }
    
    handleError(message) {
        this.showNotification('error', 'Lỗi gửi báo cáo', message);
    }
    
    setSubmitting(submitting) {
        this.isSubmitting = submitting;
        
        const submitBtn = document.getElementById('submitReportBtn');
        const submitText = submitBtn.querySelector('.submit-text');
        const loadingText = submitBtn.querySelector('.loading-text');
        
        if (submitting) {
            submitBtn.disabled = true;
            submitText.classList.add('hidden');
            loadingText.classList.remove('hidden');
        } else {
            submitBtn.disabled = false;
            submitText.classList.remove('hidden');
            loadingText.classList.add('hidden');
        }
    }
    
    openModal() {
        if (!this.modal) return;
        
        this.modal.classList.remove('hidden');
        
        // Trigger animation
        setTimeout(() => {
            this.modal.classList.remove('opacity-0');
            const content = document.getElementById('reportModalContent');
            if (content) {
                content.classList.remove('scale-95');
                content.classList.add('scale-100');
            }
        }, 10);
        
        // Focus first input
        const firstInput = this.modal.querySelector('select, textarea, input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
        if (!this.modal) return;
        
        const content = document.getElementById('reportModalContent');
        if (content) {
            content.classList.remove('scale-100');
            content.classList.add('scale-95');
        }
        
        this.modal.classList.add('opacity-0');
        
        setTimeout(() => {
            this.modal.classList.add('hidden');
            document.body.style.overflow = '';
        }, 300);
    }
    
    isModalOpen() {
        return this.modal && !this.modal.classList.contains('hidden');
    }
    
    resetForm() {
        if (!this.form) return;
        
        this.form.reset();
        
        // Clear any field errors
        this.form.querySelectorAll('.field-error').forEach(error => error.remove());
        this.form.querySelectorAll('input, textarea, select').forEach(field => {
            field.classList.remove('border-red-500', 'focus:ring-red-500');
        });
        
        // Reset message counter
        const messageCounter = document.getElementById('messageCounter');
        if (messageCounter) {
            messageCounter.textContent = '0/500';
            messageCounter.classList.remove('text-red-500');
            messageCounter.classList.add('text-theme-quaternary');
        }
    }
    
    showNotification(type, title, message) {
        const notification = document.getElementById('reportNotification');
        const icon = document.getElementById('notificationIcon');
        const titleEl = document.getElementById('notificationTitle');
        const messageEl = document.getElementById('notificationMessage');
        
        if (!notification || !icon || !titleEl || !messageEl) return;
        
        // Set icon and colors based on type
        if (type === 'success') {
            icon.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
            notification.querySelector('div').classList.add('border-green-200');
        } else {
            icon.innerHTML = '<i class="fas fa-exclamation-circle text-red-500"></i>';
            notification.querySelector('div').classList.add('border-red-200');
        }
        
        titleEl.textContent = title;
        messageEl.textContent = message;
        
        // Show notification
        notification.classList.remove('hidden');
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            this.hideNotification();
        }, 5000);
    }
    
    hideNotification() {
        const notification = document.getElementById('reportNotification');
        if (notification) {
            notification.classList.add('hidden');
            
            // Reset classes
            const container = notification.querySelector('div');
            container.classList.remove('border-green-200', 'border-red-200');
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.EpisodeReporting = new EpisodeReporting();
});

// Export for use in other scripts
window.EpisodeReporting = EpisodeReporting;
