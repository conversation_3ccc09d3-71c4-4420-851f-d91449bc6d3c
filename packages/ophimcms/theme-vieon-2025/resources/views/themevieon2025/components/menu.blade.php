@php
    // Get menu data similar to theme-pno
    $menu = \Ophim\Core\Models\Menu::getTree();
    $categories = \Ophim\Core\Models\Category::fromCache()->all();
    $regions = \Ophim\Core\Models\Region::fromCache()->all();

    // Get hot movies from theme options
    $hotMovies = Cache::remember('site.movies.hot', setting('site_cache_ttl', 5 * 60), function () {
        $list = get_theme_option('hotest');
        $data = [];
        if(trim($list)) {
            $lists = preg_split('/[\n\r]+/', $list);
            foreach ($lists as $list) {
                if (trim($list)) {
                    $list = explode('|', $list);
                    [$label, $relation, $field, $val, $sortKey, $alg, $limit] = array_merge($list, ['Phim hot', '', 'type', 'series', 'view_total', 'desc', 8]);
                    try {
                        $data[] = [
                            'label' => $label,
                            'data' => \Ophim\Core\Models\Movie::when($relation, function ($query) use ($relation, $field, $val) {
                                $query->whereHas($relation, function ($rel) use ($field, $val) {
                                    $rel->where($field, $val);
                                });
                            })
                            ->when(!$relation, function ($query) use ($field, $val) {
                                $query->where($field, $val);
                            })
                            ->orderBy($sortKey, $alg)
                            ->limit($limit)
                            ->get(),
                        ];
                    } catch (\Exception $e) {
                        // Fallback if error
                    }
                }
            }
        }

        // Fallback if no hot movies configured
        if (empty($data)) {
            $data[] = [
                'label' => 'Phim hot',
                'data' => \Ophim\Core\Models\Movie::published()
                    ->orderBy('view_total', 'desc')
                    ->limit(8)
                    ->get()
            ];
        }

        return $data;
    });

    // Get menu tabs configuration
    $menuTabs = Cache::remember('site.menu.tabs', setting('site_cache_ttl', 5 * 60), function () {
        $config = get_theme_option('menu_tabs');
        $tabs = [];

        if(trim($config)) {
            $lines = preg_split('/[\n\r]+/', $config);
            foreach ($lines as $line) {
                if (trim($line)) {
                    $parts = explode('|', $line);
                    if (count($parts) >= 5) {
                        [$tabId, $label, $icon, $type, $limit] = $parts;
                        $tabs[] = [
                            'id' => trim($tabId),
                            'label' => trim($label),
                            'icon' => trim($icon),
                            'type' => trim($type),
                            'limit' => (int)trim($limit)
                        ];
                    }
                }
            }
        }

        // Fallback default tabs if no config
        if (empty($tabs)) {
            $tabs = [
                ['id' => 'categories', 'label' => 'Thể Loại', 'icon' => 'fas fa-th-large', 'type' => 'categories', 'limit' => 16],
                ['id' => 'hot', 'label' => 'Phim Hot', 'icon' => 'fas fa-fire', 'type' => 'hot', 'limit' => 10],
                ['id' => 'regions', 'label' => 'Quốc Gia', 'icon' => 'fas fa-globe', 'type' => 'regions', 'limit' => 16],
                ['id' => 'types', 'label' => 'Định Dạng', 'icon' => 'fas fa-film', 'type' => 'types', 'limit' => 4]
            ];
        }

        return $tabs;
    });
@endphp

    <!-- Featured Movies Section -->
<div class="bg-theme-secondary py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Featured Series -->
            <div class="bg-theme-card rounded-lg overflow-hidden shadow-theme-secondary">
                <div class="relative">
                    <img src=""
                         alt="Phim Bộ" class="w-full h-48 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="bg-red-500 text-theme-inverse text-xs px-2 py-1 rounded-full font-bold">
                            Bộ
                        </span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-theme-primary truncate mb-1" title="Phim Bộ Nổi Bật">
                        Phim Bộ Nổi Bật
                    </h3>
                    <p class="text-theme-tertiary text-xs">Khám phá những bộ phim truyền hình hay nhất</p>
                </div>
            </div>

            <!-- Featured Movies -->
            <div class="bg-theme-card rounded-lg overflow-hidden shadow-theme-secondary">
                <div class="relative">
                    <img src=""
                         alt="Phim Lẻ" class="w-full h-48 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="bg-blue-500 text-theme-inverse text-xs px-2 py-1 rounded-full font-bold">
                            Lẻ
                        </span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-theme-primary truncate mb-1" title="Phim Lẻ Nổi Bật">
                        Phim Lẻ Nổi Bật
                    </h3>
                    <p class="text-theme-tertiary text-xs">Những bộ phim điện ảnh đáng xem nhất</p>
                </div>
            </div>

            <!-- Animated Movies -->
            <div class="bg-theme-card rounded-lg overflow-hidden shadow-theme-secondary">
                <div class="relative">
                    <img src=""
                         alt="Hoạt Hình" class="w-full h-48 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="bg-yellow-500 text-theme-inverse text-xs px-2 py-1 rounded-full font-bold">
                            Anime
                        </span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-theme-primary truncate mb-1" title="Hoạt Hình Nổi Bật">
                        Hoạt Hình Nổi Bật
                    </h3>
                    <p class="text-theme-tertiary text-xs">Thế giới hoạt hình đầy màu sắc</p>
                </div>
            </div>

            <!-- TV Shows -->
            <div class="bg-theme-card rounded-lg overflow-hidden shadow-theme-secondary">
                <div class="relative">
                    <img src=""
                         alt="TV Show" class="w-full h-48 object-cover">
                    <div class="absolute top-2 left-2">
                        <span class="bg-purple-500 text-theme-inverse text-xs px-2 py-1 rounded-full font-bold">
                            Show
                        </span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-theme-primary truncate mb-1" title="TV Show Nổi Bật">
                        TV Show Nổi Bật
                    </h3>
                    <p class="text-theme-tertiary text-xs">Các chương trình truyền hình hấp dẫn</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .menu-section {
        @apply bg-theme-menu text-theme-primary;
    }

    .menu-section::before {
        content: '';
        @apply absolute inset-0 bg-gradient-to-r from-theme-primary/5 to-transparent;
    }

    .menu-section .overflow-x-auto::-webkit-scrollbar {
        @apply h-1;
    }

    .menu-section .overflow-x-auto::-webkit-scrollbar-track {
        @apply bg-theme-secondary rounded-full;
    }

    .menu-section .overflow-x-auto::-webkit-scrollbar-thumb {
        @apply bg-theme-tertiary rounded-full;
    }

    .menu-section .overflow-x-auto::-webkit-scrollbar-thumb:hover {
        @apply bg-main;
    }

    .menu-section a {
        @apply text-theme-secondary hover:text-theme-primary;
    }

    .menu-section a:hover {
        @apply border-main;
    }

    .menu-section a::after {
        content: '';
        @apply absolute bottom-0 left-0 w-0 h-0.5 bg-main transition-all duration-300;
    }

    .menu-section a:hover::after {
        @apply w-full;
    }

    .menu-section .text-main {
        @apply text-main;
    }

    @media (max-width: 768px) {
        .menu-section {
            @apply py-4;
        }

        .menu-section .space-x-6 > * + * {
            @apply ml-4;
        }

        .menu-section .space-x-4 > * + * {
            @apply ml-3;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', function () {
                const targetTab = this.getAttribute('data-tab');

                // Remove active class from all buttons and panes
                tabBtns.forEach(b => b.classList.remove('active'));
                tabPanes.forEach(p => p.classList.remove('active', 'hidden'));

                // Add active class to clicked button
                this.classList.add('active');

                // Show target pane
                const targetPane = document.getElementById(targetTab);
                if (targetPane) {
                    targetPane.classList.add('active');
                }

                // Hide other panes
                tabPanes.forEach(p => {
                    if (p.id !== targetTab) {
                        p.classList.add('hidden');
                    }
                });
            });
        });
    });
</script>
