<!-- Desktop Search -->
<div class="hidden lg:block">
    <div class="relative" data-advanced-search data-api-url="{{ route('api.search') }}" data-popular-url="{{ route('api.search.popular') }}">
        <form method="GET" action="{{ route('search') }}" class="flex items-center">
            <div class="relative">
                <input type="text"
                       name="keyword"
                       value="{{ request('keyword', '') }}"
                       placeholder="Tìm kiếm..."
                       class="w-64 bg-white/10 text-white placeholder-white/70 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-main focus:bg-white/20 border border-white/20"
                       autocomplete="off">
                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-main transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Mobile Search Button -->
<div class="lg:hidden">
    <button onclick="openMobileSearch()"
            class="text-white hover:text-main text-xl p-2 hover:bg-white/10 rounded-lg transition-all duration-300"
            title="Tìm kiếm">
        <i class="fas fa-search"></i>
    </button>
</div>
