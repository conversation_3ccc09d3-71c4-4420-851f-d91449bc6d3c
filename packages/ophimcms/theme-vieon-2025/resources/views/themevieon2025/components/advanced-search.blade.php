{{-- Advanced Search Component - Vanilla JavaScript Version --}}
@php
    $searchKeyword = request('search', '');
@endphp

<!-- Desktop Search -->
<div class="hidden lg:block">
    <div class="relative" data-advanced-search data-api-url="{{ route('api.search') }}" data-popular-url="{{ route('api.search.popular') }}">
        <form method="GET" action="{{ route('search') }}" class="flex items-center">
            <div class="relative flex-1">
                <input type="text" 
                       name="keyword" 
                       value="{{ $searchKeyword }}"
                       placeholder="T<PERSON><PERSON> kiếm phim, di<PERSON><PERSON> vi<PERSON>n, đ<PERSON><PERSON> diễn..."
                       class="w-full bg-theme-dropdown text-theme-primary placeholder-theme-tertiary rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-main border border-theme-primary"
                       autocomplete="off">
                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-theme-tertiary hover:text-main transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Mobile Search Button -->
<div class="lg:hidden">
    <button onclick="openMobileSearch()" 
            class="p-2 text-theme-tertiary hover:text-main transition-colors">
        <i class="fas fa-search text-xl"></i>
    </button>
</div>

<!-- Mobile Search Overlay -->
<div id="mobileSearchOverlay" class="fixed inset-0 bg-black/80 z-[9999] lg:hidden hidden">
    <div class="flex flex-col h-full">
        <!-- Header -->
        <div class="flex items-center justify-between p-4 bg-theme-secondary border-b border-theme-primary">
            <h3 class="text-lg font-semibold text-theme-primary">Tìm kiếm phim</h3>
            <button onclick="closeMobileSearch()" class="p-2 text-theme-tertiary hover:text-theme-primary">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Search Input -->
        <div class="p-4 bg-theme-secondary border-b border-theme-primary">
            <div class="relative" data-advanced-search data-api-url="{{ route('api.search') }}" data-popular-url="{{ route('api.search.popular') }}">
                <form method="GET" action="{{ route('search') }}" class="flex items-center">
                    <div class="relative flex-1">
                        <input type="text" 
                               name="keyword" 
                               placeholder="Nhập tên phim bạn muốn tìm..."
                               class="w-full bg-theme-dropdown text-theme-primary placeholder-theme-tertiary rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-main border border-theme-primary"
                               autocomplete="off">
                        <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-theme-tertiary hover:text-main transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Search Results Area -->
        <div class="flex-1 overflow-hidden bg-theme-primary">
            <!-- Results will be populated by JavaScript -->
        </div>
    </div>
</div>

<!-- Quick Search Bar (for search page) -->
@if(request()->routeIs('search'))
<div class="bg-theme-secondary rounded-lg p-4 mb-6">
    <div class="relative" data-advanced-search data-api-url="{{ route('api.search') }}" data-popular-url="{{ route('api.search.popular') }}">
        <form method="GET" action="{{ route('search') }}" class="flex gap-3">
            <div class="flex-1 relative">
                <input type="text" 
                       name="keyword" 
                       value="{{ request('keyword') }}"
                       placeholder="Nhập tên phim, diễn viên, đạo diễn..."
                       class="w-full px-4 py-3 bg-theme-dropdown border border-theme-primary rounded-lg text-theme-primary placeholder-theme-tertiary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent">
            </div>
            <button type="submit" 
                    class="px-6 py-3 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                <i class="fas fa-search"></i>
                <span class="hidden sm:inline ml-2">Tìm kiếm</span>
            </button>
        </form>
    </div>
</div>
@endif

<script>
// Mobile search functions
function openMobileSearch() {
    const overlay = document.getElementById('mobileSearchOverlay');
    if (overlay) {
        overlay.classList.remove('hidden');
        // Focus on input after animation
        setTimeout(() => {
            const input = overlay.querySelector('input[name="keyword"]');
            if (input) input.focus();
        }, 100);
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }
}

function closeMobileSearch() {
    const overlay = document.getElementById('mobileSearchOverlay');
    if (overlay) {
        overlay.classList.add('hidden');
        document.body.style.overflow = '';
    }
}

// Close mobile search on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeMobileSearch();
    }
});

// Close mobile search when clicking overlay
document.getElementById('mobileSearchOverlay')?.addEventListener('click', function(e) {
    if (e.target === this) {
        closeMobileSearch();
    }
});

// Initialize advanced search when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Load advanced search script if not already loaded
    if (typeof AdvancedSearch === 'undefined') {
        const script = document.createElement('script');
        script.src = '{{ asset("themes/vieon-2025/js/advanced-search.js") }}';
        script.onload = function() {
            console.log('✅ Advanced Search loaded successfully');
        };
        script.onerror = function() {
            console.error('❌ Failed to load Advanced Search');
        };
        document.head.appendChild(script);
    }
});
</script>

<style>
/* Advanced Search Dropdown Styles */
.advanced-search-dropdown {
    max-width: 100%;
    min-width: 300px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-top: none;
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestion-item {
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    transform: translateX(2px);
}

.suggestion-item img {
    transition: transform 0.2s ease;
}

.suggestion-item:hover img {
    transform: scale(1.05);
}

/* Loading indicator */
.search-loading {
    z-index: 10;
}

/* Mobile search overlay */
#mobileSearchOverlay {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Section headers */
.section-header {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Scrollbar styling for dropdown */
.dropdown-content::-webkit-scrollbar {
    width: 6px;
}

.dropdown-content::-webkit-scrollbar-track {
    background: transparent;
}

.dropdown-content::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .advanced-search-dropdown {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .advanced-search-dropdown {
        min-width: 280px;
        max-height: 70vh;
    }
    
    .suggestion-item {
        padding: 0.75rem;
    }
    
    .suggestion-item img {
        width: 2.5rem;
        height: 3.5rem;
    }
}

/* Focus states */
input[name="keyword"]:focus + .advanced-search-dropdown {
    border-color: var(--main-color, #00ff66);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .advanced-search-dropdown {
        border-width: 2px;
    }
    
    .suggestion-item:hover {
        outline: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .advanced-search-dropdown,
    .suggestion-item,
    .suggestion-item img {
        animation: none;
        transition: none;
    }
    
    .suggestion-item:hover {
        transform: none;
    }
    
    .suggestion-item:hover img {
        transform: none;
    }
}
</style>
