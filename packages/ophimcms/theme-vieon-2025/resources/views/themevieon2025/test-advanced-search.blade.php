@extends('themevieon2025::layouts.app')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <h1 class="text-3xl font-bold text-gray-900 mb-8">Advanced Search System Test</h1>
                
                <!-- Test Advanced Search Component -->
                <div class="space-y-8">
                    
                    <!-- Test 1: Basic Search Input -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 1: Basic Search Input</h2>
                        <p class="text-gray-600 mb-4">Type to test real-time suggestions (minimum 2 characters)</p>
                        <div class="relative max-w-md" data-advanced-search data-api-url="{{ route('api.search') }}" data-popular-url="{{ route('api.search.popular') }}">
                            <form method="GET" action="{{ route('search') }}" class="flex items-center">
                                <div class="relative flex-1">
                                    <input type="text" 
                                           name="keyword" 
                                           placeholder="Tìm kiếm phim..."
                                           class="w-full bg-gray-50 text-gray-900 placeholder-gray-500 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-300"
                                           autocomplete="off">
                                    <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-500 transition-colors">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Test 2: Search History -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 2: Search History</h2>
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p>• Focus on empty search input to see history/popular searches</p>
                            <p>• Search for something to add to history</p>
                            <p>• Click the X button to remove items from history</p>
                        </div>
                        <div class="flex gap-2 mb-4">
                            <button onclick="clearSearchHistory()" class="px-3 py-1 bg-red-600 text-white rounded text-sm">
                                Clear History
                            </button>
                            <button onclick="addTestHistory()" class="px-3 py-1 bg-blue-600 text-white rounded text-sm">
                                Add Test History
                            </button>
                        </div>
                    </div>
                    
                    <!-- Test 3: Keyboard Navigation -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 3: Keyboard Navigation</h2>
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p>• Type to get suggestions, then use arrow keys to navigate</p>
                            <p>• Press Enter to select highlighted item</p>
                            <p>• Press Escape to close dropdown</p>
                            <p>• Test with both search results and history items</p>
                        </div>
                        <div class="relative max-w-md" data-advanced-search>
                            <form method="GET" action="{{ route('search') }}">
                                <input type="text" 
                                       name="keyword" 
                                       placeholder="Test keyboard navigation..."
                                       class="w-full bg-gray-50 text-gray-900 placeholder-gray-500 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-300">
                            </form>
                        </div>
                    </div>
                    
                    <!-- Test 4: Mobile Responsive -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 4: Mobile Responsive</h2>
                        <p class="text-gray-600 mb-4">Resize window or test on mobile device</p>
                        <div class="bg-gray-100 p-4 rounded" style="max-width: 320px;">
                            <p class="text-sm mb-2">Mobile simulation container</p>
                            <div class="relative" data-advanced-search>
                                <form method="GET" action="{{ route('search') }}">
                                    <input type="text" 
                                           name="keyword" 
                                           placeholder="Mobile search..."
                                           class="w-full bg-white text-gray-900 placeholder-gray-500 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-300">
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Test 5: API Endpoints -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 5: API Endpoints</h2>
                        <p class="text-gray-600 mb-4">Test API endpoints manually</p>
                        <div class="space-x-2 mb-4">
                            <button onclick="testSearchAPI()" class="px-3 py-1 bg-green-600 text-white rounded text-sm">
                                Test Search API
                            </button>
                            <button onclick="testPopularAPI()" class="px-3 py-1 bg-purple-600 text-white rounded text-sm">
                                Test Popular API
                            </button>
                            <button onclick="testEnhancedAPI()" class="px-3 py-1 bg-orange-600 text-white rounded text-sm">
                                Test Enhanced API
                            </button>
                        </div>
                        <div id="apiResults" class="bg-gray-50 p-4 rounded text-sm font-mono max-h-64 overflow-y-auto hidden">
                            <!-- API results will be displayed here -->
                        </div>
                    </div>
                    
                    <!-- Test 6: Performance -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 6: Performance & Debouncing</h2>
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p>• Type rapidly to test debouncing (should not make excessive API calls)</p>
                            <p>• Check browser console for API call logs</p>
                            <p>• Test with slow network (throttle in DevTools)</p>
                        </div>
                        <div class="relative max-w-md" data-advanced-search data-debounce-delay="500">
                            <form method="GET" action="{{ route('search') }}">
                                <input type="text" 
                                       name="keyword" 
                                       placeholder="Type rapidly to test debouncing..."
                                       class="w-full bg-gray-50 text-gray-900 placeholder-gray-500 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-300">
                            </form>
                        </div>
                        <div class="mt-2">
                            <span class="text-xs text-gray-500">API calls count: <span id="apiCallCount">0</span></span>
                        </div>
                    </div>
                    
                    <!-- Debug Info -->
                    <div class="border p-6 rounded-lg bg-gray-50">
                        <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
                        <div class="space-y-2 text-sm">
                            <p><strong>User Status:</strong> {{ Auth::check() ? 'Authenticated (' . Auth::user()->name . ')' : 'Guest' }}</p>
                            <p><strong>Search API:</strong> <span class="font-mono">{{ route('api.search') }}</span></p>
                            <p><strong>Popular API:</strong> <span class="font-mono">{{ route('api.search.popular') }}</span></p>
                            <p><strong>Enhanced API:</strong> <span class="font-mono">{{ route('api.search.enhanced') }}</span></p>
                            <p><strong>JavaScript Status:</strong> <span id="js-status">Loading...</span></p>
                            <p><strong>AdvancedSearch Status:</strong> <span id="advanced-search-status">Loading...</span></p>
                            <p><strong>Search History:</strong> <span id="history-status">Loading...</span></p>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
    <script src="{{ asset('themes/vieon-2025/js/advanced-search.js') }}"></script>
    <script>
        let apiCallCount = 0;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Update debug info
            document.getElementById('js-status').textContent = 'Loaded ✓';
            
            // Check if AdvancedSearch class is available
            if (typeof AdvancedSearch !== 'undefined') {
                console.log('✅ AdvancedSearch class loaded successfully');
                document.getElementById('advanced-search-status').textContent = 'Loaded ✓';
                
                // Check search history
                try {
                    const history = localStorage.getItem('vieon_search_history');
                    const historyCount = history ? JSON.parse(history).length : 0;
                    document.getElementById('history-status').textContent = `${historyCount} items`;
                } catch (error) {
                    document.getElementById('history-status').textContent = 'Error loading';
                }
            } else {
                console.error('❌ AdvancedSearch class not found');
                document.getElementById('advanced-search-status').textContent = 'Error: Class not loaded ❌';
            }
            
            // Monitor API calls
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                if (args[0].includes('/api/search')) {
                    apiCallCount++;
                    document.getElementById('apiCallCount').textContent = apiCallCount;
                    console.log(`API Call #${apiCallCount}:`, args[0]);
                }
                return originalFetch.apply(this, args);
            };
        });
        
        // Test functions
        function clearSearchHistory() {
            localStorage.removeItem('vieon_search_history');
            document.getElementById('history-status').textContent = '0 items';
            console.log('Search history cleared');
        }
        
        function addTestHistory() {
            const testItems = ['One Piece', 'Naruto', 'Attack on Titan'];
            try {
                localStorage.setItem('vieon_search_history', JSON.stringify(testItems));
                document.getElementById('history-status').textContent = `${testItems.length} items`;
                console.log('Test history added');
            } catch (error) {
                console.error('Error adding test history:', error);
            }
        }
        
        async function testSearchAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.classList.remove('hidden');
            resultsDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('{{ route("api.search") }}?keyword=one');
                const data = await response.json();
                resultsDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultsDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function testPopularAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.classList.remove('hidden');
            resultsDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('{{ route("api.search.popular") }}');
                const data = await response.json();
                resultsDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultsDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function testEnhancedAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.classList.remove('hidden');
            resultsDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('{{ route("api.search.enhanced") }}?keyword=one');
                const data = await response.json();
                resultsDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultsDiv.textContent = 'Error: ' + error.message;
            }
        }
    </script>
@endpush
