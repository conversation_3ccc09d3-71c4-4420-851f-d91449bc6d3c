<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="content-language" content="{{ config('app.locale') }}"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    {!! SEO::generate() !!}
    @php
        // Helper function to filter duplicate meta tags
        function filterDuplicateMetaTags($customMetaTags) {
            if (empty($customMetaTags)) {
                return '';
            }

            // Define patterns for duplicate meta tags
            $duplicatePatterns = [
                // Basic meta tags
                '/<meta\s+charset\s*=\s*["\']utf-8["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']viewport["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']robots["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']description["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']keywords["\'][^>]*>/i',

                // Open Graph tags
                '/<meta\s+property\s*=\s*["\']og:[^>]*>/i',

                // Twitter Card tags
                '/<meta\s+name\s*=\s*["\']twitter:[^>]*>/i',

                // Title and canonical
                '/<title[^>]*>.*?<\/title>/i',
                '/<link\s+rel\s*=\s*["\']canonical["\'][^>]*>/i',

                // Bot meta tags
                '/<meta\s+name\s*=\s*["\']revisit-after["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']googlebot["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']BingBOT["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']yahooBOT["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']slurp["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']msnbot["\'][^>]*>/i',

                // Additional SEO tags
                '/<meta\s+name\s*=\s*["\']author["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']generator["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']rating["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']distribution["\'][^>]*>/i',
                '/<meta\s+name\s*=\s*["\']classification["\'][^>]*>/i',

                // JSON-LD script tags
                '/<script\s+type\s*=\s*["\']application\/ld\+json["\'][^>]*>.*?<\/script>/is'
            ];

            // Remove duplicate meta tags
            foreach ($duplicatePatterns as $pattern) {
                $customMetaTags = preg_replace($pattern, '', $customMetaTags);
            }

            // Clean up whitespace
            $customMetaTags = preg_replace('/\n\s*\n/', "\n", trim($customMetaTags));
            $customMetaTags = preg_replace('/\s+/', ' ', $customMetaTags);
            $customMetaTags = preg_replace('/^\s*$/m', '', $customMetaTags);

            return trim($customMetaTags);
        }

        $customMetaTags = filterDuplicateMetaTags(get_theme_option('additional_meta'));
    @endphp
    {!! $customMetaTags !!}
    <meta property="fb:app_id" content="{{ setting('social_facebook_app_id') }}"/>
    <link rel="shortcut icon" href="{{ setting('site_meta_shortcut_icon') }}" type="image/png"/>
    {{-- Preconnect to external domains --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://unpkg.com">
    <link rel="preconnect" href="https://cdn.tailwindcss.com">

    {{-- DNS prefetch for performance --}}
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://unpkg.com">
    <link rel="dns-prefetch" href="https://cdn.tailwindcss.com">

    {{-- Preload critical resources --}}
    <link rel="preload" href="{{ asset('themes/vieon-2025/css/style.css') }}" as="style">
    <link rel="preload" href="https://cdn.tailwindcss.com" as="script">
    {{-- Critical fonts with preload --}}
    <link rel="preload"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
          as="style"
          onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload"
          href="https://fonts.googleapis.com/css2?family=UTM+Bebas&display=swap"
          as="style"
          onload="this.onload=null;this.rel='stylesheet'">

    {{-- Fallback for noscript --}}
    <noscript>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=UTM+Bebas&display=swap" rel="stylesheet">
    </noscript>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ asset('themes/vieon-2025/css/style.css') }}">
    <link rel="preload"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
          as="style"
          onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload"
          href="{{ asset('themes/vieon-2025/plugins/swiper/swiper-bundle.min.css') }}"
          as="style"
          onload="this.onload=null;this.rel='stylesheet'">

    {{-- Fallback for noscript --}}
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <link rel="stylesheet" href="{{ asset('themes/vieon-2025/plugins/swiper/swiper-bundle.min.css') }}"/>
    </noscript>
    <script defer
            src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"
            onerror="console.warn('Alpine.js failed to load, falling back to basic functionality')">
    </script>
    @stack('styles')
    {!! get_theme_option('additional_css') !!}
    {!! get_theme_option('additional_header_js') !!}
</head>

<body class="font-sans antialiased theme-transition" x-data="{ mobileMenuOpen: false } {!! get_theme_option('body_attributes', '') !!}">
@include('themevieon2025::components.loading')
<div class="min-h-screen">
    @php
        $headerFixed = $headerFixed ?? false;
    @endphp

    @include('themevieon2025::layouts._header', ['headerFixed' => $headerFixed])
    @include('themevieon2025::components.advanced-search')

    <main>
        @yield('content')
    </main>

    @include('themevieon2025::layouts._footer')
</div>

{!! get_theme_option('additional_body_js') !!}

{{-- Route URLs for JavaScript --}}
<script>
window.routes = {
    // Web Routes
    'logout': '{{ route('logout') }}',

    // API Routes
    'api.login': '{{ route('api.login') }}',
    'api.register': '{{ route('api.register') }}',
    'api.logout': '{{ route('api.logout') }}',
    'api.profile.show': '{{ route('api.profile.show') }}',
    'api.profile.update': '{{ route('api.profile.update') }}',
    'api.profile.destroy': '{{ route('api.profile.destroy') }}',
    'api.settings.show': '{{ route('api.settings.show') }}',
    'api.settings.update': '{{ route('api.settings.update') }}',
    'api.password.email': '{{ route('api.password.email') }}',
    'api.password.reset': '{{ route('api.password.reset') }}',
    'api.password.change': '{{ route('api.password.change') }}',
    'api.2fa.send': '{{ route('api.2fa.send') }}',
    'api.2fa.verify': '{{ route('api.2fa.verify') }}',
    'api.verification.verify': '{{ route('api.verification.verify', ['id' => ':id', 'hash' => ':hash']) }}',
    'api.verification.resend': '{{ route('api.verification.resend') }}',
    'api.comments.index': '{{ route('api.comments.index', ':movieId') }}',
    'api.comments.store': '{{ route('api.comments.store', ':movieId') }}',
    'api.comments.destroy': '{{ route('api.comments.destroy', ':id') }}',
    'api.ratings.index': '{{ route('api.ratings.index', ':movieId') }}',
    'api.ratings.store': '{{ route('api.ratings.store', ':movieId') }}',
    'api.ratings.destroy': '{{ route('api.ratings.destroy', ':movieId') }}',
    'api.user.dashboard': '{{ route('api.user.dashboard') }}',
    'api.user.favorites': '{{ route('api.user.favorites') }}',
    'api.user.history': '{{ route('api.user.history') }}',
    'api.user.watchlist': '{{ route('api.user.watchlist') }}',
    'api.user.recommendations': '{{ route('api.user.recommendations') }}',
    'api.user.favorites.add': '{{ route('api.user.favorites.add') }}',
    'api.user.favorites.remove': '{{ route('api.user.favorites.remove', ':movieId') }}',
    'api.user.watchlist.add': '{{ route('api.user.watchlist.add') }}',
    'api.user.watchlist.remove': '{{ route('api.user.watchlist.remove', ':movieId') }}',
    'api.user.rate': '{{ route('api.user.rate', ':movieId') }}',
    'api.search': '{{ route('api.search') }}'
};

// Helper function to get route URL with parameters
window.route = function(name, params = {}) {
    let url = window.routes[name];
    if (!url) {
        console.error('Route not found:', name);
        return '';
    }

    // Replace parameters in URL
    for (let key in params) {
        url = url.replace(':' + key, params[key]);
    }

    return url;
};
</script>

{{-- Core JavaScript files - load in order --}}
<script src="{{ asset('themes/vieon-2025/plugins/swiper/swiper-bundle.min.js') }}"></script>
<script src="{{ asset('themes/vieon-2025/js/ajax-form.js') }}"></script>
<script src="{{ asset('themes/vieon-2025/js/user-api.js') }}"></script>
<script src="{{ asset('themes/vieon-2025/js/app.js') }}"></script>

{{-- Page-specific scripts --}}
@stack('scripts')
{!! get_theme_option('additional_footer_js') !!}

@include('themevieon2025::components.modal.auth')
@include('themevieon2025::components.notification')
</body>
</html>
