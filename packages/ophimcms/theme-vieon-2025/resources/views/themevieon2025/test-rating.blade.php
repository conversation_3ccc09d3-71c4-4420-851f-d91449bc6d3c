@extends('themevieon2025::layouts.app')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <h1 class="text-3xl font-bold text-gray-900 mb-8">Rating System Test</h1>
                
                <!-- Test Rating Component -->
                <div class="space-y-8">
                    
                    <!-- Test 1: Basic Rating (Readonly) -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 1: Readonly Rating Display</h2>
                        <p class="text-gray-600 mb-4">Current rating: 4.2/5 (125 votes)</p>
                        <div 
                            data-movie-rating
                            data-max-stars="5"
                            data-current-rating="4.2"
                            data-readonly="true"
                            data-show-hint="false"
                            class="movie-rating-container">
                        </div>
                    </div>
                    
                    <!-- Test 2: Interactive Rating (Authenticated) -->
                    @auth
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 2: Interactive Rating (Authenticated)</h2>
                        <p class="text-gray-600 mb-4">Click stars to rate (will submit to test endpoint)</p>
                        <div 
                            data-movie-rating
                            data-max-stars="5"
                            data-current-rating="0"
                            data-movie-slug="test-movie"
                            data-rating-url="/test-rating-endpoint"
                            data-readonly="false"
                            data-show-hint="true"
                            class="movie-rating-container">
                        </div>
                    </div>
                    @else
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 2: Interactive Rating (Not Authenticated)</h2>
                        <p class="text-gray-600 mb-4">
                            <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-800">Login</a> 
                            to test interactive rating
                        </p>
                        <div 
                            data-movie-rating
                            data-max-stars="5"
                            data-current-rating="3.5"
                            data-readonly="true"
                            data-show-hint="false"
                            class="movie-rating-container">
                        </div>
                    </div>
                    @endauth
                    
                    <!-- Test 3: Different Star Counts -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 3: Different Star Counts</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm text-gray-600 mb-2">3 Stars (Rating: 2.5)</p>
                                <div 
                                    data-movie-rating
                                    data-max-stars="3"
                                    data-current-rating="2.5"
                                    data-readonly="true"
                                    data-show-hint="false"
                                    class="movie-rating-container">
                                </div>
                            </div>
                            
                            <div>
                                <p class="text-sm text-gray-600 mb-2">10 Stars (Rating: 7.8)</p>
                                <div 
                                    data-movie-rating
                                    data-max-stars="10"
                                    data-current-rating="7.8"
                                    data-readonly="true"
                                    data-show-hint="false"
                                    class="movie-rating-container">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Test 4: Mobile Responsive Test -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 4: Mobile Responsive</h2>
                        <p class="text-gray-600 mb-4">Resize window to test mobile layout</p>
                        <div class="bg-gray-100 p-4 rounded" style="max-width: 320px;">
                            <div 
                                data-movie-rating
                                data-max-stars="5"
                                data-current-rating="3.0"
                                data-readonly="false"
                                data-show-hint="true"
                                class="movie-rating-container">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Debug Info -->
                    <div class="border p-6 rounded-lg bg-gray-50">
                        <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
                        <div class="space-y-2 text-sm">
                            <p><strong>User Status:</strong> {{ Auth::check() ? 'Authenticated (' . Auth::user()->name . ')' : 'Guest' }}</p>
                            <p><strong>CSRF Token:</strong> <span class="font-mono">{{ csrf_token() }}</span></p>
                            <p><strong>Current URL:</strong> <span class="font-mono">{{ url()->current() }}</span></p>
                            <p><strong>JavaScript Status:</strong> <span id="js-status">Loading...</span></p>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
    <script src="{{ asset('themes/vieon-2025/js/rating.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update debug info
            document.getElementById('js-status').textContent = 'Loaded ✓';
            
            // Check if MovieRating class is available
            if (typeof MovieRating !== 'undefined') {
                console.log('✅ MovieRating class loaded successfully');
                
                // Count rating components
                const ratingContainers = document.querySelectorAll('[data-movie-rating]');
                console.log(`✅ Found ${ratingContainers.length} rating containers`);
                
                // Test manual initialization
                ratingContainers.forEach((container, index) => {
                    console.log(`✅ Rating container ${index + 1} initialized`);
                });
            } else {
                console.error('❌ MovieRating class not found');
                document.getElementById('js-status').textContent = 'Error: MovieRating not loaded ❌';
            }
        });
    </script>
@endpush
