@extends('themevieon2025::layouts.app')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <div class="flex items-center justify-between mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Development Test Suite</h1>
                        <p class="text-gray-600 mt-2">Phase 3: JavaScript AJAX Enhancement - Test Pages</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            Development Mode
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <i class="fas fa-code mr-1"></i>
                            Vieon 2025 Theme
                        </span>
                    </div>
                </div>
                
                <!-- Test Categories -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    
                    <!-- Movie Rating System -->
                    <div class="bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-star text-white"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-semibold text-gray-900">Movie Rating System</h3>
                                    <p class="text-sm text-gray-600">Interactive star rating with AJAX</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ✓ Completed
                            </span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p>• 5-star rating system với vanilla JavaScript</p>
                            <p>• Real-time AJAX submission</p>
                            <p>• Loading states và error handling</p>
                            <p>• Mobile responsive design</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('test.rating') }}" 
                               class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white text-center py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-play mr-1"></i>
                                Test Rating
                            </a>
                        </div>
                    </div>
                    
                    <!-- Episode Reporting System -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 border border-red-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-flag text-white"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-semibold text-gray-900">Episode Reporting</h3>
                                    <p class="text-sm text-gray-600">Modal form với AJAX submission</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ✓ Completed
                            </span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p>• Modal interface với vanilla JavaScript</p>
                            <p>• Form validation và error handling</p>
                            <p>• Success/error notifications</p>
                            <p>• Keyboard navigation support</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('test.episode.reporting') }}" 
                               class="flex-1 bg-red-500 hover:bg-red-600 text-white text-center py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-play mr-1"></i>
                                Test Reporting
                            </a>
                        </div>
                    </div>
                    
                    <!-- Advanced Search System -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-search text-white"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-semibold text-gray-900">Advanced Search</h3>
                                    <p class="text-sm text-gray-600">Autocomplete với search history</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ✓ Completed
                            </span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p>• Real-time autocomplete suggestions</p>
                            <p>• Search history với localStorage</p>
                            <p>• Keyboard navigation</p>
                            <p>• Debounced input handling</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('test.advanced.search') }}" 
                               class="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-center py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-play mr-1"></i>
                                Test Search
                            </a>
                        </div>
                    </div>
                    
                    <!-- AJAX Test Page -->
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-bolt text-white"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-semibold text-gray-900">AJAX Test Page</h3>
                                    <p class="text-sm text-gray-600">General AJAX functionality tests</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Available
                            </span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p>• Basic AJAX form submissions</p>
                            <p>• Error handling tests</p>
                            <p>• Loading state demonstrations</p>
                            <p>• General debugging tools</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('test.ajax') }}" 
                               class="flex-1 bg-purple-500 hover:bg-purple-600 text-white text-center py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-play mr-1"></i>
                                Test AJAX
                            </a>
                        </div>
                    </div>
                    
                    <!-- Coming Soon - Infinite Scroll -->
                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-6 opacity-75">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-arrows-alt-v text-white"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-semibold text-gray-600">Infinite Scroll</h3>
                                    <p class="text-sm text-gray-500">Pagination với infinite scroll</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                Coming Soon
                            </span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-500 mb-4">
                            <p>• Infinite scroll pagination</p>
                            <p>• Loading states</p>
                            <p>• Performance optimization</p>
                            <p>• Mobile touch support</p>
                        </div>
                        <div class="flex space-x-2">
                            <button disabled 
                                    class="flex-1 bg-gray-300 text-gray-500 text-center py-2 px-4 rounded-lg cursor-not-allowed">
                                <i class="fas fa-clock mr-1"></i>
                                In Development
                            </button>
                        </div>
                    </div>
                    
                    <!-- Coming Soon - Filter & Sort -->
                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-6 opacity-75">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-filter text-white"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-semibold text-gray-600">Filter & Sort AJAX</h3>
                                    <p class="text-sm text-gray-500">Dynamic filtering và sorting</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                Coming Soon
                            </span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-500 mb-4">
                            <p>• Dynamic content filtering</p>
                            <p>• AJAX-based sorting</p>
                            <p>• URL state management</p>
                            <p>• Performance optimization</p>
                        </div>
                        <div class="flex space-x-2">
                            <button disabled 
                                    class="flex-1 bg-gray-300 text-gray-500 text-center py-2 px-4 rounded-lg cursor-not-allowed">
                                <i class="fas fa-clock mr-1"></i>
                                In Development
                            </button>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Quick Links -->
                <div class="mt-8 bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-link mr-2"></i>
                        Quick Links
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <a href="{{ route('home') }}" class="flex items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-home text-blue-500 mr-2"></i>
                            <span class="text-sm font-medium">Home Page</span>
                        </a>
                        <a href="{{ route('search') }}" class="flex items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-search text-green-500 mr-2"></i>
                            <span class="text-sm font-medium">Search Page</span>
                        </a>
                        <a href="/admin" class="flex items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-cog text-purple-500 mr-2"></i>
                            <span class="text-sm font-medium">Admin Panel</span>
                        </a>
                        <a href="https://github.com" target="_blank" class="flex items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fab fa-github text-gray-700 mr-2"></i>
                            <span class="text-sm font-medium">Documentation</span>
                        </a>
                    </div>
                </div>
                
                <!-- Development Info -->
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-blue-900">Development Information</h4>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>• This test suite is only visible in development environment</p>
                                <p>• All test pages include comprehensive debugging information</p>
                                <p>• Check browser console for detailed logs and error messages</p>
                                <p>• Test pages are responsive and work on mobile devices</p>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Development Test Suite loaded');
    console.log('📊 Available test pages:', {
        'Movie Rating': '{{ route("test.rating") }}',
        'Episode Reporting': '{{ route("test.episode.reporting") }}',
        'Advanced Search': '{{ route("test.advanced.search") }}',
        'AJAX Tests': '{{ route("test.ajax") }}'
    });
});
</script>
@endpush
