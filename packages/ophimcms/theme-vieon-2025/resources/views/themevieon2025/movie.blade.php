@extends('themevieon2025::layouts.app')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Movie Info -->
                    <div class="md:col-span-1">
                        <img src="{{ $movie->poster_url }}" alt="{{ $movie->name }}" class="w-full rounded-lg shadow-lg">

                        <div class="mt-4">
                            <h1 class="text-2xl font-bold text-gray-900">{{ $movie->name }}</h1>
                            <p class="text-gray-600">{{ $movie->origin_name }}</p>

                            <div class="mt-4 space-y-2">
                                <div class="flex items-center">
                                    <span class="text-gray-600 w-24">Thể loại:</span>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($movie->categories as $category)
                                        <a href="{{ route('categories.movies.index', $category->slug) }}" class="text-sm text-blue-600 hover:text-blue-800">
                                            {{ $category->name }}
                                        </a>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="flex items-center">
                                    <span class="text-gray-600 w-24">Quốc gia:</span>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($movie->regions as $region)
                                        <span class="text-sm text-gray-600">{{ $region->name }}</span>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="flex items-center">
                                    <span class="text-gray-600 w-24">Năm:</span>
                                    <span class="text-sm text-gray-600">{{ $movie->publish_year }}</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="text-gray-600 w-24">Chất lượng:</span>
                                    <span class="text-sm text-gray-600">{{ $movie->quality }}</span>
                                </div>
                            </div>

                            <!-- Rating Section -->
                            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">Đánh giá phim</h3>

                                <!-- Current Rating Display -->
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-2xl font-bold text-yellow-500 rating-average">
                                            {{ number_format($movie->getRatingStar(), 1) }}
                                        </span>
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                <span class="text-lg {{ $i <= $movie->getRatingStar() ? 'text-yellow-400' : 'text-gray-300' }}">★</span>
                                            @endfor
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        (<span class="rating-count">{{ $movie->getRatingCount() }}</span> đánh giá)
                                    </div>
                                </div>

                                <!-- User Rating -->
                                @auth
                                    <div class="border-t pt-4">
                                        <p class="text-sm text-gray-600 mb-3">Đánh giá của bạn:</p>
                                        <div
                                            data-movie-rating
                                            data-max-stars="5"
                                            data-current-rating="{{ $movie->getRatingStar() }}"
                                            data-movie-slug="{{ $movie->slug }}"
                                            data-rating-url="{{ route('movie.rating', $movie->slug) }}"
                                            data-readonly="false"
                                            data-show-hint="true"
                                            class="movie-rating-container">
                                        </div>
                                    </div>
                                @else
                                    <div class="border-t pt-4">
                                        <p class="text-sm text-gray-600 mb-3">
                                            <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-800">Đăng nhập</a>
                                            để đánh giá phim này
                                        </p>
                                        <div
                                            data-movie-rating
                                            data-max-stars="5"
                                            data-current-rating="{{ $movie->getRatingStar() }}"
                                            data-readonly="true"
                                            data-show-hint="false"
                                            class="movie-rating-container">
                                        </div>
                                    </div>
                                @endauth
                            </div>
                        </div>
                    </div>

                    <!-- Episodes -->
                    <div class="md:col-span-2">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Danh sách tập</h2>

                        <div class="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-2">
                            @foreach($movie->episodes as $episode)
                            <a href="{{ route('episodes.show', [$movie->slug, $episode->slug]) }}"
                               class="inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                {{ $episode->name }}
                            </a>
                            @endforeach
                        </div>

                        <!-- Description -->
                        <div class="mt-8">
                            <h2 class="text-xl font-semibold text-gray-900 mb-4">Nội dung phim</h2>
                            <div class="prose max-w-none">
                                {!! $movie->content !!}
                            </div>
                        </div>

                        <!-- Related Movies -->
                        @if($relatedMovies->count() > 0)
                        <div class="mt-8">
                            <h2 class="text-xl font-semibold text-gray-900 mb-4">Phim liên quan</h2>
                            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                                @foreach($relatedMovies as $relatedMovie)
                                <div class="bg-white rounded-lg shadow overflow-hidden">
                                    <a href="{{ route('movies.show', $relatedMovie->slug) }}">
                                        <img src="{{ $relatedMovie->thumb_url }}" alt="{{ $relatedMovie->name }}" class="w-full h-32 object-cover">
                                        <div class="p-2">
                                            <h3 class="text-sm font-medium text-gray-900 truncate">{{ $relatedMovie->name }}</h3>
                                            <p class="text-xs text-gray-500">{{ $relatedMovie->episode_current }}</p>
                                        </div>
                                    </a>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
    <script src="{{ asset('themes/vieon-2025/js/rating.js') }}"></script>
@endpush
