@extends('themevieon2025::layouts.user')

@section('title', '<PERSON><PERSON><PERSON> luận của tôi - ' . config('app.name') . ' 2025')

@section('user-content')
<div class="text-center mb-8">
    <h2 class="text-3xl font-bold text-white mb-2"><PERSON><PERSON><PERSON> luận của tôi</h2>
    <p class="text-gray-400">Quản lý tất cả bình luận bạn đã đăng</p>
</div>

        @if($comments->count())
            <div class="space-y-6">
                @foreach($comments as $comment)
                    <div class="user-card">
                        <div class="p-6">
                            <div class="flex items-start space-x-4">
                                <img src="{{ $comment->user->avatar ?? asset('themes/vieon-2025/images/default-avatar.png') }}"
                                     class="w-12 h-12 rounded-full object-cover flex-shrink-0">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center space-x-2">
                                            <span class="font-semibold text-white">{{ $comment->user->name }}</span>
                                            <span class="text-xs text-gray-400">{{ $comment->created_at->diffForHumans() }}</span>
                                        </div>
                                        @if(Auth::id() === $comment->user_id)
                                            <form method="POST" action="{{ route('api.comments.destroy', $comment->id) }}" class="inline ajax-form">
                                                @csrf @method('DELETE')
                                                <button type="submit"
                                                        class="text-red-400 hover:text-red-300 text-sm py-1 px-2 rounded hover:bg-red-500/10 transition-all duration-200 flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                    Xóa
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                    <div class="text-white mb-3 leading-relaxed">{{ $comment->content }}</div>

                                    @if($comment->movie)
                                        <div class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg">
                                            <img src="{{ $comment->movie->poster_url }}"
                                                 class="w-16 h-20 object-cover rounded">
                                            <div class="flex-1">
                                                <div class="font-medium text-white">{{ $comment->movie->name }}</div>
                                                <div class="text-sm text-gray-400">{{ $comment->movie->year ?? 'N/A' }}</div>
                                                <a href="{{ route('movies.show', $comment->movie->slug) }}"
                                                   class="text-main hover:text-main/80 text-sm transition-colors">
                                                    Xem phim →
                                                </a>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            @if($comment->children && $comment->children->count() > 0)
                                <div class="ml-16 mt-4 space-y-4">
                                    <h4 class="text-sm font-medium text-gray-400">Phản hồi:</h4>
                                    @include('themevieon2025::user.comments.index', ['comments' => $comment->children])
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            @if($comments->hasPages())
                <div class="mt-8 flex justify-center">
                    <div class="bg-gray-800/50 rounded-lg p-4">
                        {{ $comments->links() }}
                    </div>
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-800/50 mb-6">
                    <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Chưa có bình luận nào</h3>
                <p class="text-gray-400 mb-6">Bạn chưa đăng bình luận nào hoặc tất cả bình luận đã được xóa.</p>
                <a href="{{ route('home') }}"
                   class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 transition-all duration-200">
                    <i class="fas fa-compass mr-2"></i>
                    Khám phá phim mới
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
