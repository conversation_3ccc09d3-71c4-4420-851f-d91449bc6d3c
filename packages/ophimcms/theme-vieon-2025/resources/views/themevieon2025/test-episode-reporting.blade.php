@extends('themevieon2025::layouts.app')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <h1 class="text-3xl font-bold text-gray-900 mb-8">Episode Reporting System Test</h1>
                
                <!-- Test Episode Reporting Component -->
                <div class="space-y-8">
                    
                    <!-- Test 1: Basic Report Button -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 1: Report Button</h2>
                        <p class="text-gray-600 mb-4">Click the button to open the report modal</p>
                        <button 
                            onclick="EpisodeReporting.openModal()" 
                            class="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                            <i class="fas fa-flag"></i>
                            <span>Báo cáo tập phim</span>
                        </button>
                    </div>
                    
                    <!-- Test 2: Form Validation -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 2: Form Validation</h2>
                        <div class="space-y-2 text-sm text-gray-600">
                            <p>• Try submitting without filling required fields</p>
                            <p>• Try submitting with message less than 10 characters</p>
                            <p>• Try submitting with message more than 500 characters</p>
                            <p>• Test character counter functionality</p>
                        </div>
                    </div>
                    
                    <!-- Test 3: AJAX Submission -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 3: AJAX Submission</h2>
                        <div class="space-y-2 text-sm text-gray-600">
                            <p>• Fill form completely and submit</p>
                            <p>• Check loading states during submission</p>
                            <p>• Verify success/error notifications</p>
                            <p>• Test form reset after successful submission</p>
                        </div>
                    </div>
                    
                    <!-- Test 4: Modal Functionality -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 4: Modal Functionality</h2>
                        <div class="space-y-2 text-sm text-gray-600">
                            <p>• Test opening/closing modal with buttons</p>
                            <p>• Test closing modal with Escape key</p>
                            <p>• Test closing modal by clicking overlay</p>
                            <p>• Test modal animations and transitions</p>
                        </div>
                        <div class="mt-4 space-x-2">
                            <button 
                                onclick="EpisodeReporting.openModal()" 
                                class="px-3 py-1 bg-blue-600 text-white rounded text-sm">
                                Open Modal
                            </button>
                            <button 
                                onclick="EpisodeReporting.closeModal()" 
                                class="px-3 py-1 bg-gray-600 text-white rounded text-sm">
                                Close Modal
                            </button>
                        </div>
                    </div>
                    
                    <!-- Test 5: Mobile Responsive -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 5: Mobile Responsive</h2>
                        <p class="text-gray-600 mb-4">Resize window or test on mobile device</p>
                        <div class="bg-gray-100 p-4 rounded" style="max-width: 320px;">
                            <p class="text-sm mb-2">Mobile simulation container</p>
                            <button 
                                onclick="EpisodeReporting.openModal()" 
                                class="w-full flex items-center justify-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                <i class="fas fa-flag"></i>
                                <span>Báo cáo</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Test 6: Notification System -->
                    <div class="border p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Test 6: Notification System</h2>
                        <p class="text-gray-600 mb-4">Test notification display manually</p>
                        <div class="space-x-2">
                            <button 
                                onclick="EpisodeReporting.showNotification('success', 'Test Success', 'This is a success notification')" 
                                class="px-3 py-1 bg-green-600 text-white rounded text-sm">
                                Show Success
                            </button>
                            <button 
                                onclick="EpisodeReporting.showNotification('error', 'Test Error', 'This is an error notification')" 
                                class="px-3 py-1 bg-red-600 text-white rounded text-sm">
                                Show Error
                            </button>
                            <button 
                                onclick="EpisodeReporting.hideNotification()" 
                                class="px-3 py-1 bg-gray-600 text-white rounded text-sm">
                                Hide Notification
                            </button>
                        </div>
                    </div>
                    
                    <!-- Debug Info -->
                    <div class="border p-6 rounded-lg bg-gray-50">
                        <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
                        <div class="space-y-2 text-sm">
                            <p><strong>User Status:</strong> {{ Auth::check() ? 'Authenticated (' . Auth::user()->name . ')' : 'Guest' }}</p>
                            <p><strong>CSRF Token:</strong> <span class="font-mono">{{ csrf_token() }}</span></p>
                            <p><strong>Test Endpoint:</strong> <span class="font-mono">/test-episode-report</span></p>
                            <p><strong>JavaScript Status:</strong> <span id="js-status">Loading...</span></p>
                            <p><strong>EpisodeReporting Status:</strong> <span id="episode-reporting-status">Loading...</span></p>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Report Modal with Test Data -->
@php
    $testMovie = (object) [
        'name' => 'Test Movie Name',
        'origin_name' => 'Test Original Name',
        'slug' => 'test-movie',
        'getThumbUrl' => function() { return 'https://via.placeholder.com/300x400?text=Test+Movie'; }
    ];
    
    $testEpisode = (object) [
        'name' => 'Tập 1',
        'slug' => 'tap-1'
    ];
@endphp

<!-- Custom Report Modal for Testing -->
<div id="reportModal" class="fixed inset-0 z-50 overflow-y-auto hidden opacity-0 transition-opacity duration-300">
    <!-- Background Overlay -->
    <div class="fixed inset-0 bg-black bg-opacity-75 backdrop-blur-sm transition-opacity"
         onclick="EpisodeReporting.closeModal()"></div>

    <!-- Modal Container -->
    <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all scale-95"
             id="reportModalContent">

            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-flag mr-2 text-red-500"></i>
                    Báo cáo tập phim (Test)
                </h3>
                <button onclick="EpisodeReporting.closeModal()"
                        class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="reportForm"
                  class="ajax-form"
                  action="/test-episode-report"
                  method="POST">
                @csrf

                <div class="p-4 space-y-4">
                    <!-- Episode Info -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center gap-3">
                            <img src="https://via.placeholder.com/48x64?text=Test"
                                 alt="Test Movie"
                                 class="w-12 h-16 object-cover rounded">
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-gray-900 truncate">Test Movie Name</h4>
                                <p class="text-sm text-gray-600">Tập 1</p>
                                <p class="text-xs text-gray-500">Test Original Name</p>
                            </div>
                        </div>
                    </div>

                    <!-- Report Reason -->
                    <div>
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-exclamation-triangle mr-1 text-yellow-500"></i>
                            Lý do báo cáo
                        </label>
                        <select name="reason"
                                id="reason"
                                class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Chọn lý do...</option>
                            <option value="Link không hoạt động">Link không hoạt động</option>
                            <option value="Chất lượng kém">Chất lượng kém</option>
                            <option value="Không có phụ đề">Không có phụ đề</option>
                            <option value="Phụ đề sai">Phụ đề sai</option>
                            <option value="Âm thanh có vấn đề">Âm thanh có vấn đề</option>
                            <option value="Video bị lag">Video bị lag</option>
                            <option value="Quảng cáo quá nhiều">Quảng cáo quá nhiều</option>
                            <option value="Nội dung không phù hợp">Nội dung không phù hợp</option>
                            <option value="Khác">Khác</option>
                        </select>
                    </div>

                    <!-- Report Message -->
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-comment mr-1 text-blue-500"></i>
                            Mô tả chi tiết <span class="text-red-500">*</span>
                        </label>
                        <textarea name="message"
                                  id="message"
                                  rows="4"
                                  required
                                  maxlength="500"
                                  placeholder="Vui lòng mô tả chi tiết vấn đề bạn gặp phải..."
                                  class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
                        <div class="flex justify-between items-center mt-1">
                            <p class="text-xs text-gray-500">Tối đa 500 ký tự</p>
                            <span id="messageCounter" class="text-xs text-gray-500">0/500</span>
                        </div>
                    </div>

                    <!-- Info Box -->
                    <div class="bg-blue-50 rounded-lg p-3">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                            <div class="text-sm text-blue-700">
                                <p class="font-medium mb-1">Thông tin hữu ích:</p>
                                <ul class="text-xs space-y-1 text-blue-600">
                                    <li>• Đây là chế độ test - báo cáo sẽ không được lưu thực tế</li>
                                    <li>• Test tất cả tính năng validation và AJAX</li>
                                    <li>• Kiểm tra responsive design trên mobile</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex items-center justify-end gap-3 p-4 border-t border-gray-200 bg-gray-50">
                    <button type="button"
                            onclick="EpisodeReporting.closeModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
                        Hủy bỏ
                    </button>
                    <button type="submit"
                            id="submitReportBtn"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="submit-text">
                            <i class="fas fa-paper-plane mr-1"></i>
                            Gửi báo cáo
                        </span>
                        <span class="loading-text hidden">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Đang gửi...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Success/Error Messages Container -->
<div id="reportNotification" class="fixed top-4 right-4 z-60 hidden">
    <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
        <div class="flex items-start gap-3">
            <div id="notificationIcon" class="flex-shrink-0 mt-0.5">
                <!-- Icon will be inserted by JavaScript -->
            </div>
            <div class="flex-1 min-w-0">
                <p id="notificationTitle" class="text-sm font-medium text-gray-900">
                    <!-- Title will be inserted by JavaScript -->
                </p>
                <p id="notificationMessage" class="text-sm text-gray-600 mt-1">
                    <!-- Message will be inserted by JavaScript -->
                </p>
            </div>
            <button onclick="EpisodeReporting.hideNotification()"
                    class="flex-shrink-0 text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

@endsection

@push('scripts')
    <script src="{{ asset('themes/vieon-2025/js/episode-reporting.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update debug info
            document.getElementById('js-status').textContent = 'Loaded ✓';
            
            // Check if EpisodeReporting class is available
            if (typeof EpisodeReporting !== 'undefined') {
                console.log('✅ EpisodeReporting class loaded successfully');
                document.getElementById('episode-reporting-status').textContent = 'Loaded ✓';
                
                // Test modal elements
                const modal = document.getElementById('reportModal');
                const form = document.getElementById('reportForm');
                
                if (modal && form) {
                    console.log('✅ Modal and form elements found');
                } else {
                    console.error('❌ Modal or form elements not found');
                    document.getElementById('episode-reporting-status').textContent = 'Error: Elements not found ❌';
                }
            } else {
                console.error('❌ EpisodeReporting class not found');
                document.getElementById('episode-reporting-status').textContent = 'Error: Class not loaded ❌';
            }
        });
    </script>
@endpush
