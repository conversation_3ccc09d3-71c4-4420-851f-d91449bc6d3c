@extends('themevieon2025::layouts.app')

@section('title', 'Tìm kiếm phim' . ($keyword ? ': ' . $keyword : ''))
@section('description', 'Tìm kiếm phim hay, phim mới nhất với bộ lọc thông minh. Tìm phim theo thể loại, quốc gia, năm sản xuất.')

@section('content')
<div class="min-h-screen bg-theme-primary">
    <!-- Page Header -->
    <div class="bg-theme-secondary border-b border-theme-primary">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-2xl md:text-3xl font-bold text-theme-primary mb-2">
                    @if($keyword)
                        Kết quả tìm kiếm: "{{ $keyword }}"
                    @else
                        Tìm kiếm phim
                    @endif
                </h1>
                <p class="text-theme-tertiary">
                    @if($keyword)
                        T<PERSON><PERSON> thấy {{ $movies->total() }} kết quả
                    @else
                        Sử dụng bộ lọc để tìm phim yêu thích
                    @endif
                </p>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Filters Sidebar -->
            <div class="lg:col-span-1">
                @include('themevieon2025::components.movie-filters', [
                    'categories' => $categories ?? collect(),
                    'regions' => $regions ?? collect(),
                    'years' => $years ?? collect(),
                    'filters' => $filters ?? []
                ])
            </div>

            <!-- Results Content -->
            <div class="lg:col-span-3">
                <!-- Advanced Search Bar -->
                @include('themevieon2025::components.advanced-search')

                <!-- Sort Options -->
                @if(isset($filters))
                <div class="bg-theme-secondary rounded-lg p-4 mb-6">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <div class="flex items-center gap-2 text-sm text-theme-tertiary">
                            <i class="fas fa-sort"></i>
                            <span>Sắp xếp theo:</span>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            @php
                                $sortOptions = [
                                    'created_at' => 'Mới nhất',
                                    'view' => 'Lượt xem',
                                    'rating' => 'Đánh giá',
                                    'year' => 'Năm sản xuất',
                                    'name' => 'Tên phim'
                                ];
                            @endphp

                            @foreach($sortOptions as $sortKey => $sortLabel)
                                <a href="{{ request()->fullUrlWithQuery(['sort' => $sortKey, 'order' => ($filters['sort'] === $sortKey && $filters['order'] === 'desc') ? 'asc' : 'desc']) }}"
                                   class="px-3 py-1 text-xs rounded-lg transition-colors {{ ($filters['sort'] ?? 'created_at') === $sortKey ? 'bg-main text-white' : 'bg-theme-button hover:bg-theme-button-hover text-theme-primary' }}">
                                    {{ $sortLabel }}
                                    @if(($filters['sort'] ?? 'created_at') === $sortKey)
                                        <i class="fas fa-chevron-{{ ($filters['order'] ?? 'desc') === 'desc' ? 'down' : 'up' }} ml-1"></i>
                                    @endif
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Results -->
                @if($movies->count() > 0)
                    <!-- Movies Grid -->
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-4 mb-6">
                        @foreach($movies as $movie)
                            @include('themevieon2025::components.movie-card', [
                                'movie' => $movie,
                                'layout' => 'grid',
                                'showRating' => true,
                                'showActions' => true
                            ])
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if($movies->hasPages())
                        <div class="bg-theme-secondary rounded-lg p-4">
                            {{ $movies->links() }}
                        </div>
                    @endif
                @else
                    <!-- No Results -->
                    <div class="bg-theme-secondary rounded-lg p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <i class="fas fa-search text-6xl text-theme-tertiary mb-4"></i>
                            <h3 class="text-xl font-semibold text-theme-primary mb-2">
                                @if($keyword)
                                    Không tìm thấy kết quả
                                @else
                                    Bắt đầu tìm kiếm
                                @endif
                            </h3>
                            <p class="text-theme-tertiary mb-6">
                                @if($keyword)
                                    Không có phim nào phù hợp với từ khóa "{{ $keyword }}". Hãy thử tìm kiếm với từ khóa khác hoặc sử dụng bộ lọc.
                                @else
                                    Nhập từ khóa hoặc sử dụng bộ lọc để tìm phim yêu thích của bạn.
                                @endif
                            </p>

                            @if($keyword)
                                <div class="space-y-3">
                                    <a href="{{ route('search') }}"
                                       class="inline-block px-6 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                                        Xóa bộ lọc
                                    </a>
                                    <div class="text-sm text-theme-tertiary">
                                        <p>Gợi ý:</p>
                                        <ul class="mt-2 space-y-1">
                                            <li>• Kiểm tra chính tả từ khóa</li>
                                            <li>• Thử tìm kiếm với từ khóa ngắn hơn</li>
                                            <li>• Sử dụng tên tiếng Anh của phim</li>
                                            <li>• Tìm theo tên diễn viên hoặc đạo diễn</li>
                                        </ul>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('themes/vieon-2025/js/advanced-search.js') }}"></script>
@endpush
