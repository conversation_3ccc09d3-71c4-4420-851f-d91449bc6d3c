@extends('themevieon2025::layouts.app')

@section('title', 'Phim của ' . $studio->name . ' - Trang ' . ($movies->currentPage() ?? 1))
@section('description', '<PERSON><PERSON> tất cả phim của studio ' . $studio->name . '. <PERSON><PERSON> sách phim hay nhất do ' . $studio->name . ' s<PERSON><PERSON> xu<PERSON><PERSON> chất l<PERSON> HD miễn phí.')

@section('content')
<div class="min-h-screen bg-theme-primary">
    <!-- Page Header -->
    <div class="bg-theme-secondary border-b border-theme-primary">
        <div class="container mx-auto px-4 py-6">
            <!-- Breadcrumb -->
            <nav class="flex text-sm text-theme-tertiary mb-4">
                <a href="{{ route('home') }}" class="hover:text-main transition-colors">Trang chủ</a>
                <span class="mx-2">/</span>
                <a href="{{ route('studios.movies.index', $studio->slug) }}" class="hover:text-main transition-colors">Studio</a>
                <span class="mx-2">/</span>
                <span class="text-main">{{ $studio->name }}</span>
            </nav>
            
            <!-- Studio Profile -->
            <div class="flex flex-col md:flex-row gap-6">
                <!-- Studio Logo -->
                <div class="flex-shrink-0">
                    <div class="w-32 h-32 md:w-40 md:h-40 mx-auto md:mx-0">
                        @if($studio->logo)
                            <img src="{{ $studio->logo }}" 
                                 alt="{{ $studio->name }}" 
                                 class="w-full h-full object-contain bg-white rounded-lg border-4 border-main p-2">
                        @else
                            <div class="w-full h-full bg-theme-dropdown rounded-lg border-4 border-main flex items-center justify-center">
                                <i class="fas fa-building text-4xl text-theme-tertiary"></i>
                            </div>
                        @endif
                    </div>
                </div>
                
                <!-- Studio Info -->
                <div class="flex-1 text-center md:text-left">
                    <h1 class="text-2xl md:text-3xl font-bold text-theme-primary mb-2">
                        <i class="fas fa-building mr-2 text-main"></i>
                        {{ $studio->name }}
                    </h1>
                    
                    @if($studio->origin_name && $studio->origin_name !== $studio->name)
                        <p class="text-lg text-theme-secondary mb-3">{{ $studio->origin_name }}</p>
                    @endif
                    
                    <div class="flex flex-wrap justify-center md:justify-start gap-4 text-sm text-theme-tertiary mb-4">
                        @if($studio->founded_year)
                            <div class="flex items-center gap-1">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Thành lập {{ $studio->founded_year }}</span>
                            </div>
                        @endif
                        
                        @if($studio->country)
                            <div class="flex items-center gap-1">
                                <i class="fas fa-flag"></i>
                                <span>{{ $studio->country }}</span>
                            </div>
                        @endif
                        
                        <div class="flex items-center gap-1">
                            <i class="fas fa-film"></i>
                            <span>{{ $movies->total() }} phim</span>
                        </div>
                        
                        @if($studio->website)
                            <div class="flex items-center gap-1">
                                <i class="fas fa-globe"></i>
                                <a href="{{ $studio->website }}" target="_blank" class="text-main hover:underline">Website</a>
                            </div>
                        @endif
                    </div>
                    
                    @if($studio->description)
                        <div class="bg-theme-dropdown rounded-lg p-4">
                            <h3 class="font-semibold text-theme-primary mb-2">Giới thiệu</h3>
                            <p class="text-theme-secondary text-sm leading-relaxed line-clamp-3" id="description">
                                {{ $studio->description }}
                            </p>
                            @if(strlen($studio->description) > 200)
                                <button onclick="toggleDescription()" 
                                        class="text-main text-sm mt-2 hover:underline" 
                                        id="description-toggle">
                                    Xem thêm
                                </button>
                            @endif
                        </div>
                    @endif
                </div>
                
                <!-- Stats -->
                <div class="flex-shrink-0">
                    <div class="bg-theme-dropdown rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-main">{{ number_format($movies->total()) }}</div>
                        <div class="text-sm text-theme-tertiary">Tổng phim</div>
                        
                        @if($movies->count() > 0)
                            @php
                                $avgRating = $movies->avg('rating_star');
                                $totalViews = $movies->sum('view_total');
                                $latestYear = $movies->max('publish_year');
                            @endphp
                            
                            @if($avgRating > 0)
                                <div class="mt-3 pt-3 border-t border-theme-primary">
                                    <div class="text-lg font-bold text-yellow-500">{{ number_format($avgRating, 1) }}</div>
                                    <div class="text-xs text-theme-tertiary">Đánh giá TB</div>
                                </div>
                            @endif
                            
                            @if($totalViews > 0)
                                <div class="mt-3 pt-3 border-t border-theme-primary">
                                    <div class="text-lg font-bold text-blue-500">{{ number_format($totalViews) }}</div>
                                    <div class="text-xs text-theme-tertiary">Tổng lượt xem</div>
                                </div>
                            @endif
                            
                            @if($latestYear)
                                <div class="mt-3 pt-3 border-t border-theme-primary">
                                    <div class="text-lg font-bold text-green-500">{{ $latestYear }}</div>
                                    <div class="text-xs text-theme-tertiary">Phim mới nhất</div>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 py-6">
        <!-- Filter & Sort Bar -->
        @include('themevieon2025::components.movie-sort-bar', [
            'currentSort' => $currentSort ?? 'created_at',
            'currentOrder' => $currentOrder ?? 'desc'
        ])

        <!-- Movies Grid/List -->
        @if($movies->count() > 0)
            <div id="movies-container" class="{{ request('view', 'grid') === 'list' ? 'space-y-4' : 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4' }} mb-6">
                @foreach($movies as $movie)
                    @include('themevieon2025::components.movie-card', [
                        'movie' => $movie,
                        'layout' => request('view', 'grid'),
                        'showRating' => true,
                        'showActions' => true
                    ])
                @endforeach
            </div>

            <!-- Pagination -->
            @if($movies->hasPages())
                <div class="bg-theme-secondary rounded-lg p-4">
                    {{ $movies->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <!-- No Movies -->
            <div class="bg-theme-secondary rounded-lg p-8 text-center">
                <div class="max-w-md mx-auto">
                    <i class="fas fa-building text-6xl text-theme-tertiary mb-4"></i>
                    <h3 class="text-xl font-semibold text-theme-primary mb-2">
                        Chưa có phim nào
                    </h3>
                    <p class="text-theme-tertiary mb-6">
                        Hiện tại chưa có phim nào do {{ $studio->name }} sản xuất. Hãy quay lại sau hoặc khám phá các studio khác.
                    </p>
                    <a href="{{ route('home') }}" 
                       class="inline-block px-6 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                        Về trang chủ
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Popular Studios -->
    <div class="bg-theme-secondary border-t border-theme-primary">
        <div class="container mx-auto px-4 py-6">
            <h3 class="text-lg font-semibold text-theme-primary mb-4">
                <i class="fas fa-star mr-2"></i>
                Studio nổi tiếng
            </h3>
            
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                @php
                    $popularStudios = [
                        ['name' => 'Netflix', 'slug' => 'netflix', 'country' => 'Mỹ'],
                        ['name' => 'Disney', 'slug' => 'disney', 'country' => 'Mỹ'],
                        ['name' => 'HBO', 'slug' => 'hbo', 'country' => 'Mỹ'],
                        ['name' => 'SBS', 'slug' => 'sbs', 'country' => 'Hàn Quốc'],
                        ['name' => 'KBS', 'slug' => 'kbs', 'country' => 'Hàn Quốc'],
                        ['name' => 'MBC', 'slug' => 'mbc', 'country' => 'Hàn Quốc'],
                        ['name' => 'TVB', 'slug' => 'tvb', 'country' => 'Hồng Kông'],
                        ['name' => 'Youku', 'slug' => 'youku', 'country' => 'Trung Quốc'],
                        ['name' => 'iQiyi', 'slug' => 'iqiyi', 'country' => 'Trung Quốc'],
                        ['name' => 'Tencent', 'slug' => 'tencent', 'country' => 'Trung Quốc'],
                        ['name' => 'VTV', 'slug' => 'vtv', 'country' => 'Việt Nam'],
                        ['name' => 'HTV', 'slug' => 'htv', 'country' => 'Việt Nam']
                    ];
                @endphp
                
                @foreach($popularStudios as $popularStudio)
                    <a href="{{ route('studios.movies.index', $popularStudio['slug']) }}" 
                       class="p-3 bg-theme-dropdown hover:bg-theme-button-hover rounded-lg transition-colors text-center {{ $studio->slug === $popularStudio['slug'] ? 'ring-2 ring-main' : '' }}">
                        <div class="font-medium text-theme-primary text-sm">{{ $popularStudio['name'] }}</div>
                        <div class="text-xs text-theme-tertiary">{{ $popularStudio['country'] }}</div>
                    </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// View mode toggle
function setViewMode(mode) {
    const url = new URL(window.location);
    url.searchParams.set('view', mode);
    window.location.href = url.toString();
}

// Description toggle
function toggleDescription() {
    const description = document.getElementById('description');
    const toggle = document.getElementById('description-toggle');
    
    if (description.classList.contains('line-clamp-3')) {
        description.classList.remove('line-clamp-3');
        toggle.textContent = 'Thu gọn';
    } else {
        description.classList.add('line-clamp-3');
        toggle.textContent = 'Xem thêm';
    }
}

// AJAX pagination
document.addEventListener('DOMContentLoaded', function() {
    // Handle pagination clicks
    document.addEventListener('click', function(e) {
        if (e.target.matches('.pagination a') || e.target.closest('.pagination a')) {
            e.preventDefault();
            const link = e.target.matches('a') ? e.target : e.target.closest('a');
            loadPage(link.href);
        }
    });
    
    function loadPage(url) {
        // Show loading state
        const container = document.getElementById('movies-container');
        container.style.opacity = '0.5';
        
        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'text/html'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Parse the response and update content
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Update movies container
            const newContainer = doc.getElementById('movies-container');
            if (newContainer) {
                container.innerHTML = newContainer.innerHTML;
            }
            
            // Update pagination
            const pagination = document.querySelector('.pagination');
            const newPagination = doc.querySelector('.pagination');
            if (pagination && newPagination) {
                pagination.innerHTML = newPagination.innerHTML;
            }
            
            // Update URL
            window.history.pushState({}, '', url);
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        })
        .catch(error => {
            console.error('AJAX pagination error:', error);
        })
        .finally(() => {
            container.style.opacity = '1';
        });
    }
});
</script>
@endpush
