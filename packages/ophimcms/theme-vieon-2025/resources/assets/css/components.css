/* Header Gradient */
.header-gradient {
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-primary);
}

.header-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(30, 30, 30, 0.7) 0%, rgba(30, 30, 30, 0.3) 100%);
    pointer-events: none;
    z-index: -1;
}

.header-gradient > * {
    position: relative;
    z-index: 1;
}

/* User Pages Container - Tối <PERSON>u cho header fixed */
.user-page-container {
    padding-top: 5rem;
    padding-bottom: 3rem;
    min-height: calc(100vh - 8rem);
}

@media (max-width: 768px) {
    .user-page-container {
        padding-top: 4rem;
        padding-bottom: 2rem;
        min-height: calc(100vh - 6rem);
    }
}

/* Auth Pages - Đăng nhập/Đăng ký */
.auth-page-container {
    padding-top: 5rem;
    padding-bottom: 3rem;
    min-height: calc(100vh - 8rem);
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .auth-page-container {
        padding-top: 4rem;
        padding-bottom: 2rem;
        min-height: calc(100vh - 6rem);
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* User Profile Pages */
.user-profile-container {
    padding-top: 5rem;
    padding-bottom: 3rem;
    min-height: calc(100vh - 8rem);
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (max-width: 768px) {
    .user-profile-container {
        padding-top: 4rem;
        padding-bottom: 2rem;
        min-height: calc(100vh - 6rem);
    }
}

/* User Content Grid */
.user-content-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

@media (max-width: 640px) {
    .user-content-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 1rem;
    }
}

/* User Form Styles */
.user-form-container {
    background: var(--bg-card);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-primary);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
}

@media (max-width: 640px) {
    .user-form-container {
        padding: 1.5rem;
        margin: 0 1rem;
    }
}

/* User Card Styles */
.user-card {
    background: var(--bg-card);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-primary);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary);
    border-color: rgba(0, 255, 102, 0.3);
}

/* Form Inputs */
input, textarea, select {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-input);
    color: var(--text-primary);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--main-color);
    box-shadow: 0 0 0 3px rgba(0, 255, 102, 0.1);
}

/* Dropdown Menu */
.dropdown-menu {
    background: var(--bg-dropdown);
    border: 1px solid var(--border-primary);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-dropdown);
    backdrop-filter: blur(10px);
}

/* Mobile Menu */
#mobile-menu {
    background: var(--bg-dropdown);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(10px);
}

/* Header Submenu */
.header-submenu {
    background: var(--bg-dropdown);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(10px);
}

/* Footer Section */
footer {
    background: var(--bg-footer);
    border-top: 1px solid var(--border-footer);
}

/* Loading Overlay Styles */
#global-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-loading);
    position: fixed;
    inset: 0;
    z-index: 9999;
    transition: opacity 0.5s;
}

#global-loading img.animate-bounce {
    animation: bounce 1.2s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-18px); }
}

/* Menu Section */
.menu-section {
    background: var(--bg-menu);
    border-bottom: 1px solid var(--border-primary);
}

.menu-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(0, 255, 102, 0.05) 0%,
        transparent 100%);
    pointer-events: none;
}

.menu-section .overflow-x-auto::-webkit-scrollbar {
    height: 4px;
}

.menu-section .overflow-x-auto::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 2px;
}

.menu-section .overflow-x-auto::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 2px;
}

.menu-section .overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: var(--main-color);
}

.menu-section a {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.menu-section a:hover {
    color: var(--text-primary);
    border-color: var(--main-color);
}

.menu-section a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--main-color);
    transition: width 0.3s ease;
}

.menu-section a:hover::after {
    width: 100%;
}

.menu-section .text-main {
    color: var(--main-color);
}

@media (max-width: 768px) {
    .menu-section {
        padding: 1rem 0;
    }

    .menu-section .space-x-6 > * + * {
        margin-left: 1rem;
    }

    .menu-section .space-x-4 > * + * {
        margin-left: 0.75rem;
    }
}

/* Footer Section */
.footer-section {
    background: var(--bg-footer);
    border-top: 1px solid var(--border-footer);
}

.footer-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(0, 255, 102, 0.02) 0%,
        transparent 100%);
    pointer-events: none;
}

@media (max-width: 1024px) {
    .footer-section .grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .footer-section .grid {
        grid-template-columns: 1fr;
    }

    .footer-section .space-y-4 > * + * {
        margin-top: 1rem;
    }
}

.footer-section a {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--main-color);
}

.footer-section .fab {
    color: var(--text-tertiary);
}

.footer-section .fab:hover {
    color: var(--main-color);
}

/* Back to Top Button */
#back-to-top {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

#back-to-top:hover {
    background: var(--bg-secondary);
    color: var(--main-color);
}

#back-to-top:active {
    transform: scale(0.95);
}

/* Footer Spacing */
.footer-section .space-y-2 > * + * {
    margin-top: 0.5rem;
}

.footer-section .space-y-4 > * + * {
    margin-top: 1rem;
}

.footer-section .space-y-6 > * + * {
    margin-top: 1.5rem;
}

.footer-section .space-y-8 > * + * {
    margin-top: 2rem;
}

/* Footer Typography */
.footer-section h3,
.footer-section .text-lg {
    color: var(--text-primary);
    font-weight: 600;
}

.footer-section .text-sm {
    color: var(--text-secondary);
}

.footer-section .text-xs {
    color: var(--text-tertiary);
}

.footer-section .border-t {
    border-color: var(--border-footer);
}

.footer-section .border-white\/10 {
    border-color: var(--border-footer);
}

@media (max-width: 640px) {
    .footer-section .text-sm {
        font-size: 0.875rem;
    }

    .footer-section .text-xs {
        font-size: 0.75rem;
    }
}

/* Hero Swiper */
.hero-swiper .swiper-pagination-bullet {
    background: var(--text-tertiary);
    opacity: 0.5;
    transition: all 0.3s ease;
}

.hero-swiper .swiper-pagination-bullet-active {
    background: var(--main-color);
    opacity: 1;
    transform: scale(1.2);
}

.hero-swiper .swiper-pagination {
    bottom: 2rem;
    z-index: 10;
}

.hero-swiper-prev, .hero-swiper-next {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.hero-swiper-prev:hover, .hero-swiper-next:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--main-color);
    color: var(--text-primary);
}

/* User Navigation Styles */
.nav-item {
    display: flex;
    align-items: center;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    padding: 0.75rem 1rem;
    text-decoration: none;
    position: relative;
}

.nav-item i {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    flex-shrink: 0;
}

/* Compact mode styles for desktop - based on parent sidebar state */
@media (min-width: 1024px) {
    /* Default nav item styles - left aligned */
    .nav-item {
        justify-content: flex-start;
        padding: 0.75rem 1rem;
    }

    /* ONLY in compact mode - center the nav items */
    .sidebar-compact .nav-item {
        justify-content: center !important;
        padding: 0.75rem 0.5rem !important;
    }

    .sidebar-compact .nav-item i {
        margin-right: 0 !important;
    }
}

/* Navigation hover and active states */
.nav-item:hover {
    transform: translateX(4px);
}

/* Compact mode hover - no horizontal movement, keep centered */
.sidebar-compact .nav-item:hover {
    transform: none;
}

.nav-item.bg-main {
    background-color: var(--main-color);
    color: white;
}

.nav-item.text-gray-300 {
    color: rgb(209 213 219);
}

.nav-item.hover\:bg-gray-700\/50:hover {
    background-color: rgba(55, 65, 81, 0.5);
}

.nav-item.hover\:text-white:hover {
    color: white;
}

/* User sidebar styles */
.user-sidebar {
    background: var(--bg-card);
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 2rem;
    transition: all 0.3s ease-in-out;
}

.user-sidebar.compact {
    width: 4rem;
    padding: 0.5rem;
}

.user-sidebar:not(.compact) {
    width: 16rem;
    padding: 1.5rem;
}

/* Mobile sidebar overlay */
.sidebar-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
}

/* Mobile sidebar positioning */
@media (max-width: 1023px) {
    .user-sidebar.mobile {
        position: fixed;
        top: 1rem;
        left: 1rem;
        right: 1rem;
        z-index: 50;
        width: auto;
        padding: 1.5rem;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .user-sidebar.mobile.open {
        transform: translateX(0);
    }

    /* Mobile sidebar full width adjustments */
    .bg-theme-card.fixed {
        left: 1rem;
        right: 1rem;
        width: auto;
    }
}

/* User info section */
.user-info {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-primary);
}

.user-avatar {
    width: 4rem;
    height: 4rem;
    background-color: rgba(0, 255, 102, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
}

.user-avatar i {
    font-size: 1.5rem;
    color: var(--main-color);
    margin-right: 0;
}

.user-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.user-email {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Navigation menu */
.nav-menu {
    margin-bottom: 1.5rem;
}

.nav-menu .nav-item {
    margin-bottom: 0.25rem;
}

/* Logout section */
.logout-section {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-primary);
}

.logout-button {
    width: 100%;
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: rgb(248 113 113);
}

.logout-button:hover {
    background-color: rgba(239, 68, 68, 0.2);
    color: rgb(252 165 165);
}

/* Compact toggle button */
.compact-toggle {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-primary);
}

.compact-toggle button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    color: var(--text-secondary);
    background: transparent;
    border: none;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.compact-toggle button:hover {
    color: var(--text-primary);
    background-color: rgba(55, 65, 81, 0.5);
}

.compact-toggle i {
    transition: transform 0.2s ease;
}

.compact-toggle i.rotate-180 {
    transform: rotate(180deg);
}

@media (min-width: 768px) {
  .menu-item-active {
    position: relative;
    border-bottom: 2.5px solid var(--main-color);
  }
  .menu-item-active::before {
    content: '';
    position: absolute;
    left: 0;
    top: calc(100% - 1px);
    width: 100%;
    height: 0.18rem;
    background: radial-gradient(50% 50% at 50% 0%, var(--main-color) 0%, transparent 100%);
    opacity: 0.7;
    pointer-events: none;
    z-index: 1;
    filter: blur(0.5px);
  }
  .menu-item-active::after {
    content: '';
    position: absolute;
    left: 0;
    top: calc(100% - 2px);
    width: 100%;
    height: 0.32rem;
    background: radial-gradient(50% 75% at 50% 75%, var(--main-color) 0%, transparent 100%);
    opacity: 0.5;
    pointer-events: none;
    z-index: 0;
    filter: blur(2px);
  }
}
