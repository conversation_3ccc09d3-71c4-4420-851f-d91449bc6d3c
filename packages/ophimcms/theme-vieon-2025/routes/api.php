<?php

use Illuminate\Support\Facades\Route;
use Ophim\ThemeVieon2025\Controllers\SearchController;
use Ophim\ThemeVieon2025\Controllers\User\AccountController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiAuthController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiCommentController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiEmailVerificationController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiPasswordController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiProfileController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiRatingController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiTwoFactorController;
use Ophim\ThemeVieon2025\Controllers\User\API\ApiUserController;
use Ophim\ThemeVieon2025\Controllers\User\PasswordController;
use Ophim\ThemeVieon2025\Controllers\User\ProfileController;
use Ophim\ThemeVieon2025\Controllers\User\SettingsController;

Route::group([
    'prefix' => 'api',
    'middleware' => array_merge(
        (array)config('backpack.base.web_middleware', 'web'),
    ),
], function () {
    // API Search routes
    Route::get('/search', [SearchController::class, 'apiSearch'])->name('api.search');
    Route::get('/search/popular', [SearchController::class, 'popularSearches'])->name('api.search.popular');
    Route::get('/search/enhanced', [SearchController::class, 'enhancedSearch'])->name('api.search.enhanced');

    // Auth Routes
    Route::post('/login', [ApiAuthController::class, 'login'])->name('api.login');
    Route::post('/register', [ApiAuthController::class, 'register'])->name('api.register');
    Route::post('/logout', [ApiAuthController::class, 'logout'])->middleware('auth:api')->name('api.logout');

    // User profile Routes
    Route::prefix('profile')->middleware('auth:api')->group(function () {
        Route::get('/', [ApiProfileController::class, 'apiShow'])->name('api.profile.show');
        Route::post('/', [ProfileController::class, 'apiUpdate'])->name('api.profile.update');
        Route::delete('/', [AccountController::class, 'apiDestroy'])->name('api.profile.destroy');
    });

    // Settings Routes
    Route::prefix('settings')->middleware('auth:api')->group(function () {
        Route::get('/', [SettingsController::class, 'show'])->name('api.settings.show');
        Route::post('/', [SettingsController::class, 'apiUpdate'])->name('api.settings.update');
    });

    // Password Routes
    Route::prefix('password')->group(function () {
        Route::post('/email', [ApiPasswordController::class, 'apiSendResetLink'])->name('api.password.email');
        Route::post('/reset', [ApiPasswordController::class, 'apiReset'])->name('api.password.reset');
        Route::post('/change', [PasswordController::class, 'apiUpdate'])->middleware('auth:api')->name('api.password.change');
    });

    // Two Factor Auth Routes
    Route::prefix('2fa')->middleware('auth:api')->group(function () {
        Route::post('/send', [ApiTwoFactorController::class, 'apiSend2faCode'])->name('api.2fa.send');
        Route::post('/verify', [ApiTwoFactorController::class, 'apiVerify2fa'])->name('api.2fa.verify');
    });

    // Email Verification Routes
    Route::prefix('email')->middleware('auth:api')->group(function () {
        Route::post('/verify/{id}/{hash}', [ApiEmailVerificationController::class, 'apiVerify'])->name('api.verification.verify');
        Route::post('/resend', [ApiEmailVerificationController::class, 'apiResend'])->name('api.verification.resend');
    });

    // Comment Routes
    Route::prefix('movies/{movieId}/comments')->middleware('auth:api')->group(function () {
        Route::get('/', [ApiCommentController::class, 'apiIndex'])->name('api.comments.index');
        Route::post('/', [ApiCommentController::class, 'apiStore'])->name('api.comments.store');
    });
    Route::delete('/comments/{id}', [ApiCommentController::class, 'apiDestroy'])->middleware('auth:api')->name('api.comments.destroy');

    // Rating Routes
    Route::prefix('movies/{movieId}/ratings')->middleware('auth:api')->group(function () {
        Route::get('/', [ApiRatingController::class, 'apiIndex'])->name('api.ratings.index');
        Route::post('/', [ApiRatingController::class, 'apiStore'])->name('api.ratings.store');
        Route::delete('/', [ApiRatingController::class, 'apiDestroy'])->name('api.ratings.destroy');
    });

    // User Feature Routes
    Route::middleware('auth:api')->group(function () {
        Route::get('/dashboard', [ApiUserController::class, 'apiDashboard'])->name('api.user.dashboard');
        Route::get('/favorites', [ApiUserController::class, 'apiFavorites'])->name('api.user.favorites');
        Route::get('/history', [ApiUserController::class, 'apiHistory'])->name('api.user.history');
        Route::get('/watchlist', [ApiUserController::class, 'apiWatchlist'])->name('api.user.watchlist');
        Route::get('/recommendations', [ApiUserController::class, 'apiRecommendations'])->name('api.user.recommendations');

        // Favorites management
        Route::post('/favorites', [ApiUserController::class, 'apiAddToFavorites'])->name('api.user.favorites.add');
        Route::delete('/favorites/{movieId}', [ApiUserController::class, 'apiRemoveFromFavorites'])->name('api.user.favorites.remove');

        // Watchlist management
        Route::post('/watchlist', [ApiUserController::class, 'apiAddToWatchlist'])->name('api.user.watchlist.add');
        Route::delete('/watchlist/{movieId}', [ApiUserController::class, 'apiRemoveFromWatchlist'])->name('api.user.watchlist.remove');

        // Movie rating
        Route::post('/movies/{movieId}/rate', [ApiUserController::class, 'apiRateMovie'])->name('api.user.rate');
    });
});
