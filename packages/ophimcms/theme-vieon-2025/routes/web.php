<?php

use Illuminate\Support\Facades\Route;
use Ophim\ThemeVieon2025\Controllers\ActorController;
use Ophim\ThemeVieon2025\Controllers\CategoryController;
use Ophim\ThemeVieon2025\Controllers\DirectorController;
use Ophim\ThemeVieon2025\Controllers\EpisodeController;
use Ophim\ThemeVieon2025\Controllers\HomeController;
use Ophim\ThemeVieon2025\Controllers\MovieController;
use Ophim\ThemeVieon2025\Controllers\RegionController;
use Ophim\ThemeVieon2025\Controllers\SearchController;
use Ophim\ThemeVieon2025\Controllers\StudioController;
use Ophim\ThemeVieon2025\Controllers\TagController;
use Ophim\ThemeVieon2025\Controllers\User\AccountController;
use Ophim\ThemeVieon2025\Controllers\User\AuthController;
use Ophim\ThemeVieon2025\Controllers\User\CommentController;
use Ophim\ThemeVieon2025\Controllers\User\FavoriteController;
use Ophim\ThemeVieon2025\Controllers\User\HistoryController;
use Ophim\ThemeVieon2025\Controllers\User\PasswordController;
use Ophim\ThemeVieon2025\Controllers\User\ProfileController;
use Ophim\ThemeVieon2025\Controllers\User\RatingController;
use Ophim\ThemeVieon2025\Controllers\User\SettingsController;
use Ophim\ThemeVieon2025\Controllers\User\UserController;

Route::group(['prefix' => '/', 'middleware' => ['web']], function () {
    // Auth routes
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Public routes
    Route::get('/', [HomeController::class, 'index'])->name('home');
    Route::get('/search', [SearchController::class, 'index'])->name('search');

    // Movies routes
    Route::get('/phim', [MovieController::class, 'index'])->name('movies.index');
    Route::get('/phim/{movie}', [MovieController::class, 'show'])
        ->where(['movie' => '.+'])
        ->name('movies.show');

    // Episode routes
    Route::get('/phim/{movie}/tap-{episode}', [EpisodeController::class, 'show'])
        ->where(['movie' => '.+', 'episode' => '.+'])
        ->name('episodes.show');

    // Category routes
    Route::get('/the-loai/{category}', [CategoryController::class, 'show'])
        ->where(['category' => '.+'])
        ->name('categories.movies.index');

    // Region routes
    Route::get('/quoc-gia/{region}', [RegionController::class, 'show'])
        ->where(['region' => '.+'])
        ->name('regions.movies.index');

    // Tag routes
    Route::get('/tu-khoa/{tag}', [TagController::class, 'show'])
        ->where(['tag' => '.+'])
        ->name('tags.movies.index');

    // Actor routes
    Route::get('/dien-vien/{actor}', [ActorController::class, 'show'])
        ->where(['actor' => '.+'])
        ->name('actors.movies.index');

    // Director routes
    Route::get('/dao-dien/{director}', [DirectorController::class, 'show'])
        ->where(['director' => '.+'])
        ->name('directors.movies.index');

    // Studio routes
    Route::get('/studio/{studio}', [StudioController::class, 'show'])
        ->where(['studio' => '.+'])
        ->name('studios.movies.index');

    // AJAX Action routes
    Route::post('/phim/{movie}/rate', [MovieController::class, 'rateMovie'])
        ->where(['movie' => '.+'])
        ->name('movie.rating');

    Route::post('/phim/{movie}/{episode}/report', [EpisodeController::class, 'reportEpisode'])
        ->where(['movie' => '.+', 'episode' => '.+'])
        ->name('episodes.report');

    // User routes (require authentication) - Only page views, actions moved to API
    Route::group(['prefix' => 'user', 'middleware' => 'auth'], function () {
        // Page views only - data loaded via AJAX from API
        Route::get('/dashboard', [UserController::class, 'dashboard'])->name('user.dashboard');
        Route::get('/favorites', [UserController::class, 'favorites'])->name('user.favorites');
        Route::get('/history', [UserController::class, 'history'])->name('user.history');
        Route::get('/watchlist', [UserController::class, 'watchlist'])->name('user.watchlist');
        Route::get('/recommendations', [UserController::class, 'recommendations'])->name('user.recommendations');
        Route::get('/comments', [UserController::class, 'userComments'])->name('user.comments.index');
        Route::get('/ratings', [UserController::class, 'userRatings'])->name('user.ratings.index');

        // Profile routes - show pages only, updates via API
        Route::get('/profile', [ProfileController::class, 'show'])->name('user.profile.show');

        // Settings routes - show pages only, updates via API
        Route::get('/settings', [SettingsController::class, 'show'])->name('user.settings.show');

        // Password routes - show pages only, updates via API
        Route::get('/password', [PasswordController::class, 'show'])->name('user.password.show');

        // Account routes - show pages only, deletes via API
        Route::get('/account', [AccountController::class, 'show'])->name('user.account.show');

        // Legacy routes - deprecated, use API endpoints instead
        // These routes are kept for backward compatibility but should be migrated to API
        Route::post('/profile', [ProfileController::class, 'update'])->name('user.profile.update');
        Route::post('/settings', [SettingsController::class, 'update'])->name('user.settings.update');
        Route::post('/password', [PasswordController::class, 'update'])->name('user.password.update');
        Route::delete('/account', [AccountController::class, 'destroy'])->name('user.account.destroy');
        Route::post('/favorites/add', [UserController::class, 'addToFavorites'])->name('user.favorites.add');
        Route::post('/favorites/remove', [UserController::class, 'removeFromFavorites'])->name('user.favorites.remove');
        Route::post('/watchlist/add', [UserController::class, 'addToWatchlist'])->name('user.watchlist.add');
        Route::post('/watchlist/remove', [UserController::class, 'removeFromWatchlist'])->name('user.watchlist.remove');
        Route::post('/rate', [UserController::class, 'rateMovie'])->name('user.rate');
    });

    // Comment routes
    Route::group(['prefix' => 'comments', 'middleware' => 'auth'], function () {
        Route::get('/{movieId}', [CommentController::class, 'index'])->name('comments.index');
        Route::post('/{movieId}', [CommentController::class, 'store'])->name('comments.store');
        Route::delete('/{id}', [CommentController::class, 'destroy'])->name('comments.destroy');
    });

    // Favorite routes
    Route::group(['prefix' => 'favorites', 'middleware' => 'auth'], function () {
        Route::get('/', [FavoriteController::class, 'index'])->name('favorites.index');
        Route::post('/', [FavoriteController::class, 'store'])->name('favorites.store');
        Route::delete('/{id}', [FavoriteController::class, 'destroy'])->name('favorites.destroy');
    });

    // History routes
    Route::group(['prefix' => 'history', 'middleware' => 'auth'], function () {
        Route::get('/', [HistoryController::class, 'index'])->name('history.index');
        Route::post('/', [HistoryController::class, 'store'])->name('history.store');
        Route::delete('/{id}', [HistoryController::class, 'destroy'])->name('history.destroy');
    });

    // Rating routes
    Route::group(['prefix' => 'ratings', 'middleware' => 'auth'], function () {
        Route::get('/{movieId}', [RatingController::class, 'index'])->name('ratings.index');
        Route::post('/{movieId}', [RatingController::class, 'store'])->name('ratings.store');
        Route::delete('/{movieId}', [RatingController::class, 'destroy'])->name('ratings.destroy');
    });
});

// Admin routes (require backpack authentication)
Route::group([
    'prefix' => config('backpack.base.route_prefix', 'admin'),
    'middleware' => array_merge(
        (array)config('backpack.base.web_middleware', 'web'),
        (array)config('backpack.base.middleware_key', 'admin')
    ),
    'namespace' => 'Ophim\ThemeVieon2025\Controllers\Admin',
], function () {
    Route::get('/vieon-dashboard', 'ThemeAdminController@dashboard')->name('admin.vieon.dashboard');
    Route::get('/vieon-settings', 'ThemeAdminController@settings')->name('admin.vieon.settings');
    Route::get('/vieon-analytics', 'ThemeAdminController@analytics')->name('admin.vieon.analytics');
});

// Test routes (development only)
Route::get('/test-rating', function () {
    return view('themevieon2025::test-rating');
})->name('test.rating');

Route::post('/test-rating-endpoint', function () {
    return response()->json([
        'status' => true,
        'message' => 'Test rating submitted successfully!',
        'rating_star' => 4.2,
        'rating_count' => 126,
        'data' => [
            'rating' => request('rating'),
            'review' => null,
        ]
    ]);
})->name('test.rating.submit');

Route::get('/test-episode-reporting', function () {
    return view('themevieon2025::test-episode-reporting');
})->name('test.episode.reporting');

Route::post('/test-episode-report', function () {
    $request = request();

    if (!$request->message || strlen(trim($request->message)) < 10) {
        return response()->json([
            'status' => false,
            'message' => 'Vui lòng nhập mô tả chi tiết (tối thiểu 10 ký tự)'
        ], 422);
    }

    return response()->json([
        'status' => true,
        'message' => 'Báo cáo đã được gửi thành công! Chúng tôi sẽ xem xét và xử lý trong thời gian sớm nhất.',
        'data' => [
            'reason' => $request->reason,
            'message' => $request->message,
            'submitted_at' => now()->format('Y-m-d H:i:s')
        ]
    ]);
})->name('test.episode.report.submit');

Route::get('/test-advanced-search', function () {
    return view('themevieon2025::test-advanced-search');
})->name('test.advanced.search');
