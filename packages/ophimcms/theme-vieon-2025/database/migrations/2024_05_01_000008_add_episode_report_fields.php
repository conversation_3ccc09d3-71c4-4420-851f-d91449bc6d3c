<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('episodes', function (Blueprint $table) {
            if (!Schema::hasColumn('episodes', 'report_reason')) {
                $table->string('report_reason', 100)->nullable()->after('report_message');
            }
            if (!Schema::hasColumn('episodes', 'reported_at')) {
                $table->timestamp('reported_at')->nullable()->after('report_reason');
            }
        });
    }

    public function down()
    {
        Schema::table('episodes', function (Blueprint $table) {
            if (Schema::hasColumn('episodes', 'report_reason')) {
                $table->dropColumn('report_reason');
            }
            if (Schema::hasColumn('episodes', 'reported_at')) {
                $table->dropColumn('reported_at');
            }
        });
    }
};
