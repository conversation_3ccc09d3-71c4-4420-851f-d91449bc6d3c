<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Studio;
use App\Models\Movie;

class StudioController extends Controller
{
    public function show($studio)
    {
        $studio = Studio::where('slug', $studio)->firstOrFail();
        $movies = Movie::with(['categories', 'regions'])
            ->whereHas('studios', function ($query) use ($studio) {
                $query->where('id', $studio->id);
            })
            ->published()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::pages.studio', [
            'studio' => $studio,
            'movies' => $movies
        ]);
    }
}
