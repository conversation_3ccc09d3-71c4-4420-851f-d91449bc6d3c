<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Catalog;
use App\Models\Movie;

class TypeController extends Controller
{
    public function show($type)
    {
        // Find catalog by slug
        $catalog = Catalog::where('slug', $type)->firstOrFail();

        // Parse catalog configuration
        $value = explode('|', trim($catalog->value));
        [$relation_config, $field, $val, $sortKey, $alg] = array_merge($value, ['', 'is_copyright', 0, 'created_at', 'desc']);
        $relation_config = explode(',', $relation_config);

        [$relation_table, $relation_field, $relation_val] = array_merge($relation_config, ['', '', '']);

        // Build query based on catalog configuration
        $query = Movie::with(['categories', 'regions']);

        if ($relation_table) {
            $query->whereHas($relation_table, function ($rel) use ($relation_field, $relation_val, $field, $val) {
                if ($relation_field && $relation_val) {
                    $rel->where($relation_field, $relation_val);
                }
                if ($field && $val) {
                    $fields = explode(",", $field);
                    $values = explode(",", $val);
                    if (count($fields) === count($values)) {
                        $rel->where(array_combine($fields, $values));
                    }
                }
            });
        } else {
            if ($field && $val) {
                $fields = explode(",", $field);
                $values = explode(",", $val);
                if (count($fields) === count($values)) {
                    $query->where(array_combine($fields, $values));
                }
            }
        }

        // Apply sorting and pagination
        $movies = $query->published()
            ->orderBy($sortKey, $alg)
            ->paginate(24);

        return view('themevieon2025::pages.type', [
            'catalog' => $catalog,
            'movies' => $movies,
            'type' => $catalog
        ]);
    }
}
