<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Movie;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Ophim\ThemeVieon2025\Models\Rating;

class MovieController extends Controller
{
    public function index(Request $request)
    {
        $query = Movie::published();

        // Filter by category
        if ($request->has('category')) {
            $category = $request->get('category');
            switch ($category) {
                case 'phim-moi':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'phim-bo':
                    $query->series()->orderBy('updated_at', 'desc');
                    break;
                case 'phim-le':
                    $query->single()->orderBy('created_at', 'desc');
                    break;
            }
        }

        $movies = $query->paginate(24);

        return view('themevieon2025::movies.index', [
            'movies' => $movies
        ]);
    }

    public function show($slug)
    {
        $movie = Movie::where('slug', $slug)
            ->with(['categories', 'regions', 'actors', 'directors', 'studios', 'tags', 'episodes'])
            ->firstOrFail();

        // Increment view count
        $movie->increment('view_total');
        $movie->increment('view_day');
        $movie->increment('view_week');
        $movie->increment('view_month');

        // Get related movies with caching
        $relatedMovies = Cache::remember("movie_related_{$movie->id}", setting('site_cache_ttl', 5 * 60), function () use ($movie) {
            return Movie::published()
                ->whereHas('categories', function($query) use ($movie) {
                    $query->whereIn('categories.id', $movie->categories->pluck('id'));
                })
                ->where('id', '!=', $movie->id)
                ->orderBy('view_total', 'desc')
                ->take(12)
                ->get();
        });

        return view('themevieon2025::movie', [
            'movie' => $movie,
            'relatedMovies' => $relatedMovies
        ]);
    }

    /**
     * Rate a movie (AJAX endpoint)
     */
    public function rateMovie(Request $request, $slug)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'status' => false,
                'message' => 'Bạn cần đăng nhập để đánh giá phim.',
                'redirect' => route('login')
            ], 401);
        }

        // Validate request
        $request->validate([
            'rating' => 'required|integer|between:1,5',
            'review' => 'nullable|string|max:500'
        ]);

        // Find movie
        $movie = Movie::where('slug', $slug)->firstOrFail();

        // Create or update rating
        $rating = Rating::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'movie_id' => $movie->id,
            ],
            [
                'rating' => $request->rating,
                'review' => $request->review ?? null,
            ]
        );

        // Update movie rating statistics
        $this->updateMovieRatingStats($movie);

        // Clear related caches
        Cache::forget("movie_related_{$movie->id}");

        return response()->json([
            'status' => true,
            'message' => 'Đánh giá phim thành công!',
            'rating_star' => number_format($movie->fresh()->rating_star ?? 0, 1),
            'rating_count' => $movie->fresh()->rating_count ?? 0,
            'data' => [
                'rating' => $rating->rating,
                'review' => $rating->review,
            ]
        ]);
    }

    /**
     * Update movie rating statistics
     */
    private function updateMovieRatingStats(Movie $movie)
    {
        $ratings = Rating::where('movie_id', $movie->id)->get();

        if ($ratings->count() > 0) {
            $averageRating = $ratings->avg('rating');
            $ratingCount = $ratings->count();

            $movie->update([
                'rating_star' => $averageRating,
                'rating_count' => $ratingCount
            ]);
        }
    }
}
