<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Director;
use App\Models\Movie;

class DirectorController extends Controller
{
    public function show($director)
    {
        $director = Director::where('slug', $director)->firstOrFail();
        $movies = Movie::with(['categories', 'regions'])
            ->whereHas('directors', function ($query) use ($director) {
                $query->where('id', $director->id);
            })
            ->published()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::pages.director', [
            'director' => $director,
            'movies' => $movies
        ]);
    }
}
