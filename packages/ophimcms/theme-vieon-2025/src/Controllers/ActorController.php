<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Actor;
use App\Models\Movie;

class ActorController extends Controller
{
    public function show($actor)
    {
        $actor = Actor::where('slug', $actor)->firstOrFail();
        $movies = Movie::with(['categories', 'regions'])
            ->whereHas('actors', function ($query) use ($actor) {
                $query->where('id', $actor->id);
            })
            ->published()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::pages.actor', [
            'actor' => $actor,
            'movies' => $movies
        ]);
    }
}
