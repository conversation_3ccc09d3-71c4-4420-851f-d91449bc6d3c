<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Tag;
use App\Models\Movie;

class TagController extends Controller
{
    public function show($tag)
    {
        $tag = Tag::where('slug', $tag)->firstOrFail();
        $movies = Movie::with(['categories', 'regions'])
            ->whereHas('tags', function ($query) use ($tag) {
                $query->where('id', $tag->id);
            })
            ->published()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::pages.tag', [
            'tag' => $tag,
            'movies' => $movies
        ]);
    }
}
