<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Region;
use App\Models\Movie;
use Illuminate\Support\Facades\Cache;

class RegionController extends Controller
{
    public function show($regionSlug, Request $request)
    {
        // Get region from cache
        $region = Cache::remember("region_{$regionSlug}", setting('site_cache_ttl', 5 * 60), function () use ($regionSlug) {
            return Region::where('slug', $regionSlug)->firstOrFail();
        });

        // Generate SEO tags
        $region->generateSeoTags();

        // Get sorting parameters
        $sort = $request->get('sort', 'created_at');
        $order = $request->get('order', 'desc');

        // Build query with caching for popular sorts
        $cacheKey = "region_movies_{$region->id}_{$sort}_{$order}_" . request('page', 1);

        if (in_array($sort, ['created_at', 'view_total']) && $order === 'desc') {
            $movies = Cache::remember($cacheKey, setting('site_cache_ttl', 5 * 60), function () use ($region, $sort, $order) {
                return $this->getMoviesQuery($region, $sort, $order)->paginate(24);
            });
        } else {
            $movies = $this->getMoviesQuery($region, $sort, $order)->paginate(24);
        }

        return view('themevieon2025::pages.region', [
            'region' => $region,
            'movies' => $movies,
            'currentSort' => $sort,
            'currentOrder' => $order
        ]);
    }

    /**
     * Get movies query for region
     */
    private function getMoviesQuery($region, $sort = 'created_at', $order = 'desc')
    {
        $query = Movie::with(['categories', 'regions'])
            ->whereHas('regions', function ($q) use ($region) {
                $q->where('id', $region->id);
            })
            ->published();

        // Apply sorting
        switch ($sort) {
            case 'name':
                $query->orderBy('name', $order);
                break;
            case 'year':
                $query->orderBy('publish_year', $order);
                break;
            case 'view':
                $query->orderBy('view_total', $order);
                break;
            case 'rating':
                $query->orderBy('rating_star', $order);
                break;
            default:
                $query->orderBy('created_at', $order);
        }

        return $query;
    }
}
