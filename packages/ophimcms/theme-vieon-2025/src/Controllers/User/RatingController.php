<?php

namespace Ophim\ThemeVieon2025\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Ophim\ThemeVieon2025\Models\Rating;

class RatingController extends Controller
{
    public function index($movieId)
    {
        $ratings = Rating::where('movie_id', $movieId)->with('user')->latest()->paginate(10);
        return view('themevieon2025::user.ratings.index', compact('ratings', 'movieId'));
    }

    public function store(Request $request, $movieId)
    {
        $data = $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:500',
        ]);
        $data['user_id'] = Auth::id();
        $data['movie_id'] = $movieId;
        Rating::updateOrCreate(
            [
                'user_id' => $data['user_id'],
                'movie_id' => $data['movie_id'],
            ],
            [
                'rating' => $data['rating'],
                'review' => $data['review'] ?? null,
            ]
        );
        return back()->with('status', 'Đánh giá thành công!');
    }

    public function destroy($movieId)
    {
        $rating = Rating::where('user_id', Auth::id())->where('movie_id', $movieId)->firstOrFail();
        $rating->delete();
        return back()->with('status', 'Đã xóa đánh giá!');
    }
}
