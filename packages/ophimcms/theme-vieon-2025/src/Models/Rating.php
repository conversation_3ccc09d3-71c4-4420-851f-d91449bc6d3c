<?php

namespace Ophim\ThemeVieon2025\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use App\Models\Movie;

class Rating extends Model
{
    protected $table = 'ratings';
    protected $fillable = [
        'user_id', 'movie_id', 'rating', 'review',
    ];

    protected $casts = [
        'rating' => 'integer'
    ];

    /**
     * Get the user that owns the rating.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the movie that owns the rating.
     */
    public function movie(): BelongsTo
    {
        return $this->belongsTo(Movie::class);
    }
}
