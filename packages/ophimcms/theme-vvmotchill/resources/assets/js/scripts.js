document.addEventListener('DOMContentLoaded', function () {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    function switchTab(tabId) {
        // Remove active class from all buttons and panes
        tabButtons.forEach(button => button.classList.remove('active'));
        tabPanes.forEach(pane => pane.classList.remove('active'));

        // Add active class to selected button and pane
        const selectedButton = document.querySelector(`[data-tab="${tabId}"]`);
        const selectedPane = document.getElementById(tabId);

        selectedButton.classList.add('active');
        selectedPane.classList.add('active');
    }

    // Add click event listeners to all tab buttons
    tabButtons.forEach(button => {
        button.addEventListener('click', function () {
            const tabId = this.getAttribute('data-tab');
            switchTab(tabId);
        });
    });


    const topViewTabButtons = document.querySelectorAll('.top-view-tab-button');
    const topViewTabContents = document.querySelectorAll('.top-view-tab-content');

    topViewTabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-top-view-tab');

            topViewTabButtons.forEach(btn => {
                btn.classList.remove('bg-[#d98a5e]', 'text-black');
                btn.classList.add('bg-zinc-800', 'text-white');
            });
            button.classList.remove('bg-zinc-800');
            button.classList.add('bg-[#d98a5e]', 'text-white');

            topViewTabContents.forEach(content => {
                content.style.display = content.id === `top-view-${targetTab}` ? 'block' : 'none';
            });
        });
    });

    // Lấy các elements
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileSearchButton = document.getElementById('mobile-search-button');
    const mobileSearchOverlay = document.getElementById('mobile-search-overlay');
    const mobileDropdownButtons = document.querySelectorAll('.mobile-dropdown-button');
    const dropdowns = document.querySelectorAll('.dropdown');

    // Toggle mobile menu
    mobileMenuButton?.addEventListener('click', () => {
        mobileMenu?.classList.toggle('active');
        // Đóng search overlay khi mở menu
        mobileSearchOverlay.classList.remove('active');
    });

    // Toggle search overlay
    mobileSearchButton?.addEventListener('click', () => {
        mobileSearchOverlay.classList.toggle('active');
        // Đóng mobile menu khi mở search
        mobileMenu.classList.remove('active');
    });

    // Xử lý mobile dropdowns
    mobileDropdownButtons?.forEach(button => {
        button.addEventListener('click', (e) => {
            // Lấy dropdown content
            const dropdownContent = button.nextElementSibling;
            const arrow = button.querySelector('svg');

            // Toggle active class
            dropdownContent.classList.toggle('active');

            // Rotate arrow
            if (dropdownContent.classList.contains('active')) {
                arrow.style.transform = 'rotate(180deg)';
            } else {
                arrow.style.transform = 'rotate(0)';
            }

            // Đóng các dropdown khác
            mobileDropdownButtons.forEach(otherButton => {
                if (otherButton !== button) {
                    const otherContent = otherButton.nextElementSibling;
                    const otherArrow = otherButton.querySelector('svg');
                    otherContent.classList.remove('active');
                    otherArrow.style.transform = 'rotate(0)';
                }
            });
        });
    });

    // Đóng mobile menu và search khi click outside
    document.addEventListener('click', (e) => {
        // Đóng mobile menu
        if (!e.target.closest('#mobile-menu') &&
            !e.target.closest('#mobile-menu-button') &&
            mobileMenu?.classList.contains('active')) {
            mobileMenu?.classList.remove('active');
        }

        // Đóng search overlay
        if (!e.target.closest('#mobile-search-overlay') &&
            !e.target.closest('#mobile-search-button') &&
            mobileSearchOverlay?.classList.contains('active')) {
            mobileSearchOverlay?.classList.remove('active');
        }
    });

    // Close mobile menu and search overlay on window resize
    let timeout;
    window.addEventListener('resize', () => {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            if (window.innerWidth >= 768) { // md breakpoint
                mobileMenu?.classList.remove('active');
                mobileSearchOverlay?.classList.remove('active');
                // Reset all mobile dropdowns
                mobileDropdownButtons.forEach(button => {
                    const dropdownContent = button.nextElementSibling;
                    const arrow = button.querySelector('svg');
                    dropdownContent.classList.remove('active');
                    arrow.style.transform = 'rotate(0)';
                });
            }
        }, 100);
    });

    // Handle keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            // Close mobile menu
            mobileMenu?.classList.remove('active');
            // Close search overlay
            mobileSearchOverlay?.classList.remove('active');
            // Reset all mobile dropdowns
            mobileDropdownButtons?.forEach(button => {
                const dropdownContent = button.nextElementSibling;
                const arrow = button.querySelector('svg');
                dropdownContent.classList.remove('active');
                arrow.style.transform = 'rotate(0)';
            });
        }
    });

    // Focus trap for mobile menu
    const focusTrap = (element) => {
        const focusableElements = element.querySelectorAll(
            'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstFocusableElement = focusableElements[0];
        const lastFocusableElement = focusableElements[focusableElements.length - 1];

        element.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey && document.activeElement === firstFocusableElement) {
                    e.preventDefault();
                    lastFocusableElement.focus();
                } else if (!e.shiftKey && document.activeElement === lastFocusableElement) {
                    e.preventDefault();
                    firstFocusableElement.focus();
                }
            }
        });
    };

    // Apply focus trap to mobile menu
    if (mobileMenu) {
        focusTrap(mobileMenu);
    }
});


document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.querySelector('.search-input');
    const searchResults = document.querySelector('#search-results');
    const searchBackdrop = document.querySelector('.search-backdrop');
    let selectedIndex = -1;
    let searchTimeout;
    let currentResults = [];

    searchInput.addEventListener('input', function () {
        const query = this.value.trim();
        clearTimeout(searchTimeout);

        if (query.length > 0) {
            showLoading();
            searchTimeout = setTimeout(() => {
                fetch(`/suggest.html?query=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            currentResults = data.results;
                            renderSearchResults(data.results, data.search_all_url, query);
                        } else {
                            renderNoResults(data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        renderError();
                    });
            }, 300);
        } else {
            hideResults();
        }
    });

    function showLoading() {
        searchResults.innerHTML = `
                <div class="search-header px-4 py-3">
                    <div class="skeleton h-4 w-48 rounded"></div>
                </div>
                <div class="p-4 space-y-4">
                    ${Array(3).fill().map(() => `
                        <div class="flex items-start space-x-3">
                            <div class="skeleton w-16 h-20 rounded"></div>
                            <div class="flex-1">
                                <div class="skeleton h-4 w-3/4 rounded mb-2"></div>
                                <div class="skeleton h-3 w-1/2 rounded mb-2"></div>
                                <div class="skeleton h-3 w-full rounded"></div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        showResults();
    }

    function highlightText(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }

    function renderSearchResults(results, searchAllUrl, keyword) {
        const title = `
                <div class="search-header px-4 py-3 flex items-center justify-between">
                    <div class="text-sm text-gray-400">
                        Kết quả cho:
                        <span class="font-medium text-white">"${keyword}"</span>
                    </div>
                    <span class="text-xs px-2 py-1 bg-zinc-700 rounded-full text-gray-300">
                        ${results.length} kết quả
                    </span>
                </div>
            `;

        const items = results.map((result, index) => `
                <div class="search-item" data-index="${index}">
                    <a href="${result.url}" class="block p-4 hover:bg-zinc-700/50 transition-colors">
                        <div class="flex items-start space-x-3">
                            <div class="thumbnail-wrapper flex-shrink-0 w-16 h-20">
                                ${result.thumbnail ?
            `<img src="${result.thumbnail}" alt="" class="w-full h-full object-cover">` :
            `<div class="w-full h-full flex items-center justify-center text-gray-500">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                            <path d="M21 15l-5-5L5 21"></path>
                                        </svg>
                                    </div>`
        }
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="font-medium text-white truncate">
                                    ${highlightText(result.name, keyword)}
                                </h3>
                                <p class="text-sm text-gray-400 mt-1">
                                    ${result.original_title}
                                    <span class="inline-block px-2 py-0.5 bg-zinc-700 rounded-full text-xs ml-1">
                                        ${result.publish_year}
                                    </span>
                                </p>
                                ${result.description ?
            `<p class="text-sm text-gray-500 mt-2 line-clamp-2">
                                        ${highlightText(result.description, keyword)}
                                    </p>` : ''
        }
                            </div>
                        </div>
                    </a>
                </div>
            `).join('');

        const footer = `
                <div class="search-footer p-3">
                    <a href="${searchAllUrl}"
                       class="block px-4 py-2 text-center font-medium text-orange-400 hover:text-orange-300
                              bg-zinc-700/50 hover:bg-zinc-700 rounded-lg transition-colors">
                        Xem tất cả kết quả
                    </a>
                </div>
            `;

        searchResults.innerHTML = `${title}${items}${footer}`;
        showResults();
    }

    function renderNoResults(message) {
        searchResults.innerHTML = `
                <div class="p-8 text-center">
                    <div class="text-gray-500 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <p class="text-gray-400">${message}</p>
                </div>
            `;
        showResults();
    }

    function showResults() {
        searchResults.classList.remove('hidden');
        searchBackdrop.classList.add('show');
        setTimeout(() => searchResults.classList.add('show'), 10);
    }

    function hideResults() {
        searchResults.classList.remove('show');
        searchBackdrop.classList.remove('show');
        setTimeout(() => searchResults.classList.add('hidden'), 200);
    }

    function renderError() {
        searchResults.innerHTML = `
                <div class="p-8 text-center">
                    <div class="text-red-500 mb-2">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-400">Có lỗi xảy ra, vui lòng thử lại</p>
                </div>
            `;
        showResults();
    }

    // Close when clicking outside
    searchBackdrop.addEventListener('click', hideResults);

    // Keyboard navigation
    searchInput.addEventListener('keydown', function (e) {
        const items = searchResults.querySelectorAll('.search-item');

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelection(items);
                break;
            case 'ArrowUp':
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection(items);
                break;
            case 'Enter':
                if (selectedIndex >= 0 && items[selectedIndex]) {
                    const link = items[selectedIndex].querySelector('a');
                    if (link) link.click();
                }
                break;
            case 'Escape':
                hideResults();
                break;
        }
    });

    function updateSelection(items) {
        items.forEach((item, index) => {
            if (index === selectedIndex) {
                item.classList.add('selected');
                item.scrollIntoView({block: 'nearest'});
            } else {
                item.classList.remove('selected');
            }
        });
    }

    // Click outside to close
    document.addEventListener('click', function (e) {
        if (!searchResults.contains(e.target) && !searchInput.contains(e.target)) {
            hideResults();
        }
    });
});
