{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./node_modules/autocomplete.js/src/common/utils.js", "webpack://[name]/./src/configure/index.js", "webpack://[name]/./node_modules/autocomplete.js/src/common/dom.js", "webpack://[name]/./node_modules/foreach/index.js", "webpack://[name]/./node_modules/algoliasearch/src/clone.js", "webpack://[name]/(webpack)/buildin/global.js", "webpack://[name]/./src/version.js", "webpack://[name]/./src/icons/address.svg", "webpack://[name]/./src/formatInputValue.js", "webpack://[name]/./src/icons/city.svg", "webpack://[name]/./src/icons/country.svg", "webpack://[name]/./src/icons/bus.svg", "webpack://[name]/./src/icons/train.svg", "webpack://[name]/./src/icons/townhall.svg", "webpack://[name]/./src/icons/plane.svg", "webpack://[name]/./src/formatDropdownValue.js", "webpack://[name]/./src/icons/algolia.svg", "webpack://[name]/./src/icons/osm.svg", "webpack://[name]/./src/defaultTemplates.js", "webpack://[name]/./src/findCountryCode.js", "webpack://[name]/./src/findType.js", "webpack://[name]/./src/formatHit.js", "webpack://[name]/./node_modules/process/browser.js", "webpack://[name]/./node_modules/algoliasearch/src/errors.js", "webpack://[name]/./node_modules/algoliasearch/node_modules/isarray/index.js", "webpack://[name]/./node_modules/algoliasearch/src/map.js", "webpack://[name]/./node_modules/algoliasearch/node_modules/debug/src/browser.js", "webpack://[name]/./src/places.css", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/event_emitter.js", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/css.js", "webpack://[name]/./node_modules/insert-css/index.js", "webpack://[name]/./src/createAutocompleteSource.js", "webpack://[name]/./src/createAutocompleteDataset.js", "webpack://[name]/./node_modules/algoliasearch/src/browser/builds/algoliasearchLite.js", "webpack://[name]/./node_modules/inherits/inherits_browser.js", "webpack://[name]/./node_modules/algoliasearch/src/buildSearchMethod.js", "webpack://[name]/./node_modules/algoliasearch/src/omit.js", "webpack://[name]/./node_modules/object-keys/isArguments.js", "webpack://[name]/./node_modules/querystring-es3/encode.js", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/event_bus.js", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/html.js", "webpack://[name]/./node_modules/autocomplete.js/version.js", "webpack://[name]/./node_modules/autocomplete.js/src/common/parseAlgoliaClientVersion.js", "webpack://[name]/./src/navigatorLanguage.js", "webpack://[name]/./node_modules/events/events.js", "webpack://[name]/./node_modules/autocomplete.js/index.js", "webpack://[name]/./src/icons/clear.svg", "webpack://[name]/./src/errors.js", "webpack://[name]/./src/createReverseGeocodingSource.js", "webpack://[name]/./src/places.js", "webpack://[name]/./node_modules/algoliasearch/src/AlgoliaSearchCore.js", "webpack://[name]/./node_modules/algoliasearch/src/exitPromise.js", "webpack://[name]/./node_modules/algoliasearch/src/IndexCore.js", "webpack://[name]/./node_modules/algoliasearch/src/deprecate.js", "webpack://[name]/./node_modules/algoliasearch/src/deprecatedMessage.js", "webpack://[name]/./node_modules/algoliasearch/src/merge.js", "webpack://[name]/./node_modules/object-keys/index.js", "webpack://[name]/./node_modules/object-keys/implementation.js", "webpack://[name]/./node_modules/algoliasearch/src/store.js", "webpack://[name]/./node_modules/algoliasearch/node_modules/debug/src/debug.js", "webpack://[name]/./node_modules/algoliasearch/node_modules/ms/index.js", "webpack://[name]/./node_modules/algoliasearch/src/browser/createAlgoliasearch.js", "webpack://[name]/./node_modules/global/window.js", "webpack://[name]/./node_modules/es6-promise/dist/es6-promise.js", "webpack://[name]/./node_modules/algoliasearch/src/browser/inline-headers.js", "webpack://[name]/./node_modules/algoliasearch/src/browser/jsonp-request.js", "webpack://[name]/./node_modules/algoliasearch/src/places.js", "webpack://[name]/./node_modules/querystring-es3/index.js", "webpack://[name]/./node_modules/querystring-es3/decode.js", "webpack://[name]/./node_modules/algoliasearch/src/version.js", "webpack://[name]/./node_modules/autocomplete.js/src/standalone/index.js", "webpack://[name]/./node_modules/autocomplete.js/zepto.js", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/typeahead.js", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/input.js", "webpack://[name]/./node_modules/immediate/lib/index.js", "webpack://[name]/./node_modules/immediate/lib/nextTick.js", "webpack://[name]/./node_modules/immediate/lib/mutation.js", "webpack://[name]/./node_modules/immediate/lib/messageChannel.js", "webpack://[name]/./node_modules/immediate/lib/stateChange.js", "webpack://[name]/./node_modules/immediate/lib/timeout.js", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/dropdown.js", "webpack://[name]/./node_modules/autocomplete.js/src/autocomplete/dataset.js", "webpack://[name]/./node_modules/autocomplete.js/src/sources/index.js", "webpack://[name]/./node_modules/autocomplete.js/src/sources/hits.js", "webpack://[name]/./node_modules/autocomplete.js/src/sources/popularIn.js", "webpack://[name]/./index.js"], "names": ["extractParams", "hitsPerPage", "postcodeSearch", "aroundLatLng", "aroundRadius", "aroundLatLngViaIP", "insideBoundingBox", "insidePolygon", "getRankingInfo", "countries", "language", "type", "extracted", "navigator", "split", "Array", "isArray", "map", "country", "toLowerCase", "undefined", "restrictSearchableAttributes", "extractControls", "useDeviceLocation", "computeQueryParams", "params", "formatInputValue", "onHits", "onError", "e", "onRateLimitReached", "onInvalidCredentials", "controls", "configure", "configuration", "administrative", "city", "name", "out", "replace", "trim", "icons", "address", "addressIcon", "cityIcon", "countryIcon", "busStop", "busIcon", "trainStation", "trainIcon", "townhall", "townhallIcon", "airport", "planeIcon", "formatDropdownValue", "highlight", "filter", "token", "join", "footer", "algoliaLogo", "osmLogo", "value", "suggestion", "findCountryCode", "tags", "tagIndex", "length", "tag", "find", "match", "findType", "types", "t", "indexOf", "getBestHighlightedForm", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "bestAttributes", "i", "matchLevel", "push", "index", "words", "matched<PERSON>ords", "sort", "a", "b", "getBestPostcode", "postcodes", "highlightedPostcodes", "postcode", "highlightedPostcode", "formatHit", "hit", "hitIndex", "query", "rawAnswer", "locale_names", "suburb", "county", "_highlightResult", "countryCode", "_tags", "latlng", "lat", "_geoloc", "lng", "console", "error", "createAutocompleteSource", "algoliasearch", "clientOptions", "<PERSON><PERSON><PERSON><PERSON>", "appId", "placesClient", "initPlaces", "as", "addAlgoliaAgent", "version", "userCoords", "tracker", "geolocation", "watchPosition", "coords", "latitude", "longitude", "searcher", "cb", "searchParams", "search", "then", "content", "hits", "statusCode", "message", "partial", "updated", "clearWatch", "createAutocompleteDataset", "options", "templates", "defaultTemplates", "source", "displayKey", "cache", "userLanguage", "String", "prototype", "toUpperCase", "multiContainers", "<PERSON><PERSON><PERSON><PERSON>", "rateLimitReached", "invalidCredentials", "invalidAppId", "filterApplicableParams", "filtered", "createReverseGeocodingSource", "queryAroundLatLng", "finalAroundLatLng", "Error", "Promise", "reject", "reverse", "insertCss", "css", "prepend", "applyAttributes", "elt", "attrs", "Object", "entries", "for<PERSON>ach", "setAttribute", "places", "container", "style", "accessibility", "autocompleteOptions", "userAutocompleteOptions", "NodeList", "errors", "resolvedContainer", "document", "querySelectorAll", "HTMLInputElement", "placesInstance", "EventEmitter", "prefix", "autoselect", "hint", "cssClasses", "root", "debug", "process", "autocompleteDataset", "emit", "suggestions", "listeners", "listenerCount", "log", "startsWith", "autocompleteInstance", "autocomplete", "autocompleteContainer", "parentNode", "autocompleteChangeEvents", "eventName", "on", "_", "suggestionIndex", "clear", "createElement", "clearButton", "classList", "add", "innerHTML", "clearIcon", "append<PERSON><PERSON><PERSON>", "display", "pin", "pinButton", "pinIcon", "addEventListener", "focus", "setVal", "<PERSON><PERSON><PERSON>y", "inputListener", "val", "querySelector", "autocompleteIsomorphicMethods", "methodName", "getVal", "destroy", "removeEventListener", "resolve", "safeConfig", "require", "module", "exports"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;AClFa;;AAEb,UAAU,mBAAO,CAAC,CAAU;;AAE5B;AACA,iCAAiC,EAAE;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oCAAoC,mCAAmC;AACvE;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;AACA;AACA,GAAG;;AAEH;AACA;AACA,mCAAmC,EAAE;AACrC,GAAG;;AAEH,2BAA2B,gCAAgC,EAAE;;AAE7D;AACA;AACA,GAAG;;AAEH;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;;AAEH;AACA;AACA,uBAAuB,kBAAkB;AACzC,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,yBAAyB;AAC3D;AACA,gCAAgC,oBAAoB;AACpD,GAAG;;AAEH,uBAAuB,mBAAmB,EAAE;;AAE5C,qBAAqB;;AAErB;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClIA,IAAMA,aAAa,GAAG,SAAhBA,aAAgB,OAYhB;AAAA,MAXJC,WAWI,QAXJA,WAWI;AAAA,MAVJC,cAUI,QAVJA,cAUI;AAAA,MATJC,YASI,QATJA,YASI;AAAA,MARJC,YAQI,QARJA,YAQI;AAAA,MAPJC,iBAOI,QAPJA,iBAOI;AAAA,MANJC,iBAMI,QANJA,iBAMI;AAAA,MALJC,aAKI,QALJA,aAKI;AAAA,MAJJC,cAII,QAJJA,cAII;AAAA,MAHJC,SAGI,QAHJA,SAGI;AAAA,MAFJC,QAEI,QAFJA,QAEI;AAAA,MADJC,IACI,QADJA,IACI;AACJ,MAAMC,SAAS,GAAG;AAChBH,aAAS,EAATA,SADgB;AAEhBR,eAAW,EAAEA,WAAW,IAAI,CAFZ;AAGhBS,YAAQ,EAAEA,QAAQ,IAAIG,SAAS,CAACH,QAAV,CAAmBI,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAHN;AAIhBH,QAAI,EAAJA;AAJgB,GAAlB;;AAOA,MAAII,KAAK,CAACC,OAAN,CAAcP,SAAd,CAAJ,EAA8B;AAC5BG,aAAS,CAACH,SAAV,GAAsBG,SAAS,CAACH,SAAV,CAAoBQ,GAApB,CAAwB,UAACC,OAAD;AAAA,aAC5CA,OAAO,CAACC,WAAR,EAD4C;AAAA,KAAxB,CAAtB;AAGD;;AAED,MAAI,OAAOP,SAAS,CAACF,QAAjB,KAA8B,QAAlC,EAA4C;AAC1CE,aAAS,CAACF,QAAV,GAAqBE,SAAS,CAACF,QAAV,CAAmBS,WAAnB,EAArB;AACD;;AAED,MAAIhB,YAAJ,EAAkB;AAChBS,aAAS,CAACT,YAAV,GAAyBA,YAAzB;AACD,GAFD,MAEO,IAAIE,iBAAiB,KAAKe,SAA1B,EAAqC;AAC1CR,aAAS,CAACP,iBAAV,GAA8BA,iBAA9B;AACD;;AAED,MAAIH,cAAJ,EAAoB;AAClBU,aAAS,CAACS,4BAAV,GAAyC,UAAzC;AACD;;AAED,yCACKT,SADL;AAEER,gBAAY,EAAZA,YAFF;AAGEE,qBAAiB,EAAjBA,iBAHF;AAIEC,iBAAa,EAAbA,aAJF;AAKEC,kBAAc,EAAdA;AALF;AAOD,CA/CD;;AAiDA,IAAMc,eAAe,GAAG,SAAlBA,eAAkB;AAAA,oCACtBC,iBADsB;AAAA,MACtBA,iBADsB,sCACF,KADE;AAAA,oCAEtBC,kBAFsB;AAAA,MAEtBA,kBAFsB,sCAED,UAACC,MAAD;AAAA,WAAYA,MAAZ;AAAA,GAFC;AAAA,MAGtBC,gBAHsB,SAGtBA,gBAHsB;AAAA,2BAItBC,MAJsB;AAAA,MAItBA,MAJsB,6BAIb,YAAM,CAAE,CAJK;AAAA,4BAKtBC,OALsB;AAAA,MAKtBA,OALsB,8BAKZ,UAACC,CAAD,EAAO;AACf,UAAMA,CAAN;AACD,GAPqB;AAAA,MAQtBC,kBARsB,SAQtBA,kBARsB;AAAA,MAStBC,oBATsB,SAStBA,oBATsB;AAAA,SAUjB;AACLR,qBAAiB,EAAjBA,iBADK;AAELC,sBAAkB,EAAlBA,kBAFK;AAGLE,oBAAgB,EAAhBA,gBAHK;AAILC,UAAM,EAANA,MAJK;AAKLC,WAAO,EAAPA,OALK;AAMLE,sBAAkB,EAAlBA,kBANK;AAOLC,wBAAoB,EAApBA;AAPK,GAViB;AAAA,CAAxB;;AAoBA,IAAIN,MAAM,GAAG,EAAb;AACA,IAAIO,QAAQ,GAAG,EAAf;;AAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,CAACC,aAAD,EAAmB;AACnCT,QAAM,GAAGzB,aAAa,iCAAMyB,MAAN,GAAiBS,aAAjB,EAAtB;AACAF,UAAQ,GAAGV,eAAe,iCAAMU,QAAN,GAAmBE,aAAnB,EAA1B;AAEA,SAAO;AAAET,UAAM,EAANA,MAAF;AAAUO,YAAQ,EAARA;AAAV,GAAP;AACD,CALD;;AAOeC,kEAAf,E;;;;;;;AC/Ea;;AAEb;AACA;AACA;;;;;;;;ACHA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,OAAO;AAC9B;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACpBA;AACA;AACA;;;;;;;ACFA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;ACnBA;AAAe,uEAAf,E;;;;;;;ACAe,8TAAqQ,E;;;;;;;;;ACArQ,SAASP,gBAAT,OAMZ;AAAA,MALDS,cAKC,QALDA,cAKC;AAAA,MAJDC,IAIC,QAJDA,IAIC;AAAA,MAHDlB,OAGC,QAHDA,OAGC;AAAA,MAFDmB,IAEC,QAFDA,IAEC;AAAA,MADD1B,IACC,QADDA,IACC;AACD,MAAM2B,GAAG,GAAG,UAAGD,IAAH,SAAU1B,IAAI,KAAK,SAAT,IAAsBO,OAAO,KAAKE,SAAlC,GAA8C,GAA9C,GAAoD,EAA9D,gBACXgB,IAAI,aAAMA,IAAN,SAAgB,EADT,gBAEXD,cAAc,aAAMA,cAAN,SAA0B,EAF7B,gBAGXjB,OAAO,GAAGA,OAAH,GAAa,EAHT,EAITqB,OAJS,CAID,WAJC,EAIY,GAJZ,EAKTC,IALS,EAAZ;AAMA,SAAOF,GAAP;AACD,C;;;;;ACdc,0TAAiR,E;;ACAjR,+cAAma,E;;ACAna,8/BAAs9B,E;;ACAt9B,u5BAA62B,E;;ACA72B,+fAAkd,E;;ACAld,8mBAAokB,E;;ACAnlB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAMG,KAAK,GAAG;AACZC,SAAO,EAAEC,0BADG;AAEZP,MAAI,EAAEQ,IAFM;AAGZ1B,SAAO,EAAE2B,OAHG;AAIZC,SAAO,EAAEC,GAJG;AAKZC,cAAY,EAAEC,KALF;AAMZC,UAAQ,EAAEC,QANE;AAOZC,SAAO,EAAEC,KAASA;AAPN,CAAd;AAUe,SAASC,mBAAT,OAAkD;AAAA,MAAnB3C,IAAmB,QAAnBA,IAAmB;AAAA,MAAb4C,SAAa,QAAbA,SAAa;AAAA,MACvDlB,IADuD,GACfkB,SADe,CACvDlB,IADuD;AAAA,MACjDF,cADiD,GACfoB,SADe,CACjDpB,cADiD;AAAA,MACjCC,IADiC,GACfmB,SADe,CACjCnB,IADiC;AAAA,MAC3BlB,OAD2B,GACfqC,SADe,CAC3BrC,OAD2B;AAG/D,MAAMoB,GAAG,GAAG,6CAAoCG,KAAK,CAAC9B,IAAD,CAAL,CAAY6B,IAAZ,EAApC,8CACUH,IADV,qDAGV,CAACD,IAAD,EAAOD,cAAP,EAAuBjB,OAAvB,EACCsC,MADD,CACQ,UAACC,KAAD;AAAA,WAAWA,KAAK,KAAKrC,SAArB;AAAA,GADR,EAECsC,IAFD,CAEM,IAFN,CAHU,aAKWnB,OALX,CAKmB,WALnB,EAKgC,GALhC,CAAZ;AAOA,SAAOD,GAAP;AACD,C;;AC7Bc,omQAAwjQ,E;;ACAxjQ,ioBAAylB,E;;ACAxmB;AACA;AACA;AACA;AAEe;AACbqB,QAAM,4IACyFC,OAAW,CAACpB,IAAZ,EADzF,6LAE2JqB,GAAO,CAACrB,IAAR,EAF3J,qCADO;AAKbsB,OAAK,EAALA,gBALa;AAMbC,YAAU,EAAVA,mBAAUA;AANG,CAAf,E;;;;;;;;;;;;ACLe,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;AAC5C,OAAK,IAAIC,QAAQ,GAAG,CAApB,EAAuBA,QAAQ,GAAGD,IAAI,CAACE,MAAvC,EAA+CD,QAAQ,EAAvD,EAA2D;AACzD,QAAME,GAAG,GAAGH,IAAI,CAACC,QAAD,CAAhB;AACA,QAAMG,IAAI,GAAGD,GAAG,CAACE,KAAJ,CAAU,gBAAV,CAAb;;AACA,QAAID,IAAJ,EAAU;AACR,aAAOA,IAAI,CAAC,CAAD,CAAX;AACD;AACF;;AAED,SAAOjD,SAAP;AACD,C;;ACVc,SAASmD,QAAT,CAAkBN,IAAlB,EAAwB;AACrC,MAAMO,KAAK,GAAG;AACZtD,WAAO,EAAE,SADG;AAEZkB,QAAI,EAAE,MAFM;AAGZ,2BAAuB,SAHX;AAIZ,wBAAoB,UAJR;AAKZ,uBAAmB,cALP;AAMZ,yBAAqB,SANT;AAOZ,wBAAoB,SAPR;AAQZ,oBAAgB;AARJ,GAAd;;AAWA,OAAK,IAAMqC,CAAX,IAAgBD,KAAhB,EAAuB;AACrB,QAAIP,IAAI,CAACS,OAAL,CAAaD,CAAb,MAAoB,CAAC,CAAzB,EAA4B;AAC1B,aAAOD,KAAK,CAACC,CAAD,CAAZ;AACD;AACF;;AAED,SAAO,SAAP;AACD,C;;;;;;;;ACnBD;AACA;;AAEA,SAASE,sBAAT,CAAgCC,iBAAhC,EAAmD;AACjD,MAAMC,YAAY,GAAGD,iBAAiB,CAAC,CAAD,CAAjB,CAAqBd,KAA1C,CADiD,CAEjD;;AACA,MAAMgB,cAAc,GAAG,EAAvB;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,iBAAiB,CAACT,MAAtC,EAA8C,EAAEY,CAAhD,EAAmD;AACjD,QAAIH,iBAAiB,CAACG,CAAD,CAAjB,CAAqBC,UAArB,KAAoC,MAAxC,EAAgD;AAC9CF,oBAAc,CAACG,IAAf,CAAoB;AAClBC,aAAK,EAAEH,CADW;AAElBI,aAAK,EAAEP,iBAAiB,CAACG,CAAD,CAAjB,CAAqBK;AAFV,OAApB;AAID;AACF,GAXgD,CAYjD;;;AACA,MAAIN,cAAc,CAACX,MAAf,KAA0B,CAA9B,EAAiC;AAC/B,WAAOU,YAAP;AACD,GAfgD,CAgBjD;;;AACAC,gBAAc,CAACO,IAAf,CAAoB,UAACC,CAAD,EAAIC,CAAJ,EAAU;AAC5B,QAAID,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AACrB,aAAO,CAAC,CAAR;AACD,KAFD,MAEO,IAAIG,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AAC5B,aAAO,CAAP;AACD;;AACD,WAAOG,CAAC,CAACJ,KAAF,GAAUK,CAAC,CAACL,KAAnB;AACD,GAPD,EAjBiD,CAyBjD;;AACA,SAAOJ,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAlB,KAA4B,CAA5B,aACAL,YADA,eACiBD,iBAAiB,CAACE,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAAjB,CAA2CpB,KAD5D,mBAEAc,iBAAiB,CAACE,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAAjB,CAA2CpB,KAF3C,eAEqDe,YAFrD,MAAP;AAGD;;AAED,SAASW,eAAT,CAAyBC,SAAzB,EAAoCC,oBAApC,EAA0D;AACxD,MAAMb,YAAY,GAAGa,oBAAoB,CAAC,CAAD,CAApB,CAAwB5B,KAA7C,CADwD,CAExD;;AACA,MAAMgB,cAAc,GAAG,EAAvB;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,oBAAoB,CAACvB,MAAzC,EAAiD,EAAEY,CAAnD,EAAsD;AACpD,QAAIW,oBAAoB,CAACX,CAAD,CAApB,CAAwBC,UAAxB,KAAuC,MAA3C,EAAmD;AACjDF,oBAAc,CAACG,IAAf,CAAoB;AAClBC,aAAK,EAAEH,CADW;AAElBI,aAAK,EAAEO,oBAAoB,CAACX,CAAD,CAApB,CAAwBK;AAFb,OAApB;AAID;AACF,GAXuD,CAYxD;;;AACA,MAAIN,cAAc,CAACX,MAAf,KAA0B,CAA9B,EAAiC;AAC/B,WAAO;AAAEwB,cAAQ,EAAEF,SAAS,CAAC,CAAD,CAArB;AAA0BG,yBAAmB,EAAEf;AAA/C,KAAP;AACD,GAfuD,CAgBxD;;;AACAC,gBAAc,CAACO,IAAf,CAAoB,UAACC,CAAD,EAAIC,CAAJ,EAAU;AAC5B,QAAID,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AACrB,aAAO,CAAC,CAAR;AACD,KAFD,MAEO,IAAIG,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AAC5B,aAAO,CAAP;AACD;;AACD,WAAOG,CAAC,CAACJ,KAAF,GAAUK,CAAC,CAACL,KAAnB;AACD,GAPD;AASA,MAAMS,QAAQ,GAAGF,SAAS,CAACX,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAA1B;AACA,SAAO;AACLS,YAAQ,EAARA,QADK;AAELC,uBAAmB,EAAEF,oBAAoB,CAACZ,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAApB,CAA8CpB;AAF9D,GAAP;AAID;;AAEc,SAAS+B,SAAT,OAMZ;AAAA,MALDnE,gBAKC,QALDA,gBAKC;AAAA,MAJDoE,GAIC,QAJDA,GAIC;AAAA,MAHDC,QAGC,QAHDA,QAGC;AAAA,MAFDC,KAEC,QAFDA,KAEC;AAAA,MADDC,SACC,QADDA,SACC;;AACD,MAAI;AACF,QAAM5D,IAAI,GAAGyD,GAAG,CAACI,YAAJ,CAAiB,CAAjB,CAAb;AACA,QAAMhF,OAAO,GAAG4E,GAAG,CAAC5E,OAApB;AACA,QAAMiB,cAAc,GAClB2D,GAAG,CAAC3D,cAAJ,IAAsB2D,GAAG,CAAC3D,cAAJ,CAAmB,CAAnB,MAA0BE,IAAhD,GACIyD,GAAG,CAAC3D,cAAJ,CAAmB,CAAnB,CADJ,GAEIf,SAHN;AAIA,QAAMgB,IAAI,GAAG0D,GAAG,CAAC1D,IAAJ,IAAY0D,GAAG,CAAC1D,IAAJ,CAAS,CAAT,MAAgBC,IAA5B,GAAmCyD,GAAG,CAAC1D,IAAJ,CAAS,CAAT,CAAnC,GAAiDhB,SAA9D;AACA,QAAM+E,MAAM,GACVL,GAAG,CAACK,MAAJ,IAAcL,GAAG,CAACK,MAAJ,CAAW,CAAX,MAAkB9D,IAAhC,GAAuCyD,GAAG,CAACK,MAAJ,CAAW,CAAX,CAAvC,GAAuD/E,SADzD;AAGA,QAAMgF,MAAM,GACVN,GAAG,CAACM,MAAJ,IAAcN,GAAG,CAACM,MAAJ,CAAW,CAAX,MAAkB/D,IAAhC,GAAuCyD,GAAG,CAACM,MAAJ,CAAW,CAAX,CAAvC,GAAuDhF,SADzD;;AAXE,gBAeA0E,GAAG,CAACH,QAAJ,IAAgBG,GAAG,CAACH,QAAJ,CAAaxB,MAA7B,GACIqB,eAAe,CAACM,GAAG,CAACH,QAAL,EAAeG,GAAG,CAACO,gBAAJ,CAAqBV,QAApC,CADnB,GAEI;AAAEA,cAAQ,EAAEvE,SAAZ;AAAuBwE,yBAAmB,EAAExE;AAA5C,KAjBJ;AAAA,QAcMuE,QAdN,SAcMA,QAdN;AAAA,QAcgBC,mBAdhB,SAcgBA,mBAdhB;;AAmBF,QAAMrC,SAAS,GAAG;AAChBlB,UAAI,EAAEsC,sBAAsB,CAACmB,GAAG,CAACO,gBAAJ,CAAqBH,YAAtB,CADZ;AAEhB9D,UAAI,EAAEA,IAAI,GACNuC,sBAAsB,CAACmB,GAAG,CAACO,gBAAJ,CAAqBjE,IAAtB,CADhB,GAENhB,SAJY;AAKhBe,oBAAc,EAAEA,cAAc,GAC1BwC,sBAAsB,CAACmB,GAAG,CAACO,gBAAJ,CAAqBlE,cAAtB,CADI,GAE1Bf,SAPY;AAQhBF,aAAO,EAAEA,OAAO,GAAG4E,GAAG,CAACO,gBAAJ,CAAqBnF,OAArB,CAA6B4C,KAAhC,GAAwC1C,SARxC;AAShB+E,YAAM,EAAEA,MAAM,GACVxB,sBAAsB,CAACmB,GAAG,CAACO,gBAAJ,CAAqBF,MAAtB,CADZ,GAEV/E,SAXY;AAYhBgF,YAAM,EAAEA,MAAM,GACVzB,sBAAsB,CAACmB,GAAG,CAACO,gBAAJ,CAAqBD,MAAtB,CADZ,GAEVhF,SAdY;AAehBuE,cAAQ,EAAEC;AAfM,KAAlB;AAkBA,QAAM7B,UAAU,GAAG;AACjB1B,UAAI,EAAJA,IADiB;AAEjBF,oBAAc,EAAdA,cAFiB;AAGjBiE,YAAM,EAANA,MAHiB;AAIjBhE,UAAI,EAAJA,IAJiB;AAKjB+D,YAAM,EAANA,MALiB;AAMjBjF,aAAO,EAAPA,OANiB;AAOjBoF,iBAAW,EAAEtC,eAAe,CAAC8B,GAAG,CAACS,KAAL,CAPX;AAQjB5F,UAAI,EAAE4D,QAAQ,CAACuB,GAAG,CAACS,KAAL,CARG;AASjBC,YAAM,EAAE;AACNC,WAAG,EAAEX,GAAG,CAACY,OAAJ,CAAYD,GADX;AAENE,WAAG,EAAEb,GAAG,CAACY,OAAJ,CAAYC;AAFX,OATS;AAajBhB,cAAQ,EAARA,QAbiB;AAcjBF,eAAS,EAAEK,GAAG,CAACH,QAAJ,IAAgBG,GAAG,CAACH,QAAJ,CAAaxB,MAA7B,GAAsC2B,GAAG,CAACH,QAA1C,GAAqDvE;AAd/C,KAAnB,CArCE,CAsDF;;AACA,QAAM0C,KAAK,GAAGpC,gBAAgB,CAACqC,UAAD,CAA9B;AAEA,2CACKA,UADL;AAEER,eAAS,EAATA,SAFF;AAGEuC,SAAG,EAAHA,GAHF;AAIEC,cAAQ,EAARA,QAJF;AAKEC,WAAK,EAALA,KALF;AAMEC,eAAS,EAATA,SANF;AAOEnC,WAAK,EAALA;AAPF;AASD,GAlED,CAkEE,OAAOjC,CAAP,EAAU;AACV;AACA+E,WAAO,CAACC,KAAR,CAAc,wBAAd,EAAwCf,GAAxC;AACAc,WAAO,CAACC,KAAR,CAAchF,CAAd;AACA;;AACA,WAAO;AACLiC,WAAK,EAAE;AADF,KAAP;AAGD;AACF,C;;;;;;ACrJD;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;;AAErC;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA,4BAA4B,UAAU;;;;;;;;ACvLzB;;AAEb;AACA;AACA;;AAEA,eAAe,mBAAO,CAAC,EAAU;;AAEjC;AACA,gBAAgB,mBAAO,CAAC,CAAS;;AAEjC;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACrFA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;ACJA,cAAc,mBAAO,CAAC,CAAS;;AAE/B;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;;;;;ACRA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B,mBAAO,CAAC,EAAS;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,YAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;ACxLe,2EAAkB,gBAAgB,GAAG,yBAAyB,gBAAgB,wBAAwB,uBAAuB,sBAAsB,iBAAiB,2BAA2B,uBAAuB,kBAAkB,kBAAkB,qBAAqB,6BAA6B,2BAA2B,GAAG,0CAA0C,6BAA6B,GAAG,0BAA0B,kBAAkB,GAAG,4GAA4G,kBAAkB,GAAG,uBAAuB,gBAAgB,wBAAwB,8EAA8E,uBAAuB,oBAAoB,qBAAqB,GAAG,oBAAoB,oBAAoB,iBAAiB,sBAAsB,uBAAuB,qBAAqB,GAAG,uBAAuB,sBAAsB,uBAAuB,GAAG,iBAAiB,uBAAuB,sBAAsB,mBAAmB,GAAG,yBAAyB,uBAAuB,gBAAgB,iBAAiB,2BAA2B,GAAG,6BAA6B,qBAAqB,kDAAkD,kDAAkD,kBAAkB,GAAG,oBAAoB,cAAc,4BAA4B,uBAAuB,WAAW,cAAc,gBAAgB,kBAAkB,GAAG,gCAAgC,oBAAoB,GAAG,wBAAwB,kBAAkB,uBAAuB,aAAa,aAAa,wCAAwC,wCAAwC,GAAG,gBAAgB,wBAAwB,GAAG,wCAAwC,gDAAgD,gDAAgD,kBAAkB,GAAG,gBAAgB,gBAAgB,sBAAsB,6BAA6B,oBAAoB,sBAAsB,GAAG,kBAAkB,mBAAmB,0BAA0B,GAAG,sBAAsB,2BAA2B,GAAG,sBAAsB,eAAe,GAAG,GAAG,E;;;;;;;ACA1qE;;AAEb,gBAAgB,mBAAO,CAAC,EAAW;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,sDAAsD;AACtD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,yEAAyE;AACzE;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,2CAA2C,uBAAuB;AAClE;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,gDAAgD;AAChE;;;;;;;;ACrGa;;AAEb,QAAQ,mBAAO,CAAC,CAAoB;;AAEpC;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,aAAa;AACb,uBAAuB;AACvB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,sBAAsB,kBAAkB;AACxC;;AAEA;;;;;;;AChGA,oBAAoB;AACpB,uBAAuB,gBAAgB;;AAEvC;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA,uCAAuC,iCAAiC;;AAExE;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzDA;AACA;AACA;AAEe,SAASgD,wBAAT,OAyBZ;AAAA,MAxBDC,aAwBC,QAxBDA,aAwBC;AAAA,MAvBDC,aAuBC,QAvBDA,aAuBC;AAAA,MAtBDC,MAsBC,QAtBDA,MAsBC;AAAA,MArBDC,KAqBC,QArBDA,KAqBC;AAAA,MApBDjH,WAoBC,QApBDA,WAoBC;AAAA,MAnBDC,cAmBC,QAnBDA,cAmBC;AAAA,MAlBDC,YAkBC,QAlBDA,YAkBC;AAAA,MAjBDC,YAiBC,QAjBDA,YAiBC;AAAA,MAhBDC,iBAgBC,QAhBDA,iBAgBC;AAAA,MAfDC,iBAeC,QAfDA,iBAeC;AAAA,MAdDC,aAcC,QAdDA,aAcC;AAAA,MAbDC,cAaC,QAbDA,cAaC;AAAA,MAZDC,SAYC,QAZDA,SAYC;AAAA,MAXDiB,gBAWC,QAXDA,gBAWC;AAAA,mCAVDF,kBAUC;AAAA,MAVDA,kBAUC,sCAVoB,UAACC,MAAD;AAAA,WAAYA,MAAZ;AAAA,GAUpB;AAAA,mCATDF,iBASC;AAAA,MATDA,iBASC,sCATmB,KASnB;AAAA,2BARDb,QAQC;AAAA,MARDA,QAQC,8BARUG,SAAS,CAACH,QAAV,CAAmBI,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAQV;AAAA,yBAPDa,MAOC;AAAA,MAPDA,MAOC,4BAPQ,YAAM,CAAE,CAOhB;AAAA,0BANDC,OAMC;AAAA,MANDA,OAMC,6BANS,UAACC,CAAD,EAAO;AACf,UAAMA,CAAN;AACD,GAIA;AAAA,MAHDC,kBAGC,QAHDA,kBAGC;AAAA,MAFDC,oBAEC,QAFDA,oBAEC;AAAA,MADDpB,IACC,QADDA,IACC;AACD,MAAMwG,YAAY,GAAGJ,aAAa,CAACK,UAAd,CAAyBF,KAAzB,EAAgCD,MAAhC,EAAwCD,aAAxC,CAArB;AACAG,cAAY,CAACE,EAAb,CAAgBC,eAAhB,0BAAkDC,kBAAlD;AAEA,MAAMrF,aAAa,GAAGD,oCAAS,CAAC;AAC9BhC,eAAW,EAAXA,WAD8B;AAE9BU,QAAI,EAAJA,IAF8B;AAG9BT,kBAAc,EAAdA,cAH8B;AAI9BO,aAAS,EAATA,SAJ8B;AAK9BC,YAAQ,EAARA,QAL8B;AAM9BP,gBAAY,EAAZA,YAN8B;AAO9BC,gBAAY,EAAZA,YAP8B;AAQ9BC,qBAAiB,EAAjBA,iBAR8B;AAS9BC,qBAAiB,EAAjBA,iBAT8B;AAU9BC,iBAAa,EAAbA,aAV8B;AAW9BC,kBAAc,EAAdA,cAX8B;AAY9BkB,oBAAgB,EAAhBA,gBAZ8B;AAa9BF,sBAAkB,EAAlBA,kBAb8B;AAc9BD,qBAAiB,EAAjBA,iBAd8B;AAe9BI,UAAM,EAANA,MAf8B;AAgB9BC,WAAO,EAAPA,OAhB8B;AAiB9BE,sBAAkB,EAAlBA,kBAjB8B;AAkB9BC,wBAAoB,EAApBA;AAlB8B,GAAD,CAA/B;AAqBA,MAAIN,MAAM,GAAGS,aAAa,CAACT,MAA3B;AACA,MAAIO,QAAQ,GAAGE,aAAa,CAACF,QAA7B;AAEA,MAAIwF,UAAJ;AACA,MAAIC,OAAO,GAAG,IAAd;;AAEA,MAAIzF,QAAQ,CAACT,iBAAb,EAAgC;AAC9BkG,WAAO,GAAG5G,SAAS,CAAC6G,WAAV,CAAsBC,aAAtB,CAAoC,iBAAgB;AAAA,UAAbC,MAAa,SAAbA,MAAa;AAC5DJ,gBAAU,aAAMI,MAAM,CAACC,QAAb,cAAyBD,MAAM,CAACE,SAAhC,CAAV;AACD,KAFS,CAAV;AAGD;;AAED,WAASC,QAAT,CAAkB/B,KAAlB,EAAyBgC,EAAzB,EAA6B;AAC3B,QAAMC,YAAY,mCACbxG,MADa;AAEhBuE,WAAK,EAALA;AAFgB,MAAlB;;AAKA,QAAIwB,UAAJ,EAAgB;AACdS,kBAAY,CAAC9H,YAAb,GAA4BqH,UAA5B;AACD;;AAED,WAAOL,YAAY,CAChBe,MADI,CACGlG,QAAQ,CAACR,kBAAT,CAA4ByG,YAA5B,CADH,EAEJE,IAFI,CAEC,UAACC,OAAD,EAAa;AACjB,UAAMC,IAAI,GAAGD,OAAO,CAACC,IAAR,CAAapH,GAAb,CAAiB,UAAC6E,GAAD,EAAMC,QAAN;AAAA,eAC5BF,oCAAS,CAAC;AACRnE,0BAAgB,EAAEM,QAAQ,CAACN,gBADnB;AAERoE,aAAG,EAAHA,GAFQ;AAGRC,kBAAQ,EAARA,QAHQ;AAIRC,eAAK,EAALA,KAJQ;AAKRC,mBAAS,EAAEmC;AALH,SAAD,CADmB;AAAA,OAAjB,CAAb;AAUApG,cAAQ,CAACL,MAAT,CAAgB;AACd0G,YAAI,EAAJA,IADc;AAEdrC,aAAK,EAALA,KAFc;AAGdC,iBAAS,EAAEmC;AAHG,OAAhB;AAMA,aAAOC,IAAP;AACD,KApBI,EAqBJF,IArBI,CAqBCH,EArBD,WAsBE,UAACnG,CAAD,EAAO;AACZ,UACEA,CAAC,CAACyG,UAAF,KAAiB,GAAjB,IACAzG,CAAC,CAAC0G,OAAF,KAAc,mCAFhB,EAGE;AACAvG,gBAAQ,CAACD,oBAAT;AACA;AACD,OAND,MAMO,IAAIF,CAAC,CAACyG,UAAF,KAAiB,GAArB,EAA0B;AAC/BtG,gBAAQ,CAACF,kBAAT;AACA;AACD;;AAEDE,cAAQ,CAACJ,OAAT,CAAiBC,CAAjB;AACD,KAnCI,CAAP;AAoCD;;AAEDkG,UAAQ,CAAC9F,SAAT,GAAqB,UAACuG,OAAD,EAAa;AAChC,QAAMC,OAAO,GAAGxG,oCAAS,+CAAMR,MAAN,GAAiBO,QAAjB,GAA8BwG,OAA9B,EAAzB;AAEA/G,UAAM,GAAGgH,OAAO,CAAChH,MAAjB;AACAO,YAAQ,GAAGyG,OAAO,CAACzG,QAAnB;;AAEA,QAAIA,QAAQ,CAACT,iBAAT,IAA8BkG,OAAO,KAAK,IAA9C,EAAoD;AAClDA,aAAO,GAAG5G,SAAS,CAAC6G,WAAV,CAAsBC,aAAtB,CAAoC,iBAAgB;AAAA,YAAbC,MAAa,SAAbA,MAAa;AAC5DJ,kBAAU,aAAMI,MAAM,CAACC,QAAb,cAAyBD,MAAM,CAACE,SAAhC,CAAV;AACD,OAFS,CAAV;AAGD,KAJD,MAIO,IAAI,CAAC9F,QAAQ,CAACT,iBAAV,IAA+BkG,OAAO,KAAK,IAA/C,EAAqD;AAC1D5G,eAAS,CAAC6G,WAAV,CAAsBgB,UAAtB,CAAiCjB,OAAjC;AACAA,aAAO,GAAG,IAAV;AACAD,gBAAU,GAAG,IAAb;AACD;AACF,GAfD;;AAgBA,SAAOO,QAAP;AACD,C;;;;;;;;;;;ACnID;AACA;AAEe,SAASY,yBAAT,CAAmCC,OAAnC,EAA4C;AACzD,MAAMC,SAAS,GAAG,kFACbC,mCADU,GAEVF,OAAO,CAACC,SAFE,CAAf;;AAKA,MAAME,MAAM,GAAGjC,wBAAwB,CAAC,kFACnC8B,OADkC;AAErClH,oBAAgB,EAAEmH,SAAS,CAAC/E,KAFS;AAGrC+E,aAAS,EAAEzH;AAH0B,KAAvC;AAMA,SAAO;AACL2H,UAAM,EAANA,MADK;AAELF,aAAS,EAATA,SAFK;AAGLG,cAAU,EAAE,OAHP;AAIL3G,QAAI,EAAE,QAJD;AAKL4G,SAAK,EAAE;AALF,GAAP;AAOD,C;;;;;;;ACtBY;;AAEb,wBAAwB,mBAAO,CAAC,EAA4B;AAC5D,0BAA0B,mBAAO,CAAC,EAA2B;;AAE7D;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC1BA;;AAEA,aAAa,mBAAO,CAAC,EAAa;;AAElC;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,YAAY,SAAS;AACrB;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB,aAAa,OAAO;AACpB,aAAa,SAAS;AACtB,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK,kDAAkD;AACvD;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;;;;;;AClEA;AACA,aAAa,mBAAO,CAAC,EAAa;AAClC,gBAAgB,mBAAO,CAAC,CAAS;;AAEjC;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;;;;;;;ACba;;AAEb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,eAAe;AAChC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACpFa;;AAEb;;AAEA,QAAQ,mBAAO,CAAC,CAAoB;AACpC,UAAU,mBAAO,CAAC,CAAkB;;AAEpC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;;;;;;;;AChCa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACRA;;;;;;;;ACAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;;;;;ACdA;AACA;AAEA;AACA;AACA;AACA;AACA,IAAI,EAAE,cAAcpI,SAAhB,CAAJ,EAAgC;AAC9BA,WAAS,CAACH,QAAV,GACE;AACA;AACA;AACA;AACA;AACCG,WAAS,CAACqI,YAAV,IACCrI,SAAS,CAACqI,YAAV,CAAuB3G,OAAvB,CACE,YADF,EAEE4G,MAAM,CAACC,SAAP,CAAiBC,WAFnB,CADF,IAKA,OAXF,CAD8B,CAYnB;AACZ,C;;;;;;;ACpBD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,sBAAsB;AACvC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,cAAc;AACd;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA,iCAAiC,QAAQ;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,OAAO;AACP;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,OAAO;AACxB;AACA;AACA;;AAEA;AACA,QAAQ,yBAAyB;AACjC;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;;;;;;;;AC7ba;;AAEb,iBAAiB,mBAAO,CAAC,EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACF7B,sWAAsT,E;;;;;;;;;;;;ACAtT;AACbC,iBAAe,sOADF;AAKbC,cAAY,sJALC;AAQbC,kBAAgB,2QARH;AAebC,oBAAkB,8CAfL;AAgBbC,cAAY;AAhBC,CAAf,E;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;;AAEA,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAClI,MAAD,EAAY;AAAA,MACjCxB,WADiC,GACuBwB,MADvB,CACjCxB,WADiC;AAAA,MACpBE,YADoB,GACuBsB,MADvB,CACpBtB,YADoB;AAAA,MACNK,cADM,GACuBiB,MADvB,CACNjB,cADM;AAAA,MACUE,QADV,GACuBe,MADvB,CACUf,QADV;AAGzC,MAAMkJ,QAAQ,GAAG,EAAjB;;AAEA,MAAI,OAAO3J,WAAP,KAAuB,QAA3B,EAAqC;AACnC2J,YAAQ,CAAC3J,WAAT,GAAuBA,WAAvB;AACD;;AAED,MAAI,OAAOS,QAAP,KAAoB,QAAxB,EAAkC;AAChCkJ,YAAQ,CAAClJ,QAAT,GAAoBA,QAApB;AACD;;AAED,MAAI,OAAOF,cAAP,KAA0B,SAA9B,EAAyC;AACvCoJ,YAAQ,CAACpJ,cAAT,GAA0BA,cAA1B;AACD;;AACD,MAAI,OAAOL,YAAP,KAAwB,QAA5B,EAAsC;AACpCyJ,YAAQ,CAACzJ,YAAT,GAAwBA,YAAxB;AACD;;AAED,SAAOyJ,QAAP;AACD,CArBD;;AAuBA,IAAMC,yDAA4B,GAAG,SAA/BA,4BAA+B,OAgB/B;AAAA,MAfJ9C,aAeI,QAfJA,aAeI;AAAA,MAdJC,aAcI,QAdJA,aAcI;AAAA,MAbJC,MAaI,QAbJA,MAaI;AAAA,MAZJC,KAYI,QAZJA,KAYI;AAAA,MAXJjH,WAWI,QAXJA,WAWI;AAAA,MAVJE,YAUI,QAVJA,YAUI;AAAA,MATJK,cASI,QATJA,cASI;AAAA,mCARJkB,gBAQI;AAAA,MARJA,gBAQI,sCAReoH,mCAAgB,CAAChF,KAQhC;AAAA,2BAPJpD,QAOI;AAAA,MAPJA,QAOI,8BAPOG,SAAS,CAACH,QAAV,CAAmBI,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAOP;AAAA,yBANJa,MAMI;AAAA,MANJA,MAMI,4BANK,YAAM,CAAE,CAMb;AAAA,0BALJC,OAKI;AAAA,MALJA,OAKI,6BALM,UAACC,CAAD,EAAO;AACf,UAAMA,CAAN;AACD,GAGG;AAAA,MAFJC,kBAEI,QAFJA,kBAEI;AAAA,MADJC,oBACI,QADJA,oBACI;AACJ,MAAMoF,YAAY,GAAGJ,aAAa,CAACK,UAAd,CAAyBF,KAAzB,EAAgCD,MAAhC,EAAwCD,aAAxC,CAArB;AACAG,cAAY,CAACE,EAAb,CAAgBC,eAAhB,0BAAkDC,kBAAlD;AAEA,MAAMrF,aAAa,GAAGD,oCAAS,CAAC;AAC9BgF,UAAM,EAANA,MAD8B;AAE9BC,SAAK,EAALA,KAF8B;AAG9BjH,eAAW,EAAXA,WAH8B;AAI9BE,gBAAY,EAAZA,YAJ8B;AAK9BK,kBAAc,EAAdA,cAL8B;AAM9BE,YAAQ,EAARA,QAN8B;AAO9BgB,oBAAgB,EAAhBA,gBAP8B;AAQ9BC,UAAM,EAANA,MAR8B;AAS9BC,WAAO,EAAPA,OAT8B;AAU9BE,sBAAkB,EAAlBA,kBAV8B;AAW9BC,wBAAoB,EAApBA;AAX8B,GAAD,CAA/B;AAcA,MAAIN,MAAM,GAAGkI,sBAAsB,CAACzH,aAAa,CAACT,MAAf,CAAnC;AACA,MAAIO,QAAQ,GAAGE,aAAa,CAACF,QAA7B;;AAEA,MAAM+F,QAAQ,GAAG,SAAXA,QAAW,CAAC+B,iBAAD,EAAoB9B,EAApB,EAA2B;AAC1C,QAAM+B,iBAAiB,GAAGD,iBAAiB,IAAIrI,MAAM,CAACtB,YAAtD;;AAEA,QAAI,CAAC4J,iBAAL,EAAwB;AACtB,UAAMlD,KAAK,GAAG,IAAImD,KAAJ,CACZ,mDADY,CAAd;AAGA,aAAOC,OAAO,CAACC,MAAR,CAAerD,KAAf,CAAP;AACD;;AAED,WAAOM,YAAY,CAChBgD,OADI,iCACS1I,MADT;AACiBtB,kBAAY,EAAE4J;AAD/B,QAEJ5B,IAFI,CAEC,UAACC,OAAD,EAAa;AACjB,UAAMC,IAAI,GAAGD,OAAO,CAACC,IAAR,CAAapH,GAAb,CAAiB,UAAC6E,GAAD,EAAMC,QAAN;AAAA,eAC5BF,oCAAS,CAAC;AACRnE,0BAAgB,EAAEM,QAAQ,CAACN,gBADnB;AAERoE,aAAG,EAAHA,GAFQ;AAGRC,kBAAQ,EAARA,QAHQ;AAIRC,eAAK,EAAE+D,iBAJC;AAKR9D,mBAAS,EAAEmC;AALH,SAAD,CADmB;AAAA,OAAjB,CAAb;AAUApG,cAAQ,CAACL,MAAT,CAAgB;AACd0G,YAAI,EAAJA,IADc;AAEdrC,aAAK,EAAE+D,iBAFO;AAGd9D,iBAAS,EAAEmC;AAHG,OAAhB;AAMA,aAAOC,IAAP;AACD,KApBI,EAqBJF,IArBI,CAqBCH,EArBD,WAsBE,UAACnG,CAAD,EAAO;AACZ,UACEA,CAAC,CAACyG,UAAF,KAAiB,GAAjB,IACAzG,CAAC,CAAC0G,OAAF,KAAc,mCAFhB,EAGE;AACAvG,gBAAQ,CAACD,oBAAT;AACA;AACD,OAND,MAMO,IAAIF,CAAC,CAACyG,UAAF,KAAiB,GAArB,EAA0B;AAC/BtG,gBAAQ,CAACF,kBAAT;AACA;AACD;;AAEDE,cAAQ,CAACJ,OAAT,CAAiBC,CAAjB;AACD,KAnCI,CAAP;AAoCD,GA9CD;;AAgDAkG,UAAQ,CAAC9F,SAAT,GAAqB,UAACuG,OAAD,EAAa;AAChC,QAAMC,OAAO,GAAGxG,oCAAS,+CAAMR,MAAN,GAAiBO,QAAjB,GAA8BwG,OAA9B,EAAzB;AAEA/G,UAAM,GAAGkI,sBAAsB,CAAClB,OAAO,CAAChH,MAAT,CAA/B;AACAO,YAAQ,GAAGyG,OAAO,CAACzG,QAAnB;AAEA,WAAO+F,QAAP;AACD,GAPD;;AASA,SAAOA,QAAP;AACD,CA/FD;;AAiGe8B,8HAAf,E;;;;;;;;;;;;;;;;;;;;AC7HA;AAEA;AACA;AAEA;AAEA;AAEA;AACA;AAEA;AACA;AACAO,oBAAS,CAACC,yBAAD,EAAM;AAAEC,SAAO,EAAE;AAAX,CAAN,CAAT;AAEA;AACA;;AAEA,IAAMC,eAAe,GAAG,SAAlBA,eAAkB,CAACC,GAAD,EAAMC,KAAN,EAAgB;AACtCC,QAAM,CAACC,OAAP,CAAeF,KAAf,EAAsBG,OAAtB,CAA8B,gBAAmB;AAAA;AAAA,QAAjBvI,IAAiB;AAAA,QAAXyB,KAAW;;AAC/C0G,OAAG,CAACK,YAAJ,CAAiBxI,IAAjB,YAA0ByB,KAA1B;AACD,GAFD;AAIA,SAAO0G,GAAP;AACD,CAND;;AAQe,SAASM,aAAT,CAAgBlC,OAAhB,EAAyB;AAAA,MAEpCmC,SAFoC,GAMlCnC,OANkC,CAEpCmC,SAFoC;AAAA,MAGpCC,KAHoC,GAMlCpC,OANkC,CAGpCoC,KAHoC;AAAA,MAIpCC,aAJoC,GAMlCrC,OANkC,CAIpCqC,aAJoC;AAAA,8BAMlCrC,OANkC,CAKpCsC,mBALoC;AAAA,MAKfC,uBALe,sCAKW,EALX,0BAQtC;;AACA,MAAIJ,SAAS,YAAYK,QAAzB,EAAmC;AACjC,QAAIL,SAAS,CAAC5G,MAAV,GAAmB,CAAvB,EAA0B;AACxB,YAAM,IAAI6F,KAAJ,CAAUqB,MAAM,CAAC/B,eAAjB,CAAN;AACD,KAHgC,CAKjC;;;AACA,WAAOwB,aAAM,CAAC,4CAAKlC,OAAN;AAAemC,eAAS,EAAEA,SAAS,CAAC,CAAD;AAAnC,OAAb;AACD,GAhBqC,CAkBtC;;;AACA,MAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;AACjC,QAAMO,iBAAiB,GAAGC,QAAQ,CAACC,gBAAT,CAA0BT,SAA1B,CAA1B;AACA,WAAOD,aAAM,CAAC,4CAAKlC,OAAN;AAAemC,eAAS,EAAEO;AAA1B,OAAb;AACD,GAtBqC,CAwBtC;;;AACA,MAAI,EAAEP,SAAS,YAAYU,gBAAvB,CAAJ,EAA8C;AAC5C,UAAM,IAAIzB,KAAJ,CAAUqB,MAAM,CAAC9B,YAAjB,CAAN;AACD;;AAED,MAAMmC,cAAc,GAAG,IAAIC,gBAAJ,EAAvB;AACA,MAAMC,MAAM,eAAQZ,KAAK,KAAK,KAAV,GAAkB,UAAlB,GAA+B,EAAvC,CAAZ;;AAEA,MAAME,mBAAmB,GAAG;AAC1BW,cAAU,EAAE,IADW;AAEvBC,QAAI,EAAE,KAFiB;AAGvBC,cAAU,EAAE;AACVC,UAAI,0BAAmBhB,KAAK,KAAK,KAAV,GAAkB,UAAlB,GAA+B,EAAlD,CADM;AAEVY,YAAM,EAANA;AAFU,KAHW;AAOvBK,SAAK,EAAEC,YAAA,KAAyB;AAPT,KAQpBf,uBARoB,CAAzB;;AAWA,MAAMgB,mBAAmB,GAAGxD,4CAAyB,CAAC,4CACjDC,OADgD;AAEnD7B,iBAAa,EAAbA,2BAFmD;AAGnDpF,UAAM,EAAE;AAAA,UAAG0G,IAAH,SAAGA,IAAH;AAAA,UAASpC,SAAT,SAASA,SAAT;AAAA,UAAoBD,KAApB,SAAoBA,KAApB;AAAA,aACN0F,cAAc,CAACU,IAAf,CAAoB,aAApB,EAAmC;AACjCnG,iBAAS,EAATA,SADiC;AAEjCD,aAAK,EAALA,KAFiC;AAGjCqG,mBAAW,EAAEhE;AAHoB,OAAnC,CADM;AAAA,KAH2C;AASnDzG,WAAO,EAAE,iBAACC,CAAD;AAAA,aAAO6J,cAAc,CAACU,IAAf,CAAoB,OAApB,EAA6BvK,CAA7B,CAAP;AAAA,KAT0C;AAUnDC,sBAAkB,EAAE,8BAAM;AACxB,UAAMwK,SAAS,GAAGZ,cAAc,CAACa,aAAf,CAA6B,OAA7B,CAAlB;;AACA,UAAID,SAAS,KAAK,CAAlB,EAAqB;AACnB1F,eAAO,CAAC4F,GAAR,CAAYnB,MAAM,CAAC7B,gBAAnB,EADmB,CACmB;;AACtC;AACD;;AAEDkC,oBAAc,CAACU,IAAf,CAAoB,OAApB,EAA6B;AAAE7D,eAAO,EAAE8C,MAAM,CAAC7B;AAAlB,OAA7B;AACD,KAlBkD;AAmBnDzH,wBAAoB,EAAE,gCAAM;AAC1B,UAAI6G,OAAO,IAAIA,OAAO,CAAC1B,KAAnB,IAA4B0B,OAAO,CAAC1B,KAAR,CAAcuF,UAAd,CAAyB,IAAzB,CAAhC,EAAgE;AAC9D7F,eAAO,CAACC,KAAR,CAAcwE,MAAM,CAAC5B,kBAArB,EAD8D,CACpB;AAC3C,OAFD,MAEO;AACL7C,eAAO,CAACC,KAAR,CAAcwE,MAAM,CAAC3B,YAArB,EADK,CAC+B;AACrC;AACF,KAzBkD;AA0BnDqB,aAAS,EAAE3J;AA1BwC,KAArD;AA6BA,MAAMsL,oBAAoB,GAAGC,yBAAY,CACvC5B,SADuC,EAEvCG,mBAFuC,EAGvCiB,mBAHuC,CAAzC;AAKA,MAAMS,qBAAqB,GAAG7B,SAAS,CAAC8B,UAAxC;AAEA,MAAMC,wBAAwB,GAAG,CAAC,UAAD,EAAa,eAAb,CAAjC;AAEAA,0BAAwB,CAAClC,OAAzB,CAAiC,UAACmC,SAAD,EAAe;AAC9CL,wBAAoB,CAACM,EAArB,wBAAwCD,SAAxC,GAAqD,UAACE,CAAD,EAAIlJ,UAAJ,EAAmB;AACtE2H,oBAAc,CAACU,IAAf,CAAoB,QAApB,EAA8B;AAC5BnG,iBAAS,EAAElC,UAAU,CAACkC,SADM;AAE5BD,aAAK,EAAEjC,UAAU,CAACiC,KAFU;AAG5BjC,kBAAU,EAAVA,UAH4B;AAI5BmJ,uBAAe,EAAEnJ,UAAU,CAACgC;AAJA,OAA9B;AAMD,KAPD;AAQD,GATD;AAUA2G,sBAAoB,CAACM,EAArB,CAAwB,4BAAxB,EAAsD,UAACC,CAAD,EAAIlJ,UAAJ,EAAmB;AACvE2H,kBAAc,CAACU,IAAf,CAAoB,eAApB,EAAqC;AACnCnG,eAAS,EAAElC,UAAU,CAACkC,SADa;AAEnCD,WAAK,EAAEjC,UAAU,CAACiC,KAFiB;AAGnCjC,gBAAU,EAAVA,UAHmC;AAInCmJ,qBAAe,EAAEnJ,UAAU,CAACgC;AAJO,KAArC;AAMD,GAPD;AASA,MAAMoH,KAAK,GAAG5B,QAAQ,CAAC6B,aAAT,CAAuB,QAAvB,CAAd;AACAD,OAAK,CAACtC,YAAN,CAAmB,MAAnB,EAA2B,QAA3B;AACAsC,OAAK,CAACtC,YAAN,CAAmB,YAAnB,EAAiC,OAAjC;;AACA,MACEI,aAAa,IACbA,aAAa,CAACoC,WADd,IAEApC,aAAa,CAACoC,WAAd,YAAqC3C,MAHvC,EAIE;AACAH,mBAAe,CAAC4C,KAAD,EAAQlC,aAAa,CAACoC,WAAtB,CAAf;AACD;;AACDF,OAAK,CAACG,SAAN,CAAgBC,GAAhB,WAAuB3B,MAAvB;AACAuB,OAAK,CAACG,SAAN,CAAgBC,GAAhB,WAAuB3B,MAAvB;AACAuB,OAAK,CAACK,SAAN,GAAkBC,WAAlB;AACAb,uBAAqB,CAACc,WAAtB,CAAkCP,KAAlC;AACAA,OAAK,CAACnC,KAAN,CAAY2C,OAAZ,GAAsB,MAAtB;AAEA,MAAMC,GAAG,GAAGrC,QAAQ,CAAC6B,aAAT,CAAuB,QAAvB,CAAZ;AACAQ,KAAG,CAAC/C,YAAJ,CAAiB,MAAjB,EAAyB,QAAzB;AACA+C,KAAG,CAAC/C,YAAJ,CAAiB,YAAjB,EAA+B,OAA/B;;AACA,MACEI,aAAa,IACbA,aAAa,CAAC4C,SADd,IAEA5C,aAAa,CAAC4C,SAAd,YAAmCnD,MAHrC,EAIE;AACAH,mBAAe,CAACqD,GAAD,EAAM3C,aAAa,CAAC4C,SAApB,CAAf;AACD;;AACDD,KAAG,CAACN,SAAJ,CAAcC,GAAd,WAAqB3B,MAArB;AACAgC,KAAG,CAACN,SAAJ,CAAcC,GAAd,WAAqB3B,MAArB;AACAgC,KAAG,CAACJ,SAAJ,GAAgBM,0BAAhB;AACAlB,uBAAqB,CAACc,WAAtB,CAAkCE,GAAlC;AAEAA,KAAG,CAACG,gBAAJ,CAAqB,OAArB,EAA8B,YAAM;AAClC5B,uBAAmB,CAACpD,MAApB,CAA2B9G,SAA3B,CAAqC;AAAEV,uBAAiB,EAAE;AAArB,KAArC;AACAmL,wBAAoB,CAACsB,KAArB;AACAtC,kBAAc,CAACU,IAAf,CAAoB,QAApB;AACD,GAJD;AAMAe,OAAK,CAACY,gBAAN,CAAuB,OAAvB,EAAgC,YAAM;AACpCrB,wBAAoB,CAACC,YAArB,CAAkCsB,MAAlC,CAAyC,EAAzC;AACAvB,wBAAoB,CAACsB,KAArB;AACAb,SAAK,CAACnC,KAAN,CAAY2C,OAAZ,GAAsB,MAAtB;AACAC,OAAG,CAAC5C,KAAJ,CAAU2C,OAAV,GAAoB,EAApB;AACAjC,kBAAc,CAACU,IAAf,CAAoB,OAApB;AACD,GAND;AAQA,MAAI8B,aAAa,GAAG,EAApB;;AAEA,MAAMC,aAAa,GAAG,SAAhBA,aAAgB,GAAM;AAC1B,QAAMnI,KAAK,GAAG0G,oBAAoB,CAAC0B,GAArB,EAAd;;AACA,QAAIpI,KAAK,KAAK,EAAd,EAAkB;AAChB4H,SAAG,CAAC5C,KAAJ,CAAU2C,OAAV,GAAoB,EAApB;AACAR,WAAK,CAACnC,KAAN,CAAY2C,OAAZ,GAAsB,MAAtB;;AACA,UAAIO,aAAa,KAAKlI,KAAtB,EAA6B;AAC3B0F,sBAAc,CAACU,IAAf,CAAoB,OAApB;AACD;AACF,KAND,MAMO;AACLe,WAAK,CAACnC,KAAN,CAAY2C,OAAZ,GAAsB,EAAtB;AACAC,SAAG,CAAC5C,KAAJ,CAAU2C,OAAV,GAAoB,MAApB;AACD;;AACDO,iBAAa,GAAGlI,KAAhB;AACD,GAbD;;AAeA4G,uBAAqB,CAClByB,aADH,YACqBzC,MADrB,aAEGmC,gBAFH,CAEoB,OAFpB,EAE6BI,aAF7B;AAIA,MAAMG,6BAA6B,GAAG,CAAC,MAAD,EAAS,OAAT,CAAtC;AACAA,+BAA6B,CAAC1D,OAA9B,CAAsC,UAAC2D,UAAD,EAAgB;AACpD7C,kBAAc,CAAC6C,UAAD,CAAd,GAA6B,YAAa;AAAA;;AACxC,+BAAA7B,oBAAoB,CAACC,YAArB,EAAkC4B,UAAlC;AACD,KAFD;AAGD,GAJD;;AAMA7C,gBAAc,CAAC8C,MAAf,GAAwB,YAAM;AAC5B,WAAO9B,oBAAoB,CAAC0B,GAArB,EAAP;AACD,GAFD;;AAIA1C,gBAAc,CAAC+C,OAAf,GAAyB,YAAa;AAAA;;AACpC7B,yBAAqB,CAClByB,aADH,YACqBzC,MADrB,aAEG8C,mBAFH,CAEuB,OAFvB,EAEgCP,aAFhC;;AAIA,8BAAAzB,oBAAoB,CAACC,YAArB,EAAkC8B,OAAlC;AACD,GAND;;AAQA/C,gBAAc,CAACuC,MAAf,GAAwB,YAAa;AAAA;;AACnCC,iBAAa,mDAAb;;AACA,QAAIA,aAAa,KAAK,EAAtB,EAA0B;AACxBN,SAAG,CAAC5C,KAAJ,CAAU2C,OAAV,GAAoB,EAApB;AACAR,WAAK,CAACnC,KAAN,CAAY2C,OAAZ,GAAsB,MAAtB;AACD,KAHD,MAGO;AACLR,WAAK,CAACnC,KAAN,CAAY2C,OAAZ,GAAsB,EAAtB;AACAC,SAAG,CAAC5C,KAAJ,CAAU2C,OAAV,GAAoB,MAApB;AACD;;AACD,8BAAAjB,oBAAoB,CAACC,YAArB,EAAkCsB,MAAlC;AACD,GAVD;;AAYAvC,gBAAc,CAACiB,YAAf,GAA8BD,oBAA9B;;AAEAhB,gBAAc,CAACxD,MAAf,GAAwB;AAAA,QAAClC,KAAD,uEAAS,EAAT;AAAA,WACtB,IAAIiE,OAAJ,CAAY,UAAC0E,OAAD,EAAa;AACvBxC,yBAAmB,CAACpD,MAApB,CAA2B/C,KAA3B,EAAkC2I,OAAlC;AACD,KAFD,CADsB;AAAA,GAAxB;;AAKAjD,gBAAc,CAACzJ,SAAf,GAA2B,UAACC,aAAD,EAAmB;AAC5C,QAAM0M,UAAU,GAAG,wBAAK1M,aAAR,CAAhB;;AAEA,WAAO0M,UAAU,CAACjN,MAAlB;AACA,WAAOiN,UAAU,CAAChN,OAAlB;AACA,WAAOgN,UAAU,CAAC9M,kBAAlB;AACA,WAAO8M,UAAU,CAAC7M,oBAAlB;AACA,WAAO6M,UAAU,CAAC/F,SAAlB;AAEAsD,uBAAmB,CAACpD,MAApB,CAA2B9G,SAA3B,CAAqC2M,UAArC;AACA,WAAOlD,cAAP;AACD,GAXD;;AAaAA,gBAAc,CAACvB,OAAf,GAAyBN,gCAA4B,CAAC,4CACjDjB,OADgD;AAEnD7B,iBAAa,EAAbA,2BAFmD;AAGnDrF,oBAAgB,EAAE,CAACkH,OAAO,CAACC,SAAR,IAAqB,EAAtB,EAA0B/E,KAHO;AAInDnC,UAAM,EAAE;AAAA,UAAG0G,IAAH,SAAGA,IAAH;AAAA,UAASpC,SAAT,SAASA,SAAT;AAAA,UAAoBD,KAApB,SAAoBA,KAApB;AAAA,aACN0F,cAAc,CAACU,IAAf,CAAoB,SAApB,EAA+B;AAC7BnG,iBAAS,EAATA,SAD6B;AAE7BD,aAAK,EAALA,KAF6B;AAG7BqG,mBAAW,EAAEhE;AAHgB,OAA/B,CADM;AAAA,KAJ2C;AAUnDzG,WAAO,EAAE,iBAACC,CAAD;AAAA,aAAO6J,cAAc,CAACU,IAAf,CAAoB,OAApB,EAA6BvK,CAA7B,CAAP;AAAA,KAV0C;AAWnDC,sBAAkB,EAAE,8BAAM;AACxB,UAAMwK,SAAS,GAAGZ,cAAc,CAACa,aAAf,CAA6B,OAA7B,CAAlB;;AACA,UAAID,SAAS,KAAK,CAAlB,EAAqB;AACnB1F,eAAO,CAAC4F,GAAR,CAAYnB,MAAM,CAAC7B,gBAAnB,EADmB,CACmB;;AACtC;AACD;;AAEDkC,oBAAc,CAACU,IAAf,CAAoB,OAApB,EAA6B;AAAE7D,eAAO,EAAE8C,MAAM,CAAC7B;AAAlB,OAA7B;AACD,KAnBkD;AAoBnDzH,wBAAoB,EAAE,gCAAM;AAC1B,UAAI6G,OAAO,IAAIA,OAAO,CAAC1B,KAAnB,IAA4B0B,OAAO,CAAC1B,KAAR,CAAcuF,UAAd,CAAyB,IAAzB,CAAhC,EAAgE;AAC9D7F,eAAO,CAACC,KAAR,CAAcwE,MAAM,CAAC5B,kBAArB,EAD8D,CACpB;AAC3C,OAFD,MAEO;AACL7C,eAAO,CAACC,KAAR,CAAcwE,MAAM,CAAC3B,YAArB,EADK,CAC+B;AACrC;AACF;AA1BkD,KAArD;AA6BA,SAAOgC,cAAP;AACD,C;;;;;;AClRD;;AAEA,aAAa,mBAAO,CAAC,EAAU;AAC/B,kBAAkB,mBAAO,CAAC,EAAkB;AAC5C,gBAAgB,mBAAO,CAAC,EAAgB;AACxC,YAAY,mBAAO,CAAC,EAAY;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;;AAEhB;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB;AACA,WAAW,OAAO;AAClB;AACA,WAAW,aAAa;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,mBAAO,CAAC,EAAO;;AAE7B,cAAc,mBAAO,CAAC,CAAY;AAClC,gBAAgB,mBAAO,CAAC,EAAS;AACjC,YAAY,mBAAO,CAAC,EAAU;;AAE9B;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;;AAEpC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,mBAAO,CAAC,EAAO;;;AAGpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,2BAA2B;AAC3B;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,yDAAyD;AACzD;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,iCAAiC,mCAAmC;AACpE;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB,WAAW,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAO,CAAC,CAAS;;AAEjC;AACA,iBAAiB;AACjB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA,YAAY,SAAS;AACrB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,YAAY,SAAS;AACrB,YAAY,kBAAkB;AAC9B;AACA;AACA,gBAAgB,mBAAO,CAAC,EAAS;AACjC,YAAY,mBAAO,CAAC,EAAU;;AAE9B;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU,SAAS;AACnB,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB;AACA;AACA;AACA;AACA,gBAAgB,mBAAO,CAAC,EAAS;AACjC,YAAY,mBAAO,CAAC,EAAU;;AAE9B,oDAAoD,oBAAoB,kCAAkC,gBAAgB;;AAE1H;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB,mBAAO,CAAC,CAAY;AACpC,eAAe,mBAAO,CAAC,EAAW;;AAElC;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,KAAK;AACL,GAAG;AACH;;AAEA;AACA;AACA,WAAW,aAAa;AACxB;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA,uBAAuB,oBAAoB;AAC3C;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,WAAW,OAAO;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU,OAAO;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,OAAO;AACjB;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,OAAO;AACjB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB,mBAAO,CAAC,CAAS;AACjC;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,cAAc,mBAAO,CAAC,CAAS;AAC/B;AACA;AACA,gCAAgC,4BAA4B;AAC5D;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,uCAAuC,qCAAqC;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;;;;;;ACv8BA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACNA,wBAAwB,mBAAO,CAAC,EAAwB;AACxD,gBAAgB,mBAAO,CAAC,EAAgB;AACxC,wBAAwB,mBAAO,CAAC,EAAwB;;AAExD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,WAAW,iCAAiC;AAC9D;AACA;AACA;AACA;AACA,kBAAkB,WAAW,iCAAiC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,wBAAwB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,SAAS;AACnB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,sBAAsB;AACzC;AACA;;AAEA;AACA;AACA;AACA;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB,UAAU,SAAS;AACnB;AACA;AACA,WAAW,kBAAkB;AAC7B;AACA;AACA;AACA;AACA,GAAG;AACH,QAAQ;AACR;AACA;AACA,cAAc,mBAAO,CAAC,EAAY;;AAElC;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;AACA;AACA,WAAW,eAAe;AAC1B;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB,UAAU,SAAS;AACnB;AACA;AACA,WAAW,kBAAkB;AAC7B;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,WAAW,eAAe;AAC1B;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,UAAU,OAAO;AACjB;AACA,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB;AACA;AACA;AACA;AACA;AACA,cAAc,mBAAO,CAAC,CAAY;AAClC,aAAa,mBAAO,CAAC,EAAW;AAChC,kDAAkD,iCAAiC;;AAEnF;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,yBAAyB;AACpC;AACA,GAAG;AACH;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,eAAe;AAC1B;AACA;AACA;AACA;AACA,aAAa;AACb,KAAK;AACL;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB,kBAAkB;AACrC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAO,CAAC,EAAS;AACjC,YAAY,mBAAO,CAAC,EAAU;;AAE9B;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;;;;;;ACpYA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;;;;;ACdA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;ACNA,cAAc,mBAAO,CAAC,CAAS;;AAE/B;AACA;;AAEA;AACA;AACA;AACA;AACA,yCAAyC;AACzC,SAAS;AACT;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;;;;;;;AClBa;;AAEb;AACA,aAAa,mBAAO,CAAC,EAAe;;AAEpC;AACA,4CAA4C,oBAAoB,EAAE,GAAG,mBAAO,CAAC,EAAkB;;AAE/F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;;AAEA;;;;;;;;AC/Ba;;AAEb;AACA;AACA;AACA;AACA;AACA,cAAc,mBAAO,CAAC,EAAe,EAAE;AACvC;AACA,0CAA0C,iBAAiB;AAC3D,uDAAuD;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,cAAc;AACpD;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,mBAAmB;AACrC;AACA;AACA;;AAEA;AACA,kBAAkB,mBAAmB;AACrC;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,sBAAsB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACzHA,0DAAY,mBAAO,CAAC,EAAO;AAC3B;;AAEA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA,+BAA+B;;AAE/B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E;AAC5E;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;;ACpFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB,mBAAO,CAAC,EAAI;;AAE/B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,WAAW,OAAO;AAClB,YAAY;AACZ;AACA;;AAEA;AACA;;AAEA;AACA;AACA,cAAc;AACd;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY;AACZ;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,iBAAiB,SAAS;AAC1B,4BAA4B;AAC5B;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY;AACZ;AACA;;AAEA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,MAAM;AACjB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;;;;;;;ACzMA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,OAAO;AAClB,YAAY,MAAM;AAClB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvJa;;AAEb,aAAa,mBAAO,CAAC,EAAQ;AAC7B,gCAAgC,mBAAO,CAAC,EAAa;;AAErD;AACA;AACA;AACA;AACA,iBAAiB,mBAAO,CAAC,EAAU;AACnC,eAAe,mBAAO,CAAC,EAAW;AAClC,sBAAsB,mBAAO,CAAC,EAAkB;AAChD,qBAAqB,mBAAO,CAAC,EAAiB;AAC9C,eAAe,mBAAO,CAAC,EAAc;AACrC;;AAEA,MAAM,KAAgC,EAAE,EAErC;;AAEH;AACA,oBAAoB,mBAAO,CAAC,CAAa;;AAEzC,+BAA+B;;AAE/B;;AAEA;AACA;;AAEA,0BAA0B,mBAAO,CAAC,EAAe;;AAEjD;AACA,4DAA4D;;AAE5D;;AAEA;AACA;AACA;AACA,WAAW,mBAAO,CAAC,EAAO;AAC1B;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO;AACP,KAAK;AACL;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;;;;;;;AC3OA;;AAEA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA;;AAEA;;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,CAAC,KAA4D;AAC7D,CAAC,SAC+B;AAChC,CAAC,qBAAqB;;AAEtB;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,gFAAgF;;AAEhF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,0BAA0B,sBAAsB;;AAEhD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC,yCAAyC,UAAc;AACxD;AACA,CAAC;AACD;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,IAAI;AACd;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,wBAAwB;AACzC;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,mBAAmB,6CAA6C;AAChE;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,MAAM;AAChB,UAAU,OAAO;AACjB;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,UAAU,MAAM;AAChB;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA,qBAAqB,YAAY;AACjC;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,IAAI;AACd;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;AACA,UAAU,SAAS;AACnB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,UAAU,SAAS;AACnB,UAAU,SAAS;AACnB;AACA,WAAW;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,UAAU,SAAS;AACnB;AACA,WAAW;AACX;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA,YAAY,SAAS;AACrB,aAAa;AACb;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA,SAAS;AACT,OAAO;AACP;;AAEA;AACA;;AAEA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,CAAC;;;;AAID;;;;;;;;;ACrpCa;;AAEb;;AAEA,aAAa,mBAAO,CAAC,EAAwB;;AAE7C;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;;;;;;;ACda;;AAEb;;AAEA,aAAa,mBAAO,CAAC,EAAW;;AAEhC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;AC7HA;;AAEA,UAAU,mBAAO,CAAC,EAAiB;AACnC,wBAAwB,mBAAO,CAAC,EAAwB;;AAExD;AACA;AACA,oBAAoB,mBAAO,CAAC,CAAY;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;;;;;;;AChDa;;AAEb,iCAAiC,mBAAO,CAAC,EAAU;AACnD,qCAAqC,mBAAO,CAAC,EAAU;;;;;;;;ACHvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;;;;;ACnFa;;AAEb;;;;;;;;ACFa;;AAEb;AACA,YAAY,mBAAO,CAAC,EAAgB;;AAEpC;AACA,UAAU,mBAAO,CAAC,CAAkB;AACpC;;AAEA;AACA,QAAQ,mBAAO,CAAC,CAAoB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB,mBAAO,CAAC,EAA8B;AACtD,eAAe,mBAAO,CAAC,EAA8B;;AAErD;AACA;;AAEA;AACA;AACA,iCAAiC,WAAW;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;;;;;;;AC1FA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,uBAAuB,iBAAiB;AACxC,iBAAiB,4GAA4G;AAC7H;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,mBAAmB;AACnB;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,uBAAuB;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B,0CAA0C,sBAAsB;AAC3F,2BAA2B;AAC3B,2BAA2B,oDAAoD,sCAAsC;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,+CAA+C,oCAAoC;;AAE5G;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,+CAA+C,sCAAsC;AACrF;;AAEA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,4BAA4B;AAC3D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe;AACf,YAAY;AACZ;;AAEA;AACA;AACA;AACA,iBAAiB,qBAAqB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,qBAAqB;AACtC;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,2CAA2C,4BAA4B;AACvE,KAAK;AACL;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,oEAAoE,cAAc;AAClF;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,wBAAwB,oBAAoB;AAC5C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA,wCAAwC,mCAAmC;AAC3E;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,0CAA0C,wBAAwB;AAClE,KAAK;AACL;AACA,kCAAkC,6DAA6D;AAC/F,KAAK;AACL;AACA;AACA,oEAAoE,oBAAoB;AACxF,OAAO;AACP,KAAK;AACL;AACA,kCAAkC,sBAAsB;AACxD,KAAK;AACL;AACA;AACA,sCAAsC,sBAAsB;AAC5D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,iCAAiC,8BAA8B;AAC/D,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL,6BAA6B,yEAAyE;AACtG,6BAA6B,qEAAqE;AAClG;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA,kCAAkC;AAClC;AACA,OAAO,QAAQ;AACf,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA,kCAAkC,oBAAoB;AACtD,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,uDAAuD,uBAAuB;AAC9E;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA,+BAA+B,iDAAiD;AAChF;AACA;AACA,OAAO;AACP;AACA;AACA,iCAAiC,4CAA4C;AAC7E;AACA,6EAA6E;AAC7E;;AAEA,kCAAkC,yBAAyB,SAAS;AACpE,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB;AAC5C,mBAAmB,qCAAqC;AACxD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,mBAAmB,0BAA0B;AAC7C,mBAAmB,qCAAqC;AACxD,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oEAAoE,kBAAkB;;AAEtF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;AACH;AACA,yCAAyC,4BAA4B;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA,iDAAiD,SAAS;AAC1D;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;AACA;AACA;;AAEA;AACA,CAAC;;AAED,CAAC;AACD;AACA;AACA;AACA,+BAA+B,gCAAgC;AAC/D,mBAAmB;AACnB,sBAAsB;AACtB;AACA,eAAe,qCAAqC;AACpD,eAAe;;AAEf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;;AAEA,aAAa;;AAEb;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,YAAY;AAC1C,+BAA+B,aAAa;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,OAAO,kBAAkB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB;AACtB;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,0CAA0C,yCAAyC;AACnF;AACA;AACA;;AAEA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC;;AAED,CAAC;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA,0CAA0C,aAAa;AACvD;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;;AAED,CAAC;AACD,eAAe;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,4CAA4C,4BAA4B;AACxE,SAAS;AACT;AACA;AACA;AACA,2BAA2B,6BAA6B;AACxD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA,CAAC;;;;;;;;ACtyCY;;AAEb;;AAEA,QAAQ,mBAAO,CAAC,CAAoB;AACpC,UAAU,mBAAO,CAAC,CAAkB;AACpC,eAAe,mBAAO,CAAC,EAAgB;AACvC,YAAY,mBAAO,CAAC,EAAY;AAChC,eAAe,mBAAO,CAAC,EAAe;AACtC,WAAW,mBAAO,CAAC,EAAW;AAC9B,UAAU,mBAAO,CAAC,EAAU;;AAE5B;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,+BAA+B,qCAAqC;AACpE,6CAA6C,wCAAwC;AACrF;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qCAAqC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,gBAAgB,EAAE;AAC5C;AACA,GAAG;;AAEH;AACA,yCAAyC,qBAAqB,EAAE;;AAEhE,8CAA8C,WAAW;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oCAAoC,2BAA2B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA,mBAAmB;;AAEnB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;;AAEnB;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;;AAEnB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB;;AAEnB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,oBAAoB,mBAAO,CAAC,EAAqB;;AAEjD;;;;;;;;AC7oBa;;AAEb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,mBAAO,CAAC,CAAoB;AACpC,UAAU,mBAAO,CAAC,CAAkB;AACpC,mBAAmB,mBAAO,CAAC,EAAoB;;AAE/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,sDAAsD,GAAG;AACzD;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;ACpVa;AACb;AACA,EAAE,mBAAO,CAAC,EAAY;AACtB,EAAE,mBAAO,CAAC,EAAe;AACzB,EAAE,mBAAO,CAAC,EAAkB;AAC5B,EAAE,mBAAO,CAAC,EAAe;AACzB,EAAE,mBAAO,CAAC,EAAW;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB,sBAAsB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC/FA,+CAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;ACVA,8CAAa;AACb;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,E;;;;;;;;ACrBA,8CAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;;;ACjBA,8CAAa;;AAEb;AACA;AACA;;AAEA;AACA;;AAEA,iCAAiC;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,E;;;;;;;;ACvBa;AACb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,E;;;;;;;ACTa;;AAEb,QAAQ,mBAAO,CAAC,CAAoB;AACpC,UAAU,mBAAO,CAAC,CAAkB;AACpC,mBAAmB,mBAAO,CAAC,EAAoB;AAC/C,cAAc,mBAAO,CAAC,EAAc;AACpC,UAAU,mBAAO,CAAC,EAAU;;AAE5B;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uBAAuB,qCAAqC;AAC5D,6CAA6C,wCAAwC;AACrF;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,sBAAsB;AACtB;;AAEA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA;AACA,uCAAuC,qCAAqC;AAC5E;;AAEA;;;;;;;;ACzYa;;AAEb;AACA;AACA;;AAEA,QAAQ,mBAAO,CAAC,CAAoB;AACpC,UAAU,mBAAO,CAAC,CAAkB;AACpC,WAAW,mBAAO,CAAC,EAAW;AAC9B,UAAU,mBAAO,CAAC,EAAU;AAC5B,mBAAmB,mBAAO,CAAC,EAAoB;;AAE/C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;;AAEA,uBAAuB,qCAAqC;AAC5D,6CAA6C,wCAAwC;AACrF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,eAAe,4BAA4B;AAC3C;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;;AAEA;AACA,oEAAoE;AACpE;AACA,wCAAwC,iDAAiD,EAAE;;AAE3F;AACA;AACA;;AAEA;AACA;AACA,eAAe,uCAAuC;AACtD;AACA;;AAEA;AACA;AACA,eAAe,uCAAuC;AACtD;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;;;;;;;AClTa;;AAEb;AACA,QAAQ,mBAAO,CAAC,EAAW;AAC3B,aAAa,mBAAO,CAAC,EAAgB;AACrC;;;;;;;;ACLa;;AAEb,QAAQ,mBAAO,CAAC,CAAoB;AACpC,cAAc,mBAAO,CAAC,EAAkB;AACxC,gCAAgC,mBAAO,CAAC,EAAwC;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;;;;;;;ACvBa;;AAEb,QAAQ,mBAAO,CAAC,CAAoB;AACpC,cAAc,mBAAO,CAAC,EAAkB;AACxC,gCAAgC,mBAAO,CAAC,EAAwC;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,4BAA4B;;AAE1G;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,qCAAqC,eAAe;AACpD,oCAAoC;AACpC,mCAAmC;;AAEnC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA,wBAAwB;AACxB,eAAe;AACf,aAAa;AACb,WAAW;;AAEX;AACA,yBAAyB,yBAAyB;AAClD;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA,KAAK;AACL;AACA;;;;;;;ACpFA;AACA;AACA;AACA;;AAEA;AAEA,IAAMZ,MAAM,GAAG+D,mBAAO,CAAC,EAAD,CAAtB;;AACA,IAAMtH,OAAO,GAAGsH,mBAAO,CAAC,CAAD,CAAvB,C,CAEA;;;AACAC,MAAM,CAACC,OAAP,GAAiBjE,MAAM,WAAvB;AACAgE,MAAM,CAACC,OAAP,CAAexH,OAAf,GAAyBA,OAAO,WAAhC;AACA,sC", "file": "places.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"places\"] = factory();\n\telse\n\t\troot[\"places\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 69);\n", "'use strict';\n\nvar DOM = require('./dom.js');\n\nfunction escapeRegExp(str) {\n  return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\n}\n\nmodule.exports = {\n  // those methods are implemented differently\n  // depending on which build it is, using\n  // $... or angular... or Zepto... or require(...)\n  isArray: null,\n  isFunction: null,\n  isObject: null,\n  bind: null,\n  each: null,\n  map: null,\n  mixin: null,\n\n  isMsie: function(agentString) {\n    if (agentString === undefined) { agentString = navigator.userAgent; }\n    // from https://github.com/ded/bowser/blob/master/bowser.js\n    if ((/(msie|trident)/i).test(agentString)) {\n      var match = agentString.match(/(msie |rv:)(\\d+(.\\d+)?)/i);\n      if (match) { return match[2]; }\n    }\n    return false;\n  },\n\n  // http://stackoverflow.com/a/6969486\n  escapeRegExChars: function(str) {\n    return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\n  },\n\n  isNumber: function(obj) { return typeof obj === 'number'; },\n\n  toStr: function toStr(s) {\n    return s === undefined || s === null ? '' : s + '';\n  },\n\n  cloneDeep: function cloneDeep(obj) {\n    var clone = this.mixin({}, obj);\n    var self = this;\n    this.each(clone, function(value, key) {\n      if (value) {\n        if (self.isArray(value)) {\n          clone[key] = [].concat(value);\n        } else if (self.isObject(value)) {\n          clone[key] = self.cloneDeep(value);\n        }\n      }\n    });\n    return clone;\n  },\n\n  error: function(msg) {\n    throw new Error(msg);\n  },\n\n  every: function(obj, test) {\n    var result = true;\n    if (!obj) {\n      return result;\n    }\n    this.each(obj, function(val, key) {\n      if (result) {\n        result = test.call(null, val, key, obj) && result;\n      }\n    });\n    return !!result;\n  },\n\n  any: function(obj, test) {\n    var found = false;\n    if (!obj) {\n      return found;\n    }\n    this.each(obj, function(val, key) {\n      if (test.call(null, val, key, obj)) {\n        found = true;\n        return false;\n      }\n    });\n    return found;\n  },\n\n  getUniqueId: (function() {\n    var counter = 0;\n    return function() { return counter++; };\n  })(),\n\n  templatify: function templatify(obj) {\n    if (this.isFunction(obj)) {\n      return obj;\n    }\n    var $template = DOM.element(obj);\n    if ($template.prop('tagName') === 'SCRIPT') {\n      return function template() { return $template.text(); };\n    }\n    return function template() { return String(obj); };\n  },\n\n  defer: function(fn) { setTimeout(fn, 0); },\n\n  noop: function() {},\n\n  formatPrefix: function(prefix, noPrefix) {\n    return noPrefix ? '' : prefix + '-';\n  },\n\n  className: function(prefix, clazz, skipDot) {\n    return (skipDot ? '' : '.') + prefix + clazz;\n  },\n\n  escapeHighlightedString: function(str, highlightPreTag, highlightPostTag) {\n    highlightPreTag = highlightPreTag || '<em>';\n    var pre = document.createElement('div');\n    pre.appendChild(document.createTextNode(highlightPreTag));\n\n    highlightPostTag = highlightPostTag || '</em>';\n    var post = document.createElement('div');\n    post.appendChild(document.createTextNode(highlightPostTag));\n\n    var div = document.createElement('div');\n    div.appendChild(document.createTextNode(str));\n    return div.innerHTML\n      .replace(RegExp(escapeRegExp(pre.innerHTML), 'g'), highlightPreTag)\n      .replace(RegExp(escapeRegExp(post.innerHTML), 'g'), highlightPostTag);\n  }\n};\n", "const extractParams = ({\n  hitsPerPage,\n  postcodeSearch,\n  aroundLatLng,\n  aroundRadius,\n  aroundLatLngViaIP,\n  insideBoundingBox,\n  insidePolygon,\n  getRankingInfo,\n  countries,\n  language,\n  type,\n}) => {\n  const extracted = {\n    countries,\n    hitsPerPage: hitsPerPage || 5,\n    language: language || navigator.language.split('-')[0],\n    type,\n  };\n\n  if (Array.isArray(countries)) {\n    extracted.countries = extracted.countries.map((country) =>\n      country.toLowerCase()\n    );\n  }\n\n  if (typeof extracted.language === 'string') {\n    extracted.language = extracted.language.toLowerCase();\n  }\n\n  if (aroundLatLng) {\n    extracted.aroundLatLng = aroundLatLng;\n  } else if (aroundLatLngViaIP !== undefined) {\n    extracted.aroundLatLngViaIP = aroundLatLngViaIP;\n  }\n\n  if (postcodeSearch) {\n    extracted.restrictSearchableAttributes = 'postcode';\n  }\n\n  return {\n    ...extracted,\n    aroundRadius,\n    insideBoundingBox,\n    insidePolygon,\n    getRankingInfo,\n  };\n};\n\nconst extractControls = ({\n  useDeviceLocation = false,\n  computeQueryParams = (params) => params,\n  formatInputValue,\n  onHits = () => {},\n  onError = (e) => {\n    throw e;\n  },\n  onRateLimitReached,\n  onInvalidCredentials,\n}) => ({\n  useDeviceLocation,\n  computeQueryParams,\n  formatInputValue,\n  onHits,\n  onError,\n  onRateLimitReached,\n  onInvalidCredentials,\n});\n\nlet params = {};\nlet controls = {};\n\nconst configure = (configuration) => {\n  params = extractParams({ ...params, ...configuration });\n  controls = extractControls({ ...controls, ...configuration });\n\n  return { params, controls };\n};\n\nexport default configure;\n", "'use strict';\n\nmodule.exports = {\n  element: null\n};\n", "\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toString = Object.prototype.toString;\n\nmodule.exports = function forEach (obj, fn, ctx) {\n    if (toString.call(fn) !== '[object Function]') {\n        throw new TypeError('iterator must be a function');\n    }\n    var l = obj.length;\n    if (l === +l) {\n        for (var i = 0; i < l; i++) {\n            fn.call(ctx, obj[i], i, obj);\n        }\n    } else {\n        for (var k in obj) {\n            if (hasOwn.call(obj, k)) {\n                fn.call(ctx, obj[k], k, obj);\n            }\n        }\n    }\n};\n\n", "module.exports = function clone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n};\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "export default '1.19.0';\n", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 14 20\\\"><path d=\\\"M7 0C3.13 0 0 3.13 0 7c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5C5.62 9.5 4.5 8.38 4.5 7S5.62 4.5 7 4.5 9.5 5.62 9.5 7 8.38 9.5 7 9.5z\\\"/></svg>\\n\";", "export default function formatInputValue({\n  administrative,\n  city,\n  country,\n  name,\n  type,\n}) {\n  const out = `${name}${type !== 'country' && country !== undefined ? ',' : ''}\n ${city ? `${city},` : ''}\n ${administrative ? `${administrative},` : ''}\n ${country ? country : ''}`\n    .replace(/\\s*\\n\\s*/g, ' ')\n    .trim();\n  return out;\n}\n", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 18 19\\\"><path d=\\\"M12 9V3L9 0 6 3v2H0v14h18V9h-6zm-8 8H2v-2h2v2zm0-4H2v-2h2v2zm0-4H2V7h2v2zm6 8H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V7h2v2zm0-4H8V3h2v2zm6 12h-2v-2h2v2zm0-4h-2v-2h2v2z\\\"/></svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 20 20\\\">\\n  <path d=\\\"M10 0C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zM9 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L7 13v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H6V8h2c.55 0 1-.45 1-1V5h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\\\"/>\\n</svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 54.9 50.5\\\"><path d=\\\"M9.6 12.7H8.5c-2.3 0-4.1 1.9-4.1 4.1v1.1c0 2.2 1.8 4 4 4.1v21.7h-.7c-1.3 0-2.3 1-2.3 2.3h7.1c0-1.3-1-2.3-2.3-2.3h-.5V22.1c2.2-.1 4-1.9 4-4.1v-1.1c0-2.3-1.8-4.2-4.1-4.2zM46 7.6h-7.5c0-1.8-1.5-3.3-3.3-3.3h-3.6c-1.8 0-3.3 1.5-3.3 3.3H21c-2.5 0-4.6 2-4.6 4.6v26.3c0 1.7 1.3 3.1 3 3.1h.8v1.6c0 1.7 1.4 3.1 3.1 3.1 1.7 0 3-1.4 3-3.1v-1.6h14.3v1.6c0 1.7 1.4 3.1 3.1 3.1 1.7 0 3.1-1.4 3.1-3.1v-1.6h.8c1.7 0 3.1-1.4 3.1-3.1V12.2c-.2-2.5-2.2-4.6-4.7-4.6zm-27.4 4.6c0-1.3 1.1-2.4 2.4-2.4h25c1.3 0 2.4 1.1 2.4 2.4v.3c0 1.3-1.1 2.4-2.4 2.4H21c-1.3 0-2.4-1.1-2.4-2.4v-.3zM21 38c-1.5 0-2.7-1.2-2.7-2.7 0-1.5 1.2-2.7 2.7-2.7 1.5 0 2.7 1.2 2.7 2.7 0 1.5-1.2 2.7-2.7 2.7zm0-10.1c-1.3 0-2.4-1.1-2.4-2.4v-6.6c0-1.3 1.1-2.4 2.4-2.4h25c1.3 0 2.4 1.1 2.4 2.4v6.6c0 1.3-1.1 2.4-2.4 2.4H21zm24.8 10c-1.5 0-2.7-1.2-2.7-2.7 0-1.5 1.2-2.7 2.7-2.7 1.5 0 2.7 1.2 2.7 2.7 0 1.5-1.2 2.7-2.7 2.7z\\\"/></svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 15 20\\\">\\n  <path d=\\\"M13.105 20l-2.366-3.354H4.26L1.907 20H0l3.297-4.787c-1.1-.177-2.196-1.287-2.194-2.642V2.68C1.1 1.28 2.317-.002 3.973 0h7.065c1.647-.002 2.863 1.28 2.86 2.676v9.895c.003 1.36-1.094 2.47-2.194 2.647L15 20h-1.895zM6.11 2h2.78c.264 0 .472-.123.472-.27v-.46c0-.147-.22-.268-.472-.27H6.11c-.252.002-.47.123-.47.27v.46c0 .146.206.27.47.27zm6.26 3.952V4.175c-.004-.74-.5-1.387-1.436-1.388H4.066c-.936 0-1.43.648-1.436 1.388v1.777c-.002.86.644 1.384 1.436 1.388h6.868c.793-.004 1.44-.528 1.436-1.388zm-8.465 5.386c-.69-.003-1.254.54-1.252 1.21-.002.673.56 1.217 1.252 1.222.697-.006 1.26-.55 1.262-1.22-.002-.672-.565-1.215-1.262-1.212zm8.42 1.21c-.005-.67-.567-1.213-1.265-1.21-.69-.003-1.253.54-1.25 1.21-.003.673.56 1.217 1.25 1.222.698-.006 1.26-.55 1.264-1.22z\\\"/>\\n</svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M12 .6L2.5 6.9h18.9L12 .6zM3.8 8.2c-.7 0-1.3.6-1.3 1.3v8.8L.3 22.1c-.2.3-.3.5-.3.6 0 .6.8.6 1.3.6h21.5c.4 0 1.3 0 1.3-.6 0-.2-.1-.3-.3-.6l-2.2-3.8V9.5c0-.7-.6-1.3-1.3-1.3H3.8zm2.5 2.5c.7 0 1.1.6 1.3 1.3v7.6H5.1V12c0-.7.5-1.3 1.2-1.3zm5.7 0c.7 0 1.3.6 1.3 1.3v7.6h-2.5V12c-.1-.7.5-1.3 1.2-1.3zm5.7 0c.7 0 1.3.6 1.3 1.3v7.6h-2.5V12c-.1-.7.5-1.3 1.2-1.3z\\\"/></svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M22.9 1.1s1.3.3-4.3 6.5l.7 3.8.2-.2c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l-1.2 1.2.3 1.7.1-.1c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l-1.1 1.1c.2 1.9.3 3.6.1 4.5 0 0-1.2 1.2-1.8.5 0 0-2.3-7.7-3.8-11.1-5.9 6-6.4 5.6-6.4 5.6s1.2 3.8-.2 5.2l-2.3-4.3h.1l-4.3-2.3c1.3-1.3 5.2-.2 5.2-.2s-.5-.4 5.6-6.3C8.9 7.7 1.2 5.5 1.2 5.5c-.7-.7.5-1.8.5-1.8.9-.2 2.6-.1 4.5.1l1.1-1.1c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l1.7.3 1.2-1.2c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l-.2.2 3.8.7c6.2-5.5 6.5-4.2 6.5-4.2z\\\"/></svg>\\n\";", "import addressIcon from './icons/address.svg';\nimport cityIcon from './icons/city.svg';\nimport countryIcon from './icons/country.svg';\nimport busIcon from './icons/bus.svg';\nimport trainIcon from './icons/train.svg';\nimport townhallIcon from './icons/townhall.svg';\nimport planeIcon from './icons/plane.svg';\n\nconst icons = {\n  address: addressIcon,\n  city: cityIcon,\n  country: countryIcon,\n  busStop: busIcon,\n  trainStation: trainIcon,\n  townhall: townhallIcon,\n  airport: planeIcon,\n};\n\nexport default function formatDropdownValue({ type, highlight }) {\n  const { name, administrative, city, country } = highlight;\n\n  const out = `<span class=\"ap-suggestion-icon\">${icons[type].trim()}</span>\n<span class=\"ap-name\">${name}</span>\n<span class=\"ap-address\">\n  ${[city, administrative, country]\n    .filter((token) => token !== undefined)\n    .join(', ')}</span>`.replace(/\\s*\\n\\s*/g, ' ');\n\n  return out;\n}\n", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"117\\\" height=\\\"17\\\" viewBox=\\\"0 0 130 19\\\"><g fill=\\\"none\\\" fill-rule=\\\"evenodd\\\"><g fill-rule=\\\"nonzero\\\"><path fill=\\\"#5468FF\\\" d=\\\"M59.399.044h13.299a2.372 2.372 0 0 1 2.377 2.364v13.234a2.372 2.372 0 0 1-2.377 2.364H59.399a2.372 2.372 0 0 1-2.377-2.364V2.403A2.368 2.368 0 0 1 59.399.044z\\\"/><path fill=\\\"#FFF\\\" d=\\\"M66.257 4.582c-2.815 0-5.1 2.272-5.1 5.078 0 2.806 2.284 5.072 5.1 5.072 2.815 0 5.1-2.272 5.1-5.078 0-2.806-2.279-5.072-5.1-5.072zm0 8.652c-1.983 0-3.593-1.602-3.593-3.574 0-1.972 1.61-3.574 3.593-3.574 1.983 0 3.593 1.602 3.593 3.574a3.582 3.582 0 0 1-3.593 3.574zm0-6.418V9.48c0 .***************.093l2.377-1.226c.055-.027.071-.093.044-.147a2.96 2.96 0 0 0-2.465-1.487c-.055 0-.11.044-.11.104h.001zm-3.33-1.956l-.312-.31a.783.783 0 0 0-1.106 0l-.372.37a.773.773 0 0 0 0 1.1l.307.305c.049.05.121.038.164-.01.181-.246.378-.48.597-.698.225-.223.455-.42.707-.599.055-.033.06-.109.016-.158h-.001zm5.001-.806v-.616a.781.781 0 0 0-.783-.779h-1.824a.78.78 0 0 0-.783.78v.631c0 .071.066.12.137.104a5.736 5.736 0 0 1 1.588-.223c.52 0 1.035.071 1.534.207a.106.106 0 0 0 .131-.104z\\\"/><path fill=\\\"#252C61\\\" d=\\\"M5.027 10.246c0 .698-.252 1.246-.757 1.644-.505.397-1.201.596-2.089.596-.888 0-1.615-.138-2.181-.414v-1.214c.358.168.739.301 1.141.397.403.097.778.145 1.125.145.508 0 .884-.097 1.125-.29a.945.945 0 0 0 .363-.779.978.978 0 0 0-.333-.747c-.222-.204-.68-.446-1.375-.725C1.33 8.57.825 8.24.531 7.865c-.294-.372-.44-.82-.44-1.343 0-.655.233-1.17.698-1.547.465-.376 1.09-.564 1.875-.564.752 0 1.5.165 2.245.494l-.408 1.047c-.698-.294-1.321-.44-1.869-.44-.415 0-.73.09-.945.271a.89.89 0 0 0-.322.717c0 .204.043.38.129.524.086.145.227.282.424.411.197.13.551.3 1.063.51.577.24.999.464 1.268.671.269.208.465.442.591.704.125.261.188.57.188.924l-.001.002zm3.98 2.24c-.924 0-1.646-.269-2.167-.808-.521-.539-.781-1.28-.781-2.226 0-.97.242-1.733.725-2.288.483-.555 1.148-.833 1.993-.833.784 0 1.404.238 1.858.714.455.476.682 1.132.682 1.966v.682H7.359c.018.577.174 1.02.467 1.33.294.31.707.464 1.241.464.351 0 .678-.033.98-.099a5.1 5.1 0 0 0 .975-.33v1.026a3.865 3.865 0 0 1-.935.312 5.723 5.723 0 0 1-1.08.091zm7.46-.107l-.252-.827h-.043c-.286.362-.575.608-.865.74-.29.13-.662.195-1.117.195-.584 0-1.039-.158-1.367-.473-.328-.315-.491-.76-.491-1.337 0-.612.227-1.074.682-1.386.455-.312 1.148-.482 2.079-.51l1.026-.032v-.317c0-.38-.089-.663-.266-.85-.177-.189-.452-.283-.824-.283-.304 0-.596.045-.875.134a6.68 6.68 0 0 0-.806.317l-.408-.902a4.414 4.414 0 0 1 1.058-.384 4.856 4.856 0 0 1 1.085-.132c.756 0 1.326.165 1.711.494.385.33.577.847.577 1.552v4.001h-.904zm5.677-6.048c.254 0 .464.018.628.054l-.124 1.176a2.383 2.383 0 0 0-.559-.064c-.505 0-.914.165-1.227.494-.313.33-.47.757-.47 1.284v3.104H19.13V6.44h.988l.167 1.047h.064c.197-.354.454-.636.771-.843a1.83 1.83 0 0 1 1.023-.312h.001zm4.125 6.155c-.899 0-1.582-.262-2.049-.787-.467-.525-.701-1.277-.701-2.259 0-.999.244-1.767.733-2.304.489-.537 1.195-.806 2.119-.806.627 0 1.191.116 1.692.35l-.381 1.014c-.534-.208-.974-.312-1.321-.312-1.028 0-1.542.682-1.542 2.046 0 .666.128 1.166.384 1.501.256.335.631.502 1.125.502a3.23 3.23 0 0 0 1.595-.419v1.101a2.53 2.53 0 0 1-.722.285 4.356 4.356 0 0 1-.932.086v.002zm8.277-.107h-1.268V8.727c0-.458-.092-.8-.277-1.026-.184-.226-.477-.338-.878-.338-.53 0-.919.158-1.168.475-.249.317-.373.848-.373 1.593v2.95H29.32V4.022h1.262v2.122c0 .34-.021.704-.064 1.09h.081a1.76 1.76 0 0 1 .717-.666c.306-.158.663-.236 1.072-.236 1.439 0 2.159.725 2.159 2.175v3.873l-.001-.002zm7.648-6.048c.741 0 1.319.27 1.732.806.414.537.62 1.291.62 2.261 0 .974-.209 1.732-.628 2.275-.419.542-1.001.814-1.746.814-.752 0-1.336-.27-1.751-.81h-.086l-.231.703h-.945V4.023h1.262V6.01l-.021.655-.032.553h.054c.401-.59.992-.886 1.772-.886zm2.917.107h1.375l1.208 3.368c.183.48.304.931.365 1.354h.043c.032-.197.091-.436.177-.717.086-.28.541-1.616 1.364-4.004h1.364l-2.541 6.73c-.462 1.235-1.232 1.853-2.31 1.853-.279 0-.551-.03-.816-.09v-1c.19.043.406.064.65.064.609 0 1.037-.353 1.284-1.058l.22-.559-2.385-5.94h.002zm-3.244.924c-.508 0-.875.15-1.098.448-.224.3-.339.8-.346 1.501v.086c0 .723.115 1.247.344 1.571.229.324.603.486 1.123.486.448 0 .787-.177 1.018-.532.231-.354.346-.867.346-1.536 0-1.35-.462-2.025-1.386-2.025l-.001.001zm-27.28 4.157c.458 0 .826-.128 1.104-.384.278-.256.416-.615.416-1.077v-.516l-.763.032c-.594.021-1.027.121-1.297.298s-.406.448-.406.814c0 .265.079.47.236.615.158.145.394.218.709.218h.001zM8.775 7.287c-.401 0-.722.127-.964.381s-.386.625-.432 1.112h2.696c-.007-.49-.125-.862-.354-1.115-.229-.252-.544-.379-.945-.379l-.001.001z\\\"/></g><path fill=\\\"#5468FF\\\" d=\\\"M102.162 13.784c0 1.455-.372 2.517-1.123 3.193-.75.676-1.895 1.013-3.44 1.013-.564 0-1.736-.109-2.673-.316l.345-1.689c.783.163 1.819.207 2.361.207.86 0 1.473-.174 1.84-.523.367-.349.548-.866.548-1.553v-.349a6.374 6.374 0 0 1-.838.316 4.151 4.151 0 0 1-1.194.158 4.515 4.515 0 0 1-1.616-.278 3.385 3.385 0 0 1-1.254-.817 3.744 3.744 0 0 1-.811-1.35c-.192-.54-.29-1.505-.29-2.213 0-.665.104-1.498.307-2.054a3.925 3.925 0 0 1 .904-1.433 4.124 4.124 0 0 1 1.441-.926 5.31 5.31 0 0 1 1.945-.365c.696 0 1.337.087 1.961.191a15.86 15.86 0 0 1 1.588.332v8.456h-.001zm-5.955-4.206c0 .893.197 1.885.592 2.3.394.413.904.62 1.528.62.34 0 .663-.049.964-.142a2.75 2.75 0 0 0 .734-.332v-5.29a8.531 8.531 0 0 0-1.413-.18c-.778-.022-1.369.294-1.786.801-.411.507-.619 1.395-.619 2.223zm16.121 0c0 .72-.104 1.264-.318 1.858a4.389 4.389 0 0 1-.904 1.52c-.389.42-.854.746-1.402.975-.548.23-1.391.36-1.813.36-.422-.005-1.26-.125-1.802-.36a4.088 4.088 0 0 1-1.397-.975 4.486 4.486 0 0 1-.909-1.52 5.037 5.037 0 0 1-.329-1.858c0-.719.099-1.41.318-1.999.219-.588.526-1.09.92-1.509.394-.42.865-.74 1.402-.97a4.547 4.547 0 0 1 1.786-.338 4.69 4.69 0 0 1 1.791.338c.548.23 1.019.55 1.402.97.389.42.69.921.909 1.51.23.587.345 1.28.345 1.998h.001zm-2.192.005c0-.92-.203-1.689-.597-2.223-.394-.539-.948-.806-1.654-.806-.707 0-1.26.267-1.654.806-.394.54-.586 1.302-.586 2.223 0 .932.197 1.558.592 2.098.394.545.948.812 1.654.812.707 0 1.26-.272 1.654-.812.394-.545.592-1.166.592-2.098h-.001zm6.963 4.708c-3.511.016-3.511-2.822-3.511-3.274L113.583.95l2.142-.338v10.003c0 .256 0 1.88 1.375 1.885v1.793h-.001zM120.873 14.291h-2.153V5.095l2.153-.338zM119.794 3.75c.718 0 1.304-.579 1.304-1.292 0-.714-.581-1.29-1.304-1.29-.723 0-1.304.577-1.304 1.29 0 .714.586 1.291 1.304 1.291zm6.431 1.012c.707 0 1.304.087 1.786.262.482.174.871.42 1.156.73.285.311.488.735.608 1.182.126.447.186.937.186 1.476v5.481a25.24 25.24 0 0 1-1.495.251c-.668.098-1.419.147-2.251.147a6.829 6.829 0 0 1-1.517-.158 3.213 3.213 0 0 1-1.178-.507 2.455 2.455 0 0 1-.761-.904c-.181-.37-.274-.893-.274-1.438 0-.523.104-.855.307-1.215.208-.36.487-.654.838-.883a3.609 3.609 0 0 1 1.227-.49 7.073 7.073 0 0 1 2.202-.103c.263.027.537.076.833.147v-.349c0-.245-.027-.479-.088-.697a1.486 1.486 0 0 0-.307-.583c-.148-.169-.34-.3-.581-.392a2.536 2.536 0 0 0-.915-.163c-.493 0-.942.06-1.353.131-.411.071-.75.153-1.008.245l-.257-1.749c.268-.093.668-.185 1.183-.278a9.335 9.335 0 0 1 1.66-.142h-.001zm.179 7.73c.657 0 1.145-.038 1.484-.104V10.22a5.097 5.097 0 0 0-1.978-.104c-.241.033-.46.098-.652.191a1.167 1.167 0 0 0-.466.392c-.121.17-.175.267-.175.523 0 .501.175.79.493.981.323.196.75.29 1.293.29h.001zM84.108 4.816c.707 0 1.304.087 1.786.262.482.174.871.42 1.156.73.29.316.487.735.608 1.182.126.447.186.937.186 1.476v5.481a25.24 25.24 0 0 1-1.495.251c-.668.098-1.419.147-2.251.147a6.829 6.829 0 0 1-1.517-.158 3.213 3.213 0 0 1-1.178-.507 2.455 2.455 0 0 1-.761-.904c-.181-.37-.274-.893-.274-1.438 0-.523.104-.855.307-1.215.208-.36.487-.654.838-.883a3.609 3.609 0 0 1 1.227-.49 7.073 7.073 0 0 1 2.202-.103c.257.027.537.076.833.147v-.349c0-.245-.027-.479-.088-.697a1.486 1.486 0 0 0-.307-.583c-.148-.169-.34-.3-.581-.392a2.536 2.536 0 0 0-.915-.163c-.493 0-.942.06-1.353.131-.411.071-.75.153-1.008.245l-.257-1.749c.268-.093.668-.185 1.183-.278a8.89 8.89 0 0 1 1.66-.142h-.001zm.185 7.736c.657 0 1.145-.038 1.484-.104V10.28a5.097 5.097 0 0 0-1.978-.104c-.241.033-.46.098-.652.191a1.167 1.167 0 0 0-.466.392c-.121.17-.175.267-.175.523 0 .501.175.79.493.981.318.191.75.29 1.293.29h.001zm8.683 1.738c-3.511.016-3.511-2.822-3.511-3.274L89.46.948 91.602.61v10.003c0 .256 0 1.88 1.375 1.885v1.793h-.001z\\\"/></g></svg>\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"12\\\" height=\\\"12\\\">\\n  <path fill=\\\"#797979\\\" fill-rule=\\\"evenodd\\\" d=\\\"M6.577.5L5.304.005 2.627 1.02 0 0l.992 2.767-.986 2.685.998 2.76-1 2.717.613.22 3.39-3.45.563.06.726-.69s-.717-.92-.91-1.86c.193-.146.184-.14.355-.285C4.1 1.93 6.58.5 6.58.5zm-4.17 11.354l.22.12 2.68-1.05 2.62 1.04 2.644-1.03 1.02-2.717-.33-.944s-1.13 1.26-3.44.878c-.174.29-.25.37-.25.37s-1.11-.31-1.683-.89c-.573.58-.795.71-.795.71l.08.634-2.76 2.89zm6.26-4.395c1.817 0 3.29-1.53 3.29-3.4 0-1.88-1.473-3.4-3.29-3.4s-3.29 1.52-3.29 3.4c0 1.87 1.473 3.4 3.29 3.4z\\\"/>\\n</svg>\\n\";", "import value from './formatInputValue';\nimport suggestion from './formatDropdownValue';\nimport algoliaLogo from './icons/algolia.svg';\nimport osmLogo from './icons/osm.svg';\n\nexport default {\n  footer: `<div class=\"ap-footer\">\n  <a href=\"https://www.algolia.com/places\" title=\"Search by Algolia\" class=\"ap-footer-algolia\">${algoliaLogo.trim()}</a>\n  using <a href=\"https://community.algolia.com/places/documentation.html#license\" class=\"ap-footer-osm\" title=\"Algolia Places data © OpenStreetMap contributors\">${osmLogo.trim()} <span>data</span></a>\n  </div>`,\n  value,\n  suggestion,\n};\n", "export default function findCountryCode(tags) {\n  for (let tagIndex = 0; tagIndex < tags.length; tagIndex++) {\n    const tag = tags[tagIndex];\n    const find = tag.match(/country\\/(.*)?/);\n    if (find) {\n      return find[1];\n    }\n  }\n\n  return undefined;\n}\n", "export default function findType(tags) {\n  const types = {\n    country: 'country',\n    city: 'city',\n    'amenity/bus_station': 'busStop',\n    'amenity/townhall': 'townhall',\n    'railway/station': 'trainStation',\n    'aeroway/aerodrome': 'airport',\n    'aeroway/terminal': 'airport',\n    'aeroway/gate': 'airport',\n  };\n\n  for (const t in types) {\n    if (tags.indexOf(t) !== -1) {\n      return types[t];\n    }\n  }\n\n  return 'address';\n}\n", "import findCountryCode from './findCountryCode';\nimport findType from './findType';\n\nfunction getBestHighlightedForm(highlightedValues) {\n  const defaultValue = highlightedValues[0].value;\n  // collect all other matches\n  const bestAttributes = [];\n  for (let i = 1; i < highlightedValues.length; ++i) {\n    if (highlightedValues[i].matchLevel !== 'none') {\n      bestAttributes.push({\n        index: i,\n        words: highlightedValues[i].matchedWords,\n      });\n    }\n  }\n  // no matches in this attribute, retrieve first value\n  if (bestAttributes.length === 0) {\n    return defaultValue;\n  }\n  // sort the matches by `desc(words), asc(index)`\n  bestAttributes.sort((a, b) => {\n    if (a.words > b.words) {\n      return -1;\n    } else if (a.words < b.words) {\n      return 1;\n    }\n    return a.index - b.index;\n  });\n  // and append the best match to the first value\n  return bestAttributes[0].index === 0\n    ? `${defaultValue} (${highlightedValues[bestAttributes[1].index].value})`\n    : `${highlightedValues[bestAttributes[0].index].value} (${defaultValue})`;\n}\n\nfunction getBestPostcode(postcodes, highlightedPostcodes) {\n  const defaultValue = highlightedPostcodes[0].value;\n  // collect all other matches\n  const bestAttributes = [];\n  for (let i = 1; i < highlightedPostcodes.length; ++i) {\n    if (highlightedPostcodes[i].matchLevel !== 'none') {\n      bestAttributes.push({\n        index: i,\n        words: highlightedPostcodes[i].matchedWords,\n      });\n    }\n  }\n  // no matches in this attribute, retrieve first value\n  if (bestAttributes.length === 0) {\n    return { postcode: postcodes[0], highlightedPostcode: defaultValue };\n  }\n  // sort the matches by `desc(words)`\n  bestAttributes.sort((a, b) => {\n    if (a.words > b.words) {\n      return -1;\n    } else if (a.words < b.words) {\n      return 1;\n    }\n    return a.index - b.index;\n  });\n\n  const postcode = postcodes[bestAttributes[0].index];\n  return {\n    postcode,\n    highlightedPostcode: highlightedPostcodes[bestAttributes[0].index].value,\n  };\n}\n\nexport default function formatHit({\n  formatInputValue,\n  hit,\n  hitIndex,\n  query,\n  rawAnswer,\n}) {\n  try {\n    const name = hit.locale_names[0];\n    const country = hit.country;\n    const administrative =\n      hit.administrative && hit.administrative[0] !== name\n        ? hit.administrative[0]\n        : undefined;\n    const city = hit.city && hit.city[0] !== name ? hit.city[0] : undefined;\n    const suburb =\n      hit.suburb && hit.suburb[0] !== name ? hit.suburb[0] : undefined;\n\n    const county =\n      hit.county && hit.county[0] !== name ? hit.county[0] : undefined;\n\n    const { postcode, highlightedPostcode } =\n      hit.postcode && hit.postcode.length\n        ? getBestPostcode(hit.postcode, hit._highlightResult.postcode)\n        : { postcode: undefined, highlightedPostcode: undefined };\n\n    const highlight = {\n      name: getBestHighlightedForm(hit._highlightResult.locale_names),\n      city: city\n        ? getBestHighlightedForm(hit._highlightResult.city)\n        : undefined,\n      administrative: administrative\n        ? getBestHighlightedForm(hit._highlightResult.administrative)\n        : undefined,\n      country: country ? hit._highlightResult.country.value : undefined,\n      suburb: suburb\n        ? getBestHighlightedForm(hit._highlightResult.suburb)\n        : undefined,\n      county: county\n        ? getBestHighlightedForm(hit._highlightResult.county)\n        : undefined,\n      postcode: highlightedPostcode,\n    };\n\n    const suggestion = {\n      name,\n      administrative,\n      county,\n      city,\n      suburb,\n      country,\n      countryCode: findCountryCode(hit._tags),\n      type: findType(hit._tags),\n      latlng: {\n        lat: hit._geoloc.lat,\n        lng: hit._geoloc.lng,\n      },\n      postcode,\n      postcodes: hit.postcode && hit.postcode.length ? hit.postcode : undefined,\n    };\n\n    // this is the value to put inside the <input value=\n    const value = formatInputValue(suggestion);\n\n    return {\n      ...suggestion,\n      highlight,\n      hit,\n      hitIndex,\n      query,\n      rawAnswer,\n      value,\n    };\n  } catch (e) {\n    /* eslint-disable no-console */\n    console.error('Could not parse object', hit);\n    console.error(e);\n    /* eslint-enable no-console */\n    return {\n      value: 'Could not parse object',\n    };\n  }\n}\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "'use strict';\n\n// This file hosts our error definitions\n// We use custom error \"types\" so that we can act on them when we need it\n// e.g.: if error instanceof errors.UnparsableJSON then..\n\nvar inherits = require('inherits');\n\nfunction AlgoliaSearchError(message, extraProperties) {\n  var forEach = require('foreach');\n\n  var error = this;\n\n  // try to get a stacktrace\n  if (typeof Error.captureStackTrace === 'function') {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    error.stack = (new Error()).stack || 'Cannot get a stacktrace, browser is too old';\n  }\n\n  this.name = 'AlgoliaSearchError';\n  this.message = message || 'Unknown error';\n\n  if (extraProperties) {\n    forEach(extraProperties, function addToErrorObject(value, key) {\n      error[key] = value;\n    });\n  }\n}\n\ninherits(AlgoliaSearchError, Error);\n\nfunction createCustomError(name, message) {\n  function AlgoliaSearchCustomError() {\n    var args = Array.prototype.slice.call(arguments, 0);\n\n    // custom message not set, use default\n    if (typeof args[0] !== 'string') {\n      args.unshift(message);\n    }\n\n    AlgoliaSearchError.apply(this, args);\n    this.name = 'AlgoliaSearch' + name + 'Error';\n  }\n\n  inherits(AlgoliaSearchCustomError, AlgoliaSearchError);\n\n  return AlgoliaSearchCustomError;\n}\n\n// late exports to let various fn defs and inherits take place\nmodule.exports = {\n  AlgoliaSearchError: AlgoliaSearchError,\n  UnparsableJSON: createCustomError(\n    'UnparsableJSON',\n    'Could not parse the incoming response as JSON, see err.more for details'\n  ),\n  RequestTimeout: createCustomError(\n    'RequestTimeout',\n    'Request timed out before getting a response'\n  ),\n  Network: createCustomError(\n    'Network',\n    'Network issue, see err.more for details'\n  ),\n  JSONPScriptFail: createCustomError(\n    'JSONPScriptFail',\n    '<script> was loaded but did not call our provided callback'\n  ),\n  ValidUntilNotFound: createCustomError(\n    'ValidUntilNotFound',\n    'The SecuredAPIKey does not have a validUntil parameter.'\n  ),\n  JSONPScriptError: createCustomError(\n    'JSONPScriptError',\n    '<script> unable to load due to an `error` event on it'\n  ),\n  ObjectNotFound: createCustomError(\n    'ObjectNotFound',\n    'Object not found'\n  ),\n  Unknown: createCustomError(\n    'Unknown',\n    'Unknown error occured'\n  )\n};\n", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n", "var foreach = require('foreach');\n\nmodule.exports = function map(arr, fn) {\n  var newArr = [];\n  foreach(arr, function(item, itemIndex) {\n    newArr.push(fn(item, itemIndex, arr));\n  });\n  return newArr;\n};\n", "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  'lightseagreen',\n  'forestgreen',\n  'goldenrod',\n  'dodgerblue',\n  'darkorchid',\n  'crimson'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n", "export default \".algolia-places {\\n  width: 100%;\\n}\\n\\n.ap-input, .ap-hint {\\n  width: 100%;\\n  padding-right: 35px;\\n  padding-left: 16px;\\n  line-height: 40px;\\n  height: 40px;\\n  border: 1px solid #CCC;\\n  border-radius: 3px;\\n  outline: none;\\n  font: inherit;\\n  appearance: none;\\n  -webkit-appearance: none;\\n  box-sizing: border-box;\\n}\\n\\n.ap-input::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n.ap-input::-ms-clear {\\n  display: none;\\n}\\n\\n.ap-input:hover ~ .ap-input-icon svg,\\n.ap-input:focus ~ .ap-input-icon svg,\\n.ap-input-icon:hover svg {\\n  fill: #aaaaaa;\\n}\\n\\n.ap-dropdown-menu {\\n  width: 100%;\\n  background: #ffffff;\\n  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n  margin-top: 3px;\\n  overflow: hidden;\\n}\\n\\n.ap-suggestion {\\n  cursor: pointer;\\n  height: 46px;\\n  line-height: 46px;\\n  padding-left: 18px;\\n  overflow: hidden;\\n}\\n\\n.ap-suggestion em {\\n  font-weight: bold;\\n  font-style: normal;\\n}\\n\\n.ap-address {\\n  font-size: smaller;\\n  margin-left: 12px;\\n  color: #aaaaaa;\\n}\\n\\n.ap-suggestion-icon {\\n  margin-right: 10px;\\n  width: 14px;\\n  height: 20px;\\n  vertical-align: middle;\\n}\\n\\n.ap-suggestion-icon svg {\\n  display: inherit;\\n  -webkit-transform: scale(0.9) translateY(2px);\\n          transform: scale(0.9) translateY(2px);\\n  fill: #cfcfcf;\\n}\\n\\n.ap-input-icon {\\n  border: 0;\\n  background: transparent;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 16px;\\n  outline: none;\\n}\\n\\n.ap-input-icon.ap-icon-pin {\\n  cursor: pointer;\\n}\\n\\n.ap-input-icon svg {\\n  fill: #cfcfcf;\\n  position: absolute;\\n  top: 50%;\\n  right: 0;\\n  -webkit-transform: translateY(-50%);\\n          transform: translateY(-50%);\\n}\\n\\n.ap-cursor {\\n  background: #efefef;\\n}\\n\\n.ap-cursor .ap-suggestion-icon svg {\\n  -webkit-transform: scale(1) translateY(2px);\\n          transform: scale(1) translateY(2px);\\n  fill: #aaaaaa;\\n}\\n\\n.ap-footer {\\n  opacity: .8;\\n  text-align: right;\\n  padding: .5em 1em .5em 0;\\n  font-size: 12px;\\n  line-height: 12px;\\n}\\n\\n.ap-footer a {\\n  color: inherit;\\n  text-decoration: none;\\n}\\n\\n.ap-footer a svg {\\n  vertical-align: middle;\\n}\\n\\n.ap-footer:hover {\\n  opacity: 1;\\n}\\n\";", "'use strict';\n\nvar immediate = require('immediate');\nvar splitter = /\\s+/;\n\nmodule.exports = {\n  onSync: onSync,\n  onAsync: onAsync,\n  off: off,\n  trigger: trigger\n};\n\nfunction on(method, types, cb, context) {\n  var type;\n\n  if (!cb) {\n    return this;\n  }\n\n  types = types.split(splitter);\n  cb = context ? bindContext(cb, context) : cb;\n\n  this._callbacks = this._callbacks || {};\n\n  while (type = types.shift()) {\n    this._callbacks[type] = this._callbacks[type] || {sync: [], async: []};\n    this._callbacks[type][method].push(cb);\n  }\n\n  return this;\n}\n\nfunction onAsync(types, cb, context) {\n  return on.call(this, 'async', types, cb, context);\n}\n\nfunction onSync(types, cb, context) {\n  return on.call(this, 'sync', types, cb, context);\n}\n\nfunction off(types) {\n  var type;\n\n  if (!this._callbacks) {\n    return this;\n  }\n\n  types = types.split(splitter);\n\n  while (type = types.shift()) {\n    delete this._callbacks[type];\n  }\n\n  return this;\n}\n\nfunction trigger(types) {\n  var type;\n  var callbacks;\n  var args;\n  var syncFlush;\n  var asyncFlush;\n\n  if (!this._callbacks) {\n    return this;\n  }\n\n  types = types.split(splitter);\n  args = [].slice.call(arguments, 1);\n\n  while ((type = types.shift()) && (callbacks = this._callbacks[type])) { // eslint-disable-line\n    syncFlush = getFlush(callbacks.sync, this, [type].concat(args));\n    asyncFlush = getFlush(callbacks.async, this, [type].concat(args));\n\n    if (syncFlush()) {\n      immediate(asyncFlush);\n    }\n  }\n\n  return this;\n}\n\nfunction getFlush(callbacks, context, args) {\n  return flush;\n\n  function flush() {\n    var cancelled;\n\n    for (var i = 0, len = callbacks.length; !cancelled && i < len; i += 1) {\n      // only cancel if the callback explicitly returns false\n      cancelled = callbacks[i].apply(context, args) === false;\n    }\n\n    return !cancelled;\n  }\n}\n\nfunction bindContext(fn, context) {\n  return fn.bind ?\n    fn.bind(context) :\n    function() { fn.apply(context, [].slice.call(arguments, 0)); };\n}\n", "'use strict';\n\nvar _ = require('../common/utils.js');\n\nvar css = {\n  wrapper: {\n    position: 'relative',\n    display: 'inline-block'\n  },\n  hint: {\n    position: 'absolute',\n    top: '0',\n    left: '0',\n    borderColor: 'transparent',\n    boxShadow: 'none',\n    // #741: fix hint opacity issue on iOS\n    opacity: '1'\n  },\n  input: {\n    position: 'relative',\n    verticalAlign: 'top',\n    backgroundColor: 'transparent'\n  },\n  inputWithNoHint: {\n    position: 'relative',\n    verticalAlign: 'top'\n  },\n  dropdown: {\n    position: 'absolute',\n    top: '100%',\n    left: '0',\n    zIndex: '100',\n    display: 'none'\n  },\n  suggestions: {\n    display: 'block'\n  },\n  suggestion: {\n    whiteSpace: 'nowrap',\n    cursor: 'pointer'\n  },\n  suggestionChild: {\n    whiteSpace: 'normal'\n  },\n  ltr: {\n    left: '0',\n    right: 'auto'\n  },\n  rtl: {\n    left: 'auto',\n    right: '0'\n  },\n  defaultClasses: {\n    root: 'algolia-autocomplete',\n    prefix: 'aa',\n    noPrefix: false,\n    dropdownMenu: 'dropdown-menu',\n    input: 'input',\n    hint: 'hint',\n    suggestions: 'suggestions',\n    suggestion: 'suggestion',\n    cursor: 'cursor',\n    dataset: 'dataset',\n    empty: 'empty'\n  },\n  // will be merged with the default ones if appendTo is used\n  appendTo: {\n    wrapper: {\n      position: 'absolute',\n      zIndex: '100',\n      display: 'none'\n    },\n    input: {},\n    inputWithNoHint: {},\n    dropdown: {\n      display: 'block'\n    }\n  }\n};\n\n// ie specific styling\nif (_.isMsie()) {\n  // ie6-8 (and 9?) doesn't fire hover and click events for elements with\n  // transparent backgrounds, for a workaround, use 1x1 transparent gif\n  _.mixin(css.input, {\n    backgroundImage: 'url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)'\n  });\n}\n\n// ie7 and under specific styling\nif (_.isMsie() && _.isMsie() <= 7) {\n  // if someone can tell me why this is necessary to align\n  // the hint with the query in ie7, i'll send you $5 - @JakeHarding\n  _.mixin(css.input, {marginTop: '-1px'});\n}\n\nmodule.exports = css;\n", "var containers = []; // will store container HTMLElement references\nvar styleElements = []; // will store {prepend: HTMLElement, append: HTMLElement}\n\nvar usage = 'insert-css: You need to provide a CSS string. Usage: insertCss(cssString[, options]).';\n\nfunction insertCss(css, options) {\n    options = options || {};\n\n    if (css === undefined) {\n        throw new Error(usage);\n    }\n\n    var position = options.prepend === true ? 'prepend' : 'append';\n    var container = options.container !== undefined ? options.container : document.querySelector('head');\n    var containerId = containers.indexOf(container);\n\n    // first time we see this container, create the necessary entries\n    if (containerId === -1) {\n        containerId = containers.push(container) - 1;\n        styleElements[containerId] = {};\n    }\n\n    // try to get the correponding container + position styleElement, create it otherwise\n    var styleElement;\n\n    if (styleElements[containerId] !== undefined && styleElements[containerId][position] !== undefined) {\n        styleElement = styleElements[containerId][position];\n    } else {\n        styleElement = styleElements[containerId][position] = createStyleElement();\n\n        if (position === 'prepend') {\n            container.insertBefore(styleElement, container.childNodes[0]);\n        } else {\n            container.appendChild(styleElement);\n        }\n    }\n\n    // strip potential UTF-8 BOM if css was read from a file\n    if (css.charCodeAt(0) === 0xFEFF) { css = css.substr(1, css.length); }\n\n    // actually add the stylesheet\n    if (styleElement.styleSheet) {\n        styleElement.styleSheet.cssText += css\n    } else {\n        styleElement.textContent += css;\n    }\n\n    return styleElement;\n};\n\nfunction createStyleElement() {\n    var styleElement = document.createElement('style');\n    styleElement.setAttribute('type', 'text/css');\n    return styleElement;\n}\n\nmodule.exports = insertCss;\nmodule.exports.insertCss = insertCss;\n", "import configure from './configure';\nimport formatHit from './formatHit';\nimport version from './version';\n\nexport default function createAutocompleteSource({\n  algoliasearch,\n  clientOptions,\n  apiKey,\n  appId,\n  hitsPerPage,\n  postcodeSearch,\n  aroundLatLng,\n  aroundRadius,\n  aroundLatLngViaIP,\n  insideBoundingBox,\n  insidePolygon,\n  getRankingInfo,\n  countries,\n  formatInputValue,\n  computeQueryParams = (params) => params,\n  useDeviceLocation = false,\n  language = navigator.language.split('-')[0],\n  onHits = () => {},\n  onError = (e) => {\n    throw e;\n  },\n  onRateLimitReached,\n  onInvalidCredentials,\n  type,\n}) {\n  const placesClient = algoliasearch.initPlaces(appId, apiKey, clientOptions);\n  placesClient.as.addAlgoliaAgent(`Algolia Places ${version}`);\n\n  const configuration = configure({\n    hitsPerPage,\n    type,\n    postcodeSearch,\n    countries,\n    language,\n    aroundLatLng,\n    aroundRadius,\n    aroundLatLngViaIP,\n    insideBoundingBox,\n    insidePolygon,\n    getRankingInfo,\n    formatInputValue,\n    computeQueryParams,\n    useDeviceLocation,\n    onHits,\n    onError,\n    onRateLimitReached,\n    onInvalidCredentials,\n  });\n\n  let params = configuration.params;\n  let controls = configuration.controls;\n\n  let userCoords;\n  let tracker = null;\n\n  if (controls.useDeviceLocation) {\n    tracker = navigator.geolocation.watchPosition(({ coords }) => {\n      userCoords = `${coords.latitude},${coords.longitude}`;\n    });\n  }\n\n  function searcher(query, cb) {\n    const searchParams = {\n      ...params,\n      query,\n    };\n\n    if (userCoords) {\n      searchParams.aroundLatLng = userCoords;\n    }\n\n    return placesClient\n      .search(controls.computeQueryParams(searchParams))\n      .then((content) => {\n        const hits = content.hits.map((hit, hitIndex) =>\n          formatHit({\n            formatInputValue: controls.formatInputValue,\n            hit,\n            hitIndex,\n            query,\n            rawAnswer: content,\n          })\n        );\n\n        controls.onHits({\n          hits,\n          query,\n          rawAnswer: content,\n        });\n\n        return hits;\n      })\n      .then(cb)\n      .catch((e) => {\n        if (\n          e.statusCode === 403 &&\n          e.message === 'Invalid Application-ID or API key'\n        ) {\n          controls.onInvalidCredentials();\n          return;\n        } else if (e.statusCode === 429) {\n          controls.onRateLimitReached();\n          return;\n        }\n\n        controls.onError(e);\n      });\n  }\n\n  searcher.configure = (partial) => {\n    const updated = configure({ ...params, ...controls, ...partial });\n\n    params = updated.params;\n    controls = updated.controls;\n\n    if (controls.useDeviceLocation && tracker === null) {\n      tracker = navigator.geolocation.watchPosition(({ coords }) => {\n        userCoords = `${coords.latitude},${coords.longitude}`;\n      });\n    } else if (!controls.useDeviceLocation && tracker !== null) {\n      navigator.geolocation.clearWatch(tracker);\n      tracker = null;\n      userCoords = null;\n    }\n  };\n  return searcher;\n}\n", "import createAutocompleteSource from './createAutocompleteSource';\nimport defaultTemplates from './defaultTemplates';\n\nexport default function createAutocompleteDataset(options) {\n  const templates = {\n    ...defaultTemplates,\n    ...options.templates,\n  };\n\n  const source = createAutocompleteSource({\n    ...options,\n    formatInputValue: templates.value,\n    templates: undefined,\n  });\n\n  return {\n    source,\n    templates,\n    displayKey: 'value',\n    name: 'places',\n    cache: false,\n  };\n}\n", "'use strict';\n\nvar AlgoliaSearchCore = require('../../AlgoliaSearchCore.js');\nvar createAlgoliasearch = require('../createAlgoliasearch.js');\n\nmodule.exports = createAlgoliasearch(AlgoliaSearchCore, 'Browser (lite)');\n", "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n", "module.exports = buildSearchMethod;\n\nvar errors = require('./errors.js');\n\n/**\n * Creates a search method to be used in clients\n * @param {string} queryParam the name of the attribute used for the query\n * @param {string} url the url\n * @return {function} the search method\n */\nfunction buildSearchMethod(queryParam, url) {\n  /**\n   * The search method. Prepares the data and send the query to Algolia.\n   * @param {string} query the string used for query search\n   * @param {object} args additional parameters to send with the search\n   * @param {function} [callback] the callback to be called with the client gets the answer\n   * @return {undefined|Promise} If the callback is not provided then this methods returns a Promise\n   */\n  return function search(query, args, callback) {\n    // warn V2 users on how to search\n    if (typeof query === 'function' && typeof args === 'object' ||\n      typeof callback === 'object') {\n      // .search(query, params, cb)\n      // .search(cb, params)\n      throw new errors.AlgoliaSearchError('index.search usage is index.search(query, params, cb)');\n    }\n\n    // Normalizing the function signature\n    if (arguments.length === 0 || typeof query === 'function') {\n      // Usage : .search(), .search(cb)\n      callback = query;\n      query = '';\n    } else if (arguments.length === 1 || typeof args === 'function') {\n      // Usage : .search(query/args), .search(query, cb)\n      callback = args;\n      args = undefined;\n    }\n    // At this point we have 3 arguments with values\n\n    // Usage : .search(args) // careful: typeof null === 'object'\n    if (typeof query === 'object' && query !== null) {\n      args = query;\n      query = undefined;\n    } else if (query === undefined || query === null) { // .search(undefined/null)\n      query = '';\n    }\n\n    var params = '';\n\n    if (query !== undefined) {\n      params += queryParam + '=' + encodeURIComponent(query);\n    }\n\n    var additionalUA;\n    if (args !== undefined) {\n      if (args.additionalUA) {\n        additionalUA = args.additionalUA;\n        delete args.additionalUA;\n      }\n      // `_getSearchParams` will augment params, do not be fooled by the = versus += from previous if\n      params = this.as._getSearchParams(args, params);\n    }\n\n\n    return this._search(params, url, callback, additionalUA);\n  };\n}\n", "module.exports = function omit(obj, test) {\n  var keys = require('object-keys');\n  var foreach = require('foreach');\n\n  var filtered = {};\n\n  foreach(keys(obj), function doFilter(keyName) {\n    if (test(keyName) !== true) {\n      filtered[keyName] = obj[keyName];\n    }\n  });\n\n  return filtered;\n};\n", "'use strict';\n\nvar toStr = Object.prototype.toString;\n\nmodule.exports = function isArguments(value) {\n\tvar str = toStr.call(value);\n\tvar isArgs = str === '[object Arguments]';\n\tif (!isArgs) {\n\t\tisArgs = str !== '[object Array]' &&\n\t\t\tvalue !== null &&\n\t\t\ttypeof value === 'object' &&\n\t\t\ttypeof value.length === 'number' &&\n\t\t\tvalue.length >= 0 &&\n\t\t\ttoStr.call(value.callee) === '[object Function]';\n\t}\n\treturn isArgs;\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar stringifyPrimitive = function(v) {\n  switch (typeof v) {\n    case 'string':\n      return v;\n\n    case 'boolean':\n      return v ? 'true' : 'false';\n\n    case 'number':\n      return isFinite(v) ? v : '';\n\n    default:\n      return '';\n  }\n};\n\nmodule.exports = function(obj, sep, eq, name) {\n  sep = sep || '&';\n  eq = eq || '=';\n  if (obj === null) {\n    obj = undefined;\n  }\n\n  if (typeof obj === 'object') {\n    return map(objectKeys(obj), function(k) {\n      var ks = encodeURIComponent(stringifyPrimitive(k)) + eq;\n      if (isArray(obj[k])) {\n        return map(obj[k], function(v) {\n          return ks + encodeURIComponent(stringifyPrimitive(v));\n        }).join(sep);\n      } else {\n        return ks + encodeURIComponent(stringifyPrimitive(obj[k]));\n      }\n    }).join(sep);\n\n  }\n\n  if (!name) return '';\n  return encodeURIComponent(stringifyPrimitive(name)) + eq +\n         encodeURIComponent(stringifyPrimitive(obj));\n};\n\nvar isArray = Array.isArray || function (xs) {\n  return Object.prototype.toString.call(xs) === '[object Array]';\n};\n\nfunction map (xs, f) {\n  if (xs.map) return xs.map(f);\n  var res = [];\n  for (var i = 0; i < xs.length; i++) {\n    res.push(f(xs[i], i));\n  }\n  return res;\n}\n\nvar objectKeys = Object.keys || function (obj) {\n  var res = [];\n  for (var key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) res.push(key);\n  }\n  return res;\n};\n", "'use strict';\n\nvar namespace = 'autocomplete:';\n\nvar _ = require('../common/utils.js');\nvar DOM = require('../common/dom.js');\n\n// constructor\n// -----------\n\nfunction EventBus(o) {\n  if (!o || !o.el) {\n    _.error('EventBus initialized without el');\n  }\n\n  this.$el = DOM.element(o.el);\n}\n\n// instance methods\n// ----------------\n\n_.mixin(EventBus.prototype, {\n\n  // ### public\n\n  trigger: function(type, suggestion, dataset, context) {\n    var event = _.Event(namespace + type);\n    this.$el.trigger(event, [suggestion, dataset, context]);\n    return event;\n  }\n});\n\nmodule.exports = EventBus;\n", "'use strict';\n\nmodule.exports = {\n  wrapper: '<span class=\"%ROOT%\"></span>',\n  dropdown: '<span class=\"%PREFIX%%DROPDOWN_MENU%\"></span>',\n  dataset: '<div class=\"%PREFIX%%DATASET%-%CLASS%\"></div>',\n  suggestions: '<span class=\"%PREFIX%%SUGGESTIONS%\"></span>',\n  suggestion: '<div class=\"%PREFIX%%SUGGESTION%\"></div>'\n};\n", "module.exports = \"0.37.1\";\n", "'use strict';\n\nmodule.exports = function parseAlgoliaClientVersion(agent) {\n  var parsed =\n    // User agent for algoliasearch >= 3.33.0\n    agent.match(/Algolia for JavaScript \\((\\d+\\.)(\\d+\\.)(\\d+)\\)/) ||\n    // User agent for algoliasearch < 3.33.0\n    agent.match(/Algolia for vanilla JavaScript (\\d+\\.)(\\d+\\.)(\\d+)/);\n\n  if (parsed) {\n    return [parsed[1], parsed[2], parsed[3]];\n  }\n\n  return undefined;\n};\n", "// polyfill for navigator.language (IE <= 10)\n// not polyfilled by https://cdn.polyfill.io/v2/docs/\n\n// Defined: http://www.whatwg.org/specs/web-apps/current-work/multipage/timers.html#navigatorlanguage\n//   with allowable values at http://www.ietf.org/rfc/bcp/bcp47.txt\n// Note that the HTML spec suggests that anonymizing services return \"en-US\" by default for\n//   user privacy (so your app may wish to provide a means of changing the locale)\nif (!('language' in navigator)) {\n  navigator.language =\n    // IE 10 in IE8 mode on Windows 7 uses upper-case in\n    // navigator.userLanguage country codes but per\n    // http://msdn.microsoft.com/en-us/library/ie/ms533052.aspx (via\n    // http://msdn.microsoft.com/en-us/library/ie/ms534713.aspx), they\n    // appear to be in lower case, so we bring them into harmony with navigator.language.\n    (navigator.userLanguage &&\n      navigator.userLanguage.replace(\n        /-[a-z]{2}$/,\n        String.prototype.toUpperCase\n      )) ||\n    'en-US'; // Default for anonymizing services: http://www.whatwg.org/specs/web-apps/current-work/multipage/timers.html#navigatorlanguage\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n", "'use strict';\n\nmodule.exports = require('./src/standalone/');\n", "export default \"<svg width=\\\"12\\\" height=\\\"12\\\" viewBox=\\\"0 0 12 12\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M.566 1.698L0 1.13 1.132 0l.565.566L6 4.868 10.302.566 10.868 0 12 1.132l-.566.565L7.132 6l4.302 4.3.566.568L10.868 12l-.565-.566L6 7.132l-4.3 4.302L1.13 12 0 10.868l.566-.565L4.868 6 .566 1.698z\\\"/></svg>\\n\";", "export default {\n  multiContainers: `Algolia Places: 'container' must point to a single <input> element.\nExample: instantiate the library twice if you want to bind two <inputs>.\n\nSee https://community.algolia.com/places/documentation.html#api-options-container`,\n  badContainer: `Algolia Places: 'container' must point to an <input> element.\n\nSee https://community.algolia.com/places/documentation.html#api-options-container`,\n  rateLimitReached: `Algolia Places: Current rate limit reached.\n\nSign up for a free 100,000 queries/month account at\nhttps://www.algolia.com/users/sign_up/places.\n\nOr upgrade your 100,000 queries/month plan by contacting us at\nhttps://community.algolia.com/places/contact.html.`,\n  invalidCredentials: `The APP ID or API key provided is invalid.`,\n  invalidAppId: `Your APP ID is invalid. A Places APP ID starts with 'pl'. You must create a valid Places app first.\n\nCreate a free Places app here: https://www.algolia.com/users/sign_up/places`,\n};\n", "import configure from './configure';\nimport formatHit from './formatHit';\nimport version from './version';\nimport defaultTemplates from './defaultTemplates';\n\nconst filterApplicableParams = (params) => {\n  const { hitsPerPage, aroundLatLng, getRankingInfo, language } = params;\n\n  const filtered = {};\n\n  if (typeof hitsPerPage === 'number') {\n    filtered.hitsPerPage = hitsPerPage;\n  }\n\n  if (typeof language === 'string') {\n    filtered.language = language;\n  }\n\n  if (typeof getRankingInfo === 'boolean') {\n    filtered.getRankingInfo = getRankingInfo;\n  }\n  if (typeof aroundLatLng === 'string') {\n    filtered.aroundLatLng = aroundLatLng;\n  }\n\n  return filtered;\n};\n\nconst createReverseGeocodingSource = ({\n  algoliasearch,\n  clientOptions,\n  apiKey,\n  appId,\n  hitsPerPage,\n  aroundLatLng,\n  getRankingInfo,\n  formatInputValue = defaultTemplates.value,\n  language = navigator.language.split('-')[0],\n  onHits = () => {},\n  onError = (e) => {\n    throw e;\n  },\n  onRateLimitReached,\n  onInvalidCredentials,\n}) => {\n  const placesClient = algoliasearch.initPlaces(appId, apiKey, clientOptions);\n  placesClient.as.addAlgoliaAgent(`Algolia Places ${version}`);\n\n  const configuration = configure({\n    apiKey,\n    appId,\n    hitsPerPage,\n    aroundLatLng,\n    getRankingInfo,\n    language,\n    formatInputValue,\n    onHits,\n    onError,\n    onRateLimitReached,\n    onInvalidCredentials,\n  });\n\n  let params = filterApplicableParams(configuration.params);\n  let controls = configuration.controls;\n\n  const searcher = (queryAroundLatLng, cb) => {\n    const finalAroundLatLng = queryAroundLatLng || params.aroundLatLng;\n\n    if (!finalAroundLatLng) {\n      const error = new Error(\n        'A location must be provided for reverse geocoding'\n      );\n      return Promise.reject(error);\n    }\n\n    return placesClient\n      .reverse({ ...params, aroundLatLng: finalAroundLatLng })\n      .then((content) => {\n        const hits = content.hits.map((hit, hitIndex) =>\n          formatHit({\n            formatInputValue: controls.formatInputValue,\n            hit,\n            hitIndex,\n            query: finalAroundLatLng,\n            rawAnswer: content,\n          })\n        );\n\n        controls.onHits({\n          hits,\n          query: finalAroundLatLng,\n          rawAnswer: content,\n        });\n\n        return hits;\n      })\n      .then(cb)\n      .catch((e) => {\n        if (\n          e.statusCode === 403 &&\n          e.message === 'Invalid Application-ID or API key'\n        ) {\n          controls.onInvalidCredentials();\n          return;\n        } else if (e.statusCode === 429) {\n          controls.onRateLimitReached();\n          return;\n        }\n\n        controls.onError(e);\n      });\n  };\n\n  searcher.configure = (partial) => {\n    const updated = configure({ ...params, ...controls, ...partial });\n\n    params = filterApplicableParams(updated.params);\n    controls = updated.controls;\n\n    return searcher;\n  };\n\n  return searcher;\n};\n\nexport default createReverseGeocodingSource;\n", "import EventEmitter from 'events';\n\nimport algoliasearch from 'algoliasearch/src/browser/builds/algoliasearchLite';\nimport autocomplete from 'autocomplete.js';\n\nimport './navigatorLanguage';\n\nimport createAutocompleteDataset from './createAutocompleteDataset';\n\nimport clearIcon from './icons/clear.svg';\nimport pinIcon from './icons/address.svg';\n\nimport css from './places.css';\nimport insertCss from 'insert-css';\ninsertCss(css, { prepend: true });\n\nimport errors from './errors';\nimport createReverseGeocodingSource from './createReverseGeocodingSource';\n\nconst applyAttributes = (elt, attrs) => {\n  Object.entries(attrs).forEach(([name, value]) => {\n    elt.setAttribute(name, `${value}`);\n  });\n\n  return elt;\n};\n\nexport default function places(options) {\n  const {\n    container,\n    style,\n    accessibility,\n    autocompleteOptions: userAutocompleteOptions = {},\n  } = options;\n\n  // multiple DOM elements targeted\n  if (container instanceof NodeList) {\n    if (container.length > 1) {\n      throw new Error(errors.multiContainers);\n    }\n\n    // if single node NodeList received, resolve to the first one\n    return places({ ...options, container: container[0] });\n  }\n\n  // container sent as a string, resolve it for multiple DOM elements issue\n  if (typeof container === 'string') {\n    const resolvedContainer = document.querySelectorAll(container);\n    return places({ ...options, container: resolvedContainer });\n  }\n\n  // if not an <input>, error\n  if (!(container instanceof HTMLInputElement)) {\n    throw new Error(errors.badContainer);\n  }\n\n  const placesInstance = new EventEmitter();\n  const prefix = `ap${style === false ? '-nostyle' : ''}`;\n\n  const autocompleteOptions = {\n    autoselect: true,\n    hint: false,\n    cssClasses: {\n      root: `algolia-places${style === false ? '-nostyle' : ''}`,\n      prefix,\n    },\n    debug: process.env.NODE_ENV === 'development',\n    ...userAutocompleteOptions,\n  };\n\n  const autocompleteDataset = createAutocompleteDataset({\n    ...options,\n    algoliasearch,\n    onHits: ({ hits, rawAnswer, query }) =>\n      placesInstance.emit('suggestions', {\n        rawAnswer,\n        query,\n        suggestions: hits,\n      }),\n    onError: (e) => placesInstance.emit('error', e),\n    onRateLimitReached: () => {\n      const listeners = placesInstance.listenerCount('limit');\n      if (listeners === 0) {\n        console.log(errors.rateLimitReached); // eslint-disable-line no-console\n        return;\n      }\n\n      placesInstance.emit('limit', { message: errors.rateLimitReached });\n    },\n    onInvalidCredentials: () => {\n      if (options && options.appId && options.appId.startsWith('pl')) {\n        console.error(errors.invalidCredentials); // eslint-disable-line no-console\n      } else {\n        console.error(errors.invalidAppId); // eslint-disable-line no-console\n      }\n    },\n    container: undefined,\n  });\n\n  const autocompleteInstance = autocomplete(\n    container,\n    autocompleteOptions,\n    autocompleteDataset\n  );\n  const autocompleteContainer = container.parentNode;\n\n  const autocompleteChangeEvents = ['selected', 'autocompleted'];\n\n  autocompleteChangeEvents.forEach((eventName) => {\n    autocompleteInstance.on(`autocomplete:${eventName}`, (_, suggestion) => {\n      placesInstance.emit('change', {\n        rawAnswer: suggestion.rawAnswer,\n        query: suggestion.query,\n        suggestion,\n        suggestionIndex: suggestion.hitIndex,\n      });\n    });\n  });\n  autocompleteInstance.on('autocomplete:cursorchanged', (_, suggestion) => {\n    placesInstance.emit('cursorchanged', {\n      rawAnswer: suggestion.rawAnswer,\n      query: suggestion.query,\n      suggestion,\n      suggestionIndex: suggestion.hitIndex,\n    });\n  });\n\n  const clear = document.createElement('button');\n  clear.setAttribute('type', 'button');\n  clear.setAttribute('aria-label', 'clear');\n  if (\n    accessibility &&\n    accessibility.clearButton &&\n    accessibility.clearButton instanceof Object\n  ) {\n    applyAttributes(clear, accessibility.clearButton);\n  }\n  clear.classList.add(`${prefix}-input-icon`);\n  clear.classList.add(`${prefix}-icon-clear`);\n  clear.innerHTML = clearIcon;\n  autocompleteContainer.appendChild(clear);\n  clear.style.display = 'none';\n\n  const pin = document.createElement('button');\n  pin.setAttribute('type', 'button');\n  pin.setAttribute('aria-label', 'focus');\n  if (\n    accessibility &&\n    accessibility.pinButton &&\n    accessibility.pinButton instanceof Object\n  ) {\n    applyAttributes(pin, accessibility.pinButton);\n  }\n  pin.classList.add(`${prefix}-input-icon`);\n  pin.classList.add(`${prefix}-icon-pin`);\n  pin.innerHTML = pinIcon;\n  autocompleteContainer.appendChild(pin);\n\n  pin.addEventListener('click', () => {\n    autocompleteDataset.source.configure({ useDeviceLocation: true });\n    autocompleteInstance.focus();\n    placesInstance.emit('locate');\n  });\n\n  clear.addEventListener('click', () => {\n    autocompleteInstance.autocomplete.setVal('');\n    autocompleteInstance.focus();\n    clear.style.display = 'none';\n    pin.style.display = '';\n    placesInstance.emit('clear');\n  });\n\n  let previousQuery = '';\n\n  const inputListener = () => {\n    const query = autocompleteInstance.val();\n    if (query === '') {\n      pin.style.display = '';\n      clear.style.display = 'none';\n      if (previousQuery !== query) {\n        placesInstance.emit('clear');\n      }\n    } else {\n      clear.style.display = '';\n      pin.style.display = 'none';\n    }\n    previousQuery = query;\n  };\n\n  autocompleteContainer\n    .querySelector(`.${prefix}-input`)\n    .addEventListener('input', inputListener);\n\n  const autocompleteIsomorphicMethods = ['open', 'close'];\n  autocompleteIsomorphicMethods.forEach((methodName) => {\n    placesInstance[methodName] = (...args) => {\n      autocompleteInstance.autocomplete[methodName](...args);\n    };\n  });\n\n  placesInstance.getVal = () => {\n    return autocompleteInstance.val();\n  };\n\n  placesInstance.destroy = (...args) => {\n    autocompleteContainer\n      .querySelector(`.${prefix}-input`)\n      .removeEventListener('input', inputListener);\n\n    autocompleteInstance.autocomplete.destroy(...args);\n  };\n\n  placesInstance.setVal = (...args) => {\n    previousQuery = args[0];\n    if (previousQuery === '') {\n      pin.style.display = '';\n      clear.style.display = 'none';\n    } else {\n      clear.style.display = '';\n      pin.style.display = 'none';\n    }\n    autocompleteInstance.autocomplete.setVal(...args);\n  };\n\n  placesInstance.autocomplete = autocompleteInstance;\n\n  placesInstance.search = (query = '') =>\n    new Promise((resolve) => {\n      autocompleteDataset.source(query, resolve);\n    });\n\n  placesInstance.configure = (configuration) => {\n    const safeConfig = { ...configuration };\n\n    delete safeConfig.onHits;\n    delete safeConfig.onError;\n    delete safeConfig.onRateLimitReached;\n    delete safeConfig.onInvalidCredentials;\n    delete safeConfig.templates;\n\n    autocompleteDataset.source.configure(safeConfig);\n    return placesInstance;\n  };\n\n  placesInstance.reverse = createReverseGeocodingSource({\n    ...options,\n    algoliasearch,\n    formatInputValue: (options.templates || {}).value,\n    onHits: ({ hits, rawAnswer, query }) =>\n      placesInstance.emit('reverse', {\n        rawAnswer,\n        query,\n        suggestions: hits,\n      }),\n    onError: (e) => placesInstance.emit('error', e),\n    onRateLimitReached: () => {\n      const listeners = placesInstance.listenerCount('limit');\n      if (listeners === 0) {\n        console.log(errors.rateLimitReached); // eslint-disable-line no-console\n        return;\n      }\n\n      placesInstance.emit('limit', { message: errors.rateLimitReached });\n    },\n    onInvalidCredentials: () => {\n      if (options && options.appId && options.appId.startsWith('pl')) {\n        console.error(errors.invalidCredentials); // eslint-disable-line no-console\n      } else {\n        console.error(errors.invalidAppId); // eslint-disable-line no-console\n      }\n    },\n  });\n\n  return placesInstance;\n}\n", "module.exports = AlgoliaSearchCore;\n\nvar errors = require('./errors');\nvar exitPromise = require('./exitPromise.js');\nvar IndexCore = require('./IndexCore.js');\nvar store = require('./store.js');\n\n// We will always put the API KEY in the JSON body in case of too long API KEY,\n// to avoid query string being too long and failing in various conditions (our server limit, browser limit,\n// proxies limit)\nvar MAX_API_KEY_LENGTH = 500;\nvar RESET_APP_DATA_TIMER =\n  process.env.RESET_APP_DATA_TIMER && parseInt(process.env.RESET_APP_DATA_TIMER, 10) ||\n  60 * 2 * 1000; // after 2 minutes reset to first host\n\n/*\n * Algolia Search library initialization\n * https://www.algolia.com/\n *\n * @param {string} applicationID - Your applicationID, found in your dashboard\n * @param {string} apiKey - Your API key, found in your dashboard\n * @param {Object} [opts]\n * @param {number} [opts.timeout=2000] - The request timeout set in milliseconds,\n * another request will be issued after this timeout\n * @param {string} [opts.protocol='https:'] - The protocol used to query Algolia Search API.\n *                                        Set to 'http:' to force using http.\n * @param {Object|Array} [opts.hosts={\n *           read: [this.applicationID + '-dsn.algolia.net'].concat([\n *             this.applicationID + '-1.algolianet.com',\n *             this.applicationID + '-2.algolianet.com',\n *             this.applicationID + '-3.algolianet.com']\n *           ]),\n *           write: [this.applicationID + '.algolia.net'].concat([\n *             this.applicationID + '-1.algolianet.com',\n *             this.applicationID + '-2.algolianet.com',\n *             this.applicationID + '-3.algolianet.com']\n *           ]) - The hosts to use for Algolia Search API.\n *           If you provide them, you will less benefit from our HA implementation\n */\nfunction AlgoliaSearchCore(applicationID, apiKey, opts) {\n  var debug = require('debug')('algoliasearch');\n\n  var clone = require('./clone.js');\n  var isArray = require('isarray');\n  var map = require('./map.js');\n\n  var usage = 'Usage: algoliasearch(applicationID, apiKey, opts)';\n\n  if (opts._allowEmptyCredentials !== true && !applicationID) {\n    throw new errors.AlgoliaSearchError('Please provide an application ID. ' + usage);\n  }\n\n  if (opts._allowEmptyCredentials !== true && !apiKey) {\n    throw new errors.AlgoliaSearchError('Please provide an API key. ' + usage);\n  }\n\n  this.applicationID = applicationID;\n  this.apiKey = apiKey;\n\n  this.hosts = {\n    read: [],\n    write: []\n  };\n\n  opts = opts || {};\n\n  this._timeouts = opts.timeouts || {\n    connect: 1 * 1000, // 500ms connect is GPRS latency\n    read: 2 * 1000,\n    write: 30 * 1000\n  };\n\n  // backward compat, if opts.timeout is passed, we use it to configure all timeouts like before\n  if (opts.timeout) {\n    this._timeouts.connect = this._timeouts.read = this._timeouts.write = opts.timeout;\n  }\n\n  var protocol = opts.protocol || 'https:';\n  // while we advocate for colon-at-the-end values: 'http:' for `opts.protocol`\n  // we also accept `http` and `https`. It's a common error.\n  if (!/:$/.test(protocol)) {\n    protocol = protocol + ':';\n  }\n\n  if (protocol !== 'http:' && protocol !== 'https:') {\n    throw new errors.AlgoliaSearchError('protocol must be `http:` or `https:` (was `' + opts.protocol + '`)');\n  }\n\n  this._checkAppIdData();\n\n  if (!opts.hosts) {\n    var defaultHosts = map(this._shuffleResult, function(hostNumber) {\n      return applicationID + '-' + hostNumber + '.algolianet.com';\n    });\n\n    // no hosts given, compute defaults\n    var mainSuffix = (opts.dsn === false ? '' : '-dsn') + '.algolia.net';\n    this.hosts.read = [this.applicationID + mainSuffix].concat(defaultHosts);\n    this.hosts.write = [this.applicationID + '.algolia.net'].concat(defaultHosts);\n  } else if (isArray(opts.hosts)) {\n    // when passing custom hosts, we need to have a different host index if the number\n    // of write/read hosts are different.\n    this.hosts.read = clone(opts.hosts);\n    this.hosts.write = clone(opts.hosts);\n  } else {\n    this.hosts.read = clone(opts.hosts.read);\n    this.hosts.write = clone(opts.hosts.write);\n  }\n\n  // add protocol and lowercase hosts\n  this.hosts.read = map(this.hosts.read, prepareHost(protocol));\n  this.hosts.write = map(this.hosts.write, prepareHost(protocol));\n\n  this.extraHeaders = {};\n\n  // In some situations you might want to warm the cache\n  this.cache = opts._cache || {};\n\n  this._ua = opts._ua;\n  this._useCache = opts._useCache === undefined || opts._cache ? true : opts._useCache;\n  this._useRequestCache = this._useCache && opts._useRequestCache;\n  this._useFallback = opts.useFallback === undefined ? true : opts.useFallback;\n\n  this._setTimeout = opts._setTimeout;\n\n  debug('init done, %j', this);\n}\n\n/*\n * Get the index object initialized\n *\n * @param indexName the name of index\n * @param callback the result callback with one argument (the Index instance)\n */\nAlgoliaSearchCore.prototype.initIndex = function(indexName) {\n  return new IndexCore(this, indexName);\n};\n\n/**\n* Add an extra field to the HTTP request\n*\n* @param name the header field name\n* @param value the header field value\n*/\nAlgoliaSearchCore.prototype.setExtraHeader = function(name, value) {\n  this.extraHeaders[name.toLowerCase()] = value;\n};\n\n/**\n* Get the value of an extra HTTP header\n*\n* @param name the header field name\n*/\nAlgoliaSearchCore.prototype.getExtraHeader = function(name) {\n  return this.extraHeaders[name.toLowerCase()];\n};\n\n/**\n* Remove an extra field from the HTTP request\n*\n* @param name the header field name\n*/\nAlgoliaSearchCore.prototype.unsetExtraHeader = function(name) {\n  delete this.extraHeaders[name.toLowerCase()];\n};\n\n/**\n* Augment sent x-algolia-agent with more data, each agent part\n* is automatically separated from the others by a semicolon;\n*\n* @param algoliaAgent the agent to add\n*/\nAlgoliaSearchCore.prototype.addAlgoliaAgent = function(algoliaAgent) {\n  var algoliaAgentWithDelimiter = '; ' + algoliaAgent;\n\n  if (this._ua.indexOf(algoliaAgentWithDelimiter) === -1) {\n    this._ua += algoliaAgentWithDelimiter;\n  }\n};\n\n/*\n * Wrapper that try all hosts to maximize the quality of service\n */\nAlgoliaSearchCore.prototype._jsonRequest = function(initialOpts) {\n  this._checkAppIdData();\n\n  var requestDebug = require('debug')('algoliasearch:' + initialOpts.url);\n\n\n  var body;\n  var cacheID;\n  var additionalUA = initialOpts.additionalUA || '';\n  var cache = initialOpts.cache;\n  var client = this;\n  var tries = 0;\n  var usingFallback = false;\n  var hasFallback = client._useFallback && client._request.fallback && initialOpts.fallback;\n  var headers;\n\n  if (\n    this.apiKey.length > MAX_API_KEY_LENGTH &&\n    initialOpts.body !== undefined &&\n    (initialOpts.body.params !== undefined || // index.search()\n    initialOpts.body.requests !== undefined) // client.search()\n  ) {\n    initialOpts.body.apiKey = this.apiKey;\n    headers = this._computeRequestHeaders({\n      additionalUA: additionalUA,\n      withApiKey: false,\n      headers: initialOpts.headers\n    });\n  } else {\n    headers = this._computeRequestHeaders({\n      additionalUA: additionalUA,\n      headers: initialOpts.headers\n    });\n  }\n\n  if (initialOpts.body !== undefined) {\n    body = safeJSONStringify(initialOpts.body);\n  }\n\n  requestDebug('request start');\n  var debugData = [];\n\n\n  function doRequest(requester, reqOpts) {\n    client._checkAppIdData();\n\n    var startTime = new Date();\n\n    if (client._useCache && !client._useRequestCache) {\n      cacheID = initialOpts.url;\n    }\n\n    // as we sometime use POST requests to pass parameters (like query='aa'),\n    // the cacheID must also include the body to be different between calls\n    if (client._useCache && !client._useRequestCache && body) {\n      cacheID += '_body_' + reqOpts.body;\n    }\n\n    // handle cache existence\n    if (isCacheValidWithCurrentID(!client._useRequestCache, cache, cacheID)) {\n      requestDebug('serving response from cache');\n\n      var responseText = cache[cacheID];\n\n      // Cache response must match the type of the original one\n      return client._promise.resolve({\n        body: JSON.parse(responseText),\n        responseText: responseText\n      });\n    }\n\n    // if we reached max tries\n    if (tries >= client.hosts[initialOpts.hostType].length) {\n      if (!hasFallback || usingFallback) {\n        requestDebug('could not get any response');\n        // then stop\n        return client._promise.reject(new errors.AlgoliaSearchError(\n          'Cannot connect to the AlgoliaSearch API.' +\n          ' Send an <NAME_EMAIL> to report and resolve the issue.' +\n          ' Application id was: ' + client.applicationID, {debugData: debugData}\n        ));\n      }\n\n      requestDebug('switching to fallback');\n\n      // let's try the fallback starting from here\n      tries = 0;\n\n      // method, url and body are fallback dependent\n      reqOpts.method = initialOpts.fallback.method;\n      reqOpts.url = initialOpts.fallback.url;\n      reqOpts.jsonBody = initialOpts.fallback.body;\n      if (reqOpts.jsonBody) {\n        reqOpts.body = safeJSONStringify(reqOpts.jsonBody);\n      }\n      // re-compute headers, they could be omitting the API KEY\n      headers = client._computeRequestHeaders({\n        additionalUA: additionalUA,\n        headers: initialOpts.headers\n      });\n\n      reqOpts.timeouts = client._getTimeoutsForRequest(initialOpts.hostType);\n      client._setHostIndexByType(0, initialOpts.hostType);\n      usingFallback = true; // the current request is now using fallback\n      return doRequest(client._request.fallback, reqOpts);\n    }\n\n    var currentHost = client._getHostByType(initialOpts.hostType);\n\n    var url = currentHost + reqOpts.url;\n    var options = {\n      body: reqOpts.body,\n      jsonBody: reqOpts.jsonBody,\n      method: reqOpts.method,\n      headers: headers,\n      timeouts: reqOpts.timeouts,\n      debug: requestDebug,\n      forceAuthHeaders: reqOpts.forceAuthHeaders\n    };\n\n    requestDebug('method: %s, url: %s, headers: %j, timeouts: %d',\n      options.method, url, options.headers, options.timeouts);\n\n    if (requester === client._request.fallback) {\n      requestDebug('using fallback');\n    }\n\n    // `requester` is any of this._request or this._request.fallback\n    // thus it needs to be called using the client as context\n    return requester.call(client, url, options).then(success, tryFallback);\n\n    function success(httpResponse) {\n      // compute the status of the response,\n      //\n      // When in browser mode, using XDR or JSONP, we have no statusCode available\n      // So we rely on our API response `status` property.\n      // But `waitTask` can set a `status` property which is not the statusCode (it's the task status)\n      // So we check if there's a `message` along `status` and it means it's an error\n      //\n      // That's the only case where we have a response.status that's not the http statusCode\n      var status = httpResponse && httpResponse.body && httpResponse.body.message && httpResponse.body.status ||\n\n        // this is important to check the request statusCode AFTER the body eventual\n        // statusCode because some implementations (jQuery XDomainRequest transport) may\n        // send statusCode 200 while we had an error\n        httpResponse.statusCode ||\n\n        // When in browser mode, using XDR or JSONP\n        // we default to success when no error (no response.status && response.message)\n        // If there was a JSON.parse() error then body is null and it fails\n        httpResponse && httpResponse.body && 200;\n\n      requestDebug('received response: statusCode: %s, computed statusCode: %d, headers: %j',\n        httpResponse.statusCode, status, httpResponse.headers);\n\n      var httpResponseOk = Math.floor(status / 100) === 2;\n\n      var endTime = new Date();\n      debugData.push({\n        currentHost: currentHost,\n        headers: removeCredentials(headers),\n        content: body || null,\n        contentLength: body !== undefined ? body.length : null,\n        method: reqOpts.method,\n        timeouts: reqOpts.timeouts,\n        url: reqOpts.url,\n        startTime: startTime,\n        endTime: endTime,\n        duration: endTime - startTime,\n        statusCode: status\n      });\n\n      if (httpResponseOk) {\n        if (client._useCache && !client._useRequestCache && cache) {\n          cache[cacheID] = httpResponse.responseText;\n        }\n\n        return {\n          responseText: httpResponse.responseText,\n          body: httpResponse.body\n        };\n      }\n\n      var shouldRetry = Math.floor(status / 100) !== 4;\n\n      if (shouldRetry) {\n        tries += 1;\n        return retryRequest();\n      }\n\n      requestDebug('unrecoverable error');\n\n      // no success and no retry => fail\n      var unrecoverableError = new errors.AlgoliaSearchError(\n        httpResponse.body && httpResponse.body.message, {debugData: debugData, statusCode: status}\n      );\n\n      return client._promise.reject(unrecoverableError);\n    }\n\n    function tryFallback(err) {\n      // error cases:\n      //  While not in fallback mode:\n      //    - CORS not supported\n      //    - network error\n      //  While in fallback mode:\n      //    - timeout\n      //    - network error\n      //    - badly formatted JSONP (script loaded, did not call our callback)\n      //  In both cases:\n      //    - uncaught exception occurs (TypeError)\n      requestDebug('error: %s, stack: %s', err.message, err.stack);\n\n      var endTime = new Date();\n      debugData.push({\n        currentHost: currentHost,\n        headers: removeCredentials(headers),\n        content: body || null,\n        contentLength: body !== undefined ? body.length : null,\n        method: reqOpts.method,\n        timeouts: reqOpts.timeouts,\n        url: reqOpts.url,\n        startTime: startTime,\n        endTime: endTime,\n        duration: endTime - startTime\n      });\n\n      if (!(err instanceof errors.AlgoliaSearchError)) {\n        err = new errors.Unknown(err && err.message, err);\n      }\n\n      tries += 1;\n\n      // stop the request implementation when:\n      if (\n        // we did not generate this error,\n        // it comes from a throw in some other piece of code\n        err instanceof errors.Unknown ||\n\n        // server sent unparsable JSON\n        err instanceof errors.UnparsableJSON ||\n\n        // max tries and already using fallback or no fallback\n        tries >= client.hosts[initialOpts.hostType].length &&\n        (usingFallback || !hasFallback)) {\n        // stop request implementation for this command\n        err.debugData = debugData;\n        return client._promise.reject(err);\n      }\n\n      // When a timeout occurred, retry by raising timeout\n      if (err instanceof errors.RequestTimeout) {\n        return retryRequestWithHigherTimeout();\n      }\n\n      return retryRequest();\n    }\n\n    function retryRequest() {\n      requestDebug('retrying request');\n      client._incrementHostIndex(initialOpts.hostType);\n      return doRequest(requester, reqOpts);\n    }\n\n    function retryRequestWithHigherTimeout() {\n      requestDebug('retrying request with higher timeout');\n      client._incrementHostIndex(initialOpts.hostType);\n      client._incrementTimeoutMultipler();\n      reqOpts.timeouts = client._getTimeoutsForRequest(initialOpts.hostType);\n      return doRequest(requester, reqOpts);\n    }\n  }\n\n  function isCacheValidWithCurrentID(\n    useRequestCache,\n    currentCache,\n    currentCacheID\n  ) {\n    return (\n      client._useCache &&\n      useRequestCache &&\n      currentCache &&\n      currentCache[currentCacheID] !== undefined\n    );\n  }\n\n\n  function interopCallbackReturn(request, callback) {\n    if (isCacheValidWithCurrentID(client._useRequestCache, cache, cacheID)) {\n      request.catch(function() {\n        // Release the cache on error\n        delete cache[cacheID];\n      });\n    }\n\n    if (typeof initialOpts.callback === 'function') {\n      // either we have a callback\n      request.then(function okCb(content) {\n        exitPromise(function() {\n          initialOpts.callback(null, callback(content));\n        }, client._setTimeout || setTimeout);\n      }, function nookCb(err) {\n        exitPromise(function() {\n          initialOpts.callback(err);\n        }, client._setTimeout || setTimeout);\n      });\n    } else {\n      // either we are using promises\n      return request.then(callback);\n    }\n  }\n\n  if (client._useCache && client._useRequestCache) {\n    cacheID = initialOpts.url;\n  }\n\n  // as we sometime use POST requests to pass parameters (like query='aa'),\n  // the cacheID must also include the body to be different between calls\n  if (client._useCache && client._useRequestCache && body) {\n    cacheID += '_body_' + body;\n  }\n\n  if (isCacheValidWithCurrentID(client._useRequestCache, cache, cacheID)) {\n    requestDebug('serving request from cache');\n\n    var maybePromiseForCache = cache[cacheID];\n\n    // In case the cache is warmup with value that is not a promise\n    var promiseForCache = typeof maybePromiseForCache.then !== 'function'\n      ? client._promise.resolve({responseText: maybePromiseForCache})\n      : maybePromiseForCache;\n\n    return interopCallbackReturn(promiseForCache, function(content) {\n      // In case of the cache request, return the original value\n      return JSON.parse(content.responseText);\n    });\n  }\n\n  var request = doRequest(\n    client._request, {\n      url: initialOpts.url,\n      method: initialOpts.method,\n      body: body,\n      jsonBody: initialOpts.body,\n      timeouts: client._getTimeoutsForRequest(initialOpts.hostType),\n      forceAuthHeaders: initialOpts.forceAuthHeaders\n    }\n  );\n\n  if (client._useCache && client._useRequestCache && cache) {\n    cache[cacheID] = request;\n  }\n\n  return interopCallbackReturn(request, function(content) {\n    // In case of the first request, return the JSON value\n    return content.body;\n  });\n};\n\n/*\n* Transform search param object in query string\n* @param {object} args arguments to add to the current query string\n* @param {string} params current query string\n* @return {string} the final query string\n*/\nAlgoliaSearchCore.prototype._getSearchParams = function(args, params) {\n  if (args === undefined || args === null) {\n    return params;\n  }\n  for (var key in args) {\n    if (key !== null && args[key] !== undefined && args.hasOwnProperty(key)) {\n      params += params === '' ? '' : '&';\n      params += key + '=' + encodeURIComponent(Object.prototype.toString.call(args[key]) === '[object Array]' ? safeJSONStringify(args[key]) : args[key]);\n    }\n  }\n  return params;\n};\n\n/**\n * Compute the headers for a request\n *\n * @param [string] options.additionalUA semi-colon separated string with other user agents to add\n * @param [boolean=true] options.withApiKey Send the api key as a header\n * @param [Object] options.headers Extra headers to send\n */\nAlgoliaSearchCore.prototype._computeRequestHeaders = function(options) {\n  var forEach = require('foreach');\n\n  var ua = options.additionalUA ?\n    this._ua + '; ' + options.additionalUA :\n    this._ua;\n\n  var requestHeaders = {\n    'x-algolia-agent': ua,\n    'x-algolia-application-id': this.applicationID\n  };\n\n  // browser will inline headers in the url, node.js will use http headers\n  // but in some situations, the API KEY will be too long (big secured API keys)\n  // so if the request is a POST and the KEY is very long, we will be asked to not put\n  // it into headers but in the JSON body\n  if (options.withApiKey !== false) {\n    requestHeaders['x-algolia-api-key'] = this.apiKey;\n  }\n\n  if (this.userToken) {\n    requestHeaders['x-algolia-usertoken'] = this.userToken;\n  }\n\n  if (this.securityTags) {\n    requestHeaders['x-algolia-tagfilters'] = this.securityTags;\n  }\n\n  forEach(this.extraHeaders, function addToRequestHeaders(value, key) {\n    requestHeaders[key] = value;\n  });\n\n  if (options.headers) {\n    forEach(options.headers, function addToRequestHeaders(value, key) {\n      requestHeaders[key] = value;\n    });\n  }\n\n  return requestHeaders;\n};\n\n/**\n * Search through multiple indices at the same time\n * @param  {Object[]}   queries  An array of queries you want to run.\n * @param {string} queries[].indexName The index name you want to target\n * @param {string} [queries[].query] The query to issue on this index. Can also be passed into `params`\n * @param {Object} queries[].params Any search param like hitsPerPage, ..\n * @param  {Function} callback Callback to be called\n * @return {Promise|undefined} Returns a promise if no callback given\n */\nAlgoliaSearchCore.prototype.search = function(queries, opts, callback) {\n  var isArray = require('isarray');\n  var map = require('./map.js');\n\n  var usage = 'Usage: client.search(arrayOfQueries[, callback])';\n\n  if (!isArray(queries)) {\n    throw new Error(usage);\n  }\n\n  if (typeof opts === 'function') {\n    callback = opts;\n    opts = {};\n  } else if (opts === undefined) {\n    opts = {};\n  }\n\n  var client = this;\n\n  var postObj = {\n    requests: map(queries, function prepareRequest(query) {\n      var params = '';\n\n      // allow query.query\n      // so we are mimicing the index.search(query, params) method\n      // {indexName:, query:, params:}\n      if (query.query !== undefined) {\n        params += 'query=' + encodeURIComponent(query.query);\n      }\n\n      return {\n        indexName: query.indexName,\n        params: client._getSearchParams(query.params, params)\n      };\n    })\n  };\n\n  var JSONPParams = map(postObj.requests, function prepareJSONPParams(request, requestId) {\n    return requestId + '=' +\n      encodeURIComponent(\n        '/1/indexes/' + encodeURIComponent(request.indexName) + '?' +\n        request.params\n      );\n  }).join('&');\n\n  var url = '/1/indexes/*/queries';\n\n  if (opts.strategy !== undefined) {\n    postObj.strategy = opts.strategy;\n  }\n\n  return this._jsonRequest({\n    cache: this.cache,\n    method: 'POST',\n    url: url,\n    body: postObj,\n    hostType: 'read',\n    fallback: {\n      method: 'GET',\n      url: '/1/indexes/*',\n      body: {\n        params: JSONPParams\n      }\n    },\n    callback: callback\n  });\n};\n\n/**\n* Search for facet values\n* https://www.algolia.com/doc/rest-api/search#search-for-facet-values\n* This is the top-level API for SFFV.\n*\n* @param {object[]} queries An array of queries to run.\n* @param {string} queries[].indexName Index name, name of the index to search.\n* @param {object} queries[].params Query parameters.\n* @param {string} queries[].params.facetName Facet name, name of the attribute to search for values in.\n* Must be declared as a facet\n* @param {string} queries[].params.facetQuery Query for the facet search\n* @param {string} [queries[].params.*] Any search parameter of Algolia,\n* see https://www.algolia.com/doc/api-client/javascript/search#search-parameters\n* Pagination is not supported. The page and hitsPerPage parameters will be ignored.\n*/\nAlgoliaSearchCore.prototype.searchForFacetValues = function(queries) {\n  var isArray = require('isarray');\n  var map = require('./map.js');\n\n  var usage = 'Usage: client.searchForFacetValues([{indexName, params: {facetName, facetQuery, ...params}}, ...queries])'; // eslint-disable-line max-len\n\n  if (!isArray(queries)) {\n    throw new Error(usage);\n  }\n\n  var client = this;\n\n  return client._promise.all(map(queries, function performQuery(query) {\n    if (\n      !query ||\n      query.indexName === undefined ||\n      query.params.facetName === undefined ||\n      query.params.facetQuery === undefined\n    ) {\n      throw new Error(usage);\n    }\n\n    var clone = require('./clone.js');\n    var omit = require('./omit.js');\n\n    var indexName = query.indexName;\n    var params = query.params;\n\n    var facetName = params.facetName;\n    var filteredParams = omit(clone(params), function(keyName) {\n      return keyName === 'facetName';\n    });\n    var searchParameters = client._getSearchParams(filteredParams, '');\n\n    return client._jsonRequest({\n      cache: client.cache,\n      method: 'POST',\n      url:\n        '/1/indexes/' +\n        encodeURIComponent(indexName) +\n        '/facets/' +\n        encodeURIComponent(facetName) +\n        '/query',\n      hostType: 'read',\n      body: {params: searchParameters}\n    });\n  }));\n};\n\n/**\n * Set the extra security tagFilters header\n * @param {string|array} tags The list of tags defining the current security filters\n */\nAlgoliaSearchCore.prototype.setSecurityTags = function(tags) {\n  if (Object.prototype.toString.call(tags) === '[object Array]') {\n    var strTags = [];\n    for (var i = 0; i < tags.length; ++i) {\n      if (Object.prototype.toString.call(tags[i]) === '[object Array]') {\n        var oredTags = [];\n        for (var j = 0; j < tags[i].length; ++j) {\n          oredTags.push(tags[i][j]);\n        }\n        strTags.push('(' + oredTags.join(',') + ')');\n      } else {\n        strTags.push(tags[i]);\n      }\n    }\n    tags = strTags.join(',');\n  }\n\n  this.securityTags = tags;\n};\n\n/**\n * Set the extra user token header\n * @param {string} userToken The token identifying a uniq user (used to apply rate limits)\n */\nAlgoliaSearchCore.prototype.setUserToken = function(userToken) {\n  this.userToken = userToken;\n};\n\n/**\n * Clear all queries in client's cache\n * @return undefined\n */\nAlgoliaSearchCore.prototype.clearCache = function() {\n  this.cache = {};\n};\n\n/**\n* Set the number of milliseconds a request can take before automatically being terminated.\n* @deprecated\n* @param {Number} milliseconds\n*/\nAlgoliaSearchCore.prototype.setRequestTimeout = function(milliseconds) {\n  if (milliseconds) {\n    this._timeouts.connect = this._timeouts.read = this._timeouts.write = milliseconds;\n  }\n};\n\n/**\n* Set the three different (connect, read, write) timeouts to be used when requesting\n* @param {Object} timeouts\n*/\nAlgoliaSearchCore.prototype.setTimeouts = function(timeouts) {\n  this._timeouts = timeouts;\n};\n\n/**\n* Get the three different (connect, read, write) timeouts to be used when requesting\n* @param {Object} timeouts\n*/\nAlgoliaSearchCore.prototype.getTimeouts = function() {\n  return this._timeouts;\n};\n\nAlgoliaSearchCore.prototype._getAppIdData = function() {\n  var data = store.get(this.applicationID);\n  if (data !== null) this._cacheAppIdData(data);\n  return data;\n};\n\nAlgoliaSearchCore.prototype._setAppIdData = function(data) {\n  data.lastChange = (new Date()).getTime();\n  this._cacheAppIdData(data);\n  return store.set(this.applicationID, data);\n};\n\nAlgoliaSearchCore.prototype._checkAppIdData = function() {\n  var data = this._getAppIdData();\n  var now = (new Date()).getTime();\n  if (data === null || now - data.lastChange > RESET_APP_DATA_TIMER) {\n    return this._resetInitialAppIdData(data);\n  }\n\n  return data;\n};\n\nAlgoliaSearchCore.prototype._resetInitialAppIdData = function(data) {\n  var newData = data || {};\n  newData.hostIndexes = {read: 0, write: 0};\n  newData.timeoutMultiplier = 1;\n  newData.shuffleResult = newData.shuffleResult || shuffle([1, 2, 3]);\n  return this._setAppIdData(newData);\n};\n\nAlgoliaSearchCore.prototype._cacheAppIdData = function(data) {\n  this._hostIndexes = data.hostIndexes;\n  this._timeoutMultiplier = data.timeoutMultiplier;\n  this._shuffleResult = data.shuffleResult;\n};\n\nAlgoliaSearchCore.prototype._partialAppIdDataUpdate = function(newData) {\n  var foreach = require('foreach');\n  var currentData = this._getAppIdData();\n  foreach(newData, function(value, key) {\n    currentData[key] = value;\n  });\n\n  return this._setAppIdData(currentData);\n};\n\nAlgoliaSearchCore.prototype._getHostByType = function(hostType) {\n  return this.hosts[hostType][this._getHostIndexByType(hostType)];\n};\n\nAlgoliaSearchCore.prototype._getTimeoutMultiplier = function() {\n  return this._timeoutMultiplier;\n};\n\nAlgoliaSearchCore.prototype._getHostIndexByType = function(hostType) {\n  return this._hostIndexes[hostType];\n};\n\nAlgoliaSearchCore.prototype._setHostIndexByType = function(hostIndex, hostType) {\n  var clone = require('./clone');\n  var newHostIndexes = clone(this._hostIndexes);\n  newHostIndexes[hostType] = hostIndex;\n  this._partialAppIdDataUpdate({hostIndexes: newHostIndexes});\n  return hostIndex;\n};\n\nAlgoliaSearchCore.prototype._incrementHostIndex = function(hostType) {\n  return this._setHostIndexByType(\n    (this._getHostIndexByType(hostType) + 1) % this.hosts[hostType].length, hostType\n  );\n};\n\nAlgoliaSearchCore.prototype._incrementTimeoutMultipler = function() {\n  var timeoutMultiplier = Math.max(this._timeoutMultiplier + 1, 4);\n  return this._partialAppIdDataUpdate({timeoutMultiplier: timeoutMultiplier});\n};\n\nAlgoliaSearchCore.prototype._getTimeoutsForRequest = function(hostType) {\n  return {\n    connect: this._timeouts.connect * this._timeoutMultiplier,\n    complete: this._timeouts[hostType] * this._timeoutMultiplier\n  };\n};\n\nfunction prepareHost(protocol) {\n  return function prepare(host) {\n    return protocol + '//' + host.toLowerCase();\n  };\n}\n\n// Prototype.js < 1.7, a widely used library, defines a weird\n// Array.prototype.toJSON function that will fail to stringify our content\n// appropriately\n// refs:\n//   - https://groups.google.com/forum/#!topic/prototype-core/E-SAVvV_V9Q\n//   - https://github.com/sstephenson/prototype/commit/038a2985a70593c1a86c230fadbdfe2e4898a48c\n//   - http://stackoverflow.com/a/3148441/147079\nfunction safeJSONStringify(obj) {\n  /* eslint no-extend-native:0 */\n\n  if (Array.prototype.toJSON === undefined) {\n    return JSON.stringify(obj);\n  }\n\n  var toJSON = Array.prototype.toJSON;\n  delete Array.prototype.toJSON;\n  var out = JSON.stringify(obj);\n  Array.prototype.toJSON = toJSON;\n\n  return out;\n}\n\nfunction shuffle(array) {\n  var currentIndex = array.length;\n  var temporaryValue;\n  var randomIndex;\n\n  // While there remain elements to shuffle...\n  while (currentIndex !== 0) {\n    // Pick a remaining element...\n    randomIndex = Math.floor(Math.random() * currentIndex);\n    currentIndex -= 1;\n\n    // And swap it with the current element.\n    temporaryValue = array[currentIndex];\n    array[currentIndex] = array[randomIndex];\n    array[randomIndex] = temporaryValue;\n  }\n\n  return array;\n}\n\nfunction removeCredentials(headers) {\n  var newHeaders = {};\n\n  for (var headerName in headers) {\n    if (Object.prototype.hasOwnProperty.call(headers, headerName)) {\n      var value;\n\n      if (headerName === 'x-algolia-api-key' || headerName === 'x-algolia-application-id') {\n        value = '**hidden for security purposes**';\n      } else {\n        value = headers[headerName];\n      }\n\n      newHeaders[headerName] = value;\n    }\n  }\n\n  return newHeaders;\n}\n", "// Parse cloud does not supports setTimeout\n// We do not store a setTimeout reference in the client everytime\n// We only fallback to a fake setTimeout when not available\n// setTimeout cannot be override globally sadly\nmodule.exports = function exitPromise(fn, _setTimeout) {\n  _setTimeout(fn, 0);\n};\n", "var buildSearchMethod = require('./buildSearchMethod.js');\nvar deprecate = require('./deprecate.js');\nvar deprecatedMessage = require('./deprecatedMessage.js');\n\nmodule.exports = IndexCore;\n\n/*\n* Index class constructor.\n* You should not use this method directly but use initIndex() function\n*/\nfunction IndexCore(algoliasearch, indexName) {\n  this.indexName = indexName;\n  this.as = algoliasearch;\n  this.typeAheadArgs = null;\n  this.typeAheadValueOption = null;\n\n  // make sure every index instance has it's own cache\n  this.cache = {};\n}\n\n/*\n* Clear all queries in cache\n*/\nIndexCore.prototype.clearCache = function() {\n  this.cache = {};\n};\n\n/*\n* Search inside the index using XMLHttpRequest request (Using a POST query to\n* minimize number of OPTIONS queries: Cross-Origin Resource Sharing).\n*\n* @param {string} [query] the full text query\n* @param {object} [args] (optional) if set, contains an object with query parameters:\n* - page: (integer) Pagination parameter used to select the page to retrieve.\n*                   Page is zero-based and defaults to 0. Thus,\n*                   to retrieve the 10th page you need to set page=9\n* - hitsPerPage: (integer) Pagination parameter used to select the number of hits per page. Defaults to 20.\n* - attributesToRetrieve: a string that contains the list of object attributes\n* you want to retrieve (let you minimize the answer size).\n*   Attributes are separated with a comma (for example \"name,address\").\n*   You can also use an array (for example [\"name\",\"address\"]).\n*   By default, all attributes are retrieved. You can also use '*' to retrieve all\n*   values when an attributesToRetrieve setting is specified for your index.\n* - attributesToHighlight: a string that contains the list of attributes you\n*   want to highlight according to the query.\n*   Attributes are separated by a comma. You can also use an array (for example [\"name\",\"address\"]).\n*   If an attribute has no match for the query, the raw value is returned.\n*   By default all indexed text attributes are highlighted.\n*   You can use `*` if you want to highlight all textual attributes.\n*   Numerical attributes are not highlighted.\n*   A matchLevel is returned for each highlighted attribute and can contain:\n*      - full: if all the query terms were found in the attribute,\n*      - partial: if only some of the query terms were found,\n*      - none: if none of the query terms were found.\n* - attributesToSnippet: a string that contains the list of attributes to snippet alongside\n* the number of words to return (syntax is `attributeName:nbWords`).\n*    Attributes are separated by a comma (Example: attributesToSnippet=name:10,content:10).\n*    You can also use an array (Example: attributesToSnippet: ['name:10','content:10']).\n*    By default no snippet is computed.\n* - minWordSizefor1Typo: the minimum number of characters in a query word to accept one typo in this word.\n* Defaults to 3.\n* - minWordSizefor2Typos: the minimum number of characters in a query word\n* to accept two typos in this word. Defaults to 7.\n* - getRankingInfo: if set to 1, the result hits will contain ranking\n* information in _rankingInfo attribute.\n* - aroundLatLng: search for entries around a given\n* latitude/longitude (specified as two floats separated by a comma).\n*   For example aroundLatLng=47.316669,5.016670).\n*   You can specify the maximum distance in meters with the aroundRadius parameter (in meters)\n*   and the precision for ranking with aroundPrecision\n*   (for example if you set aroundPrecision=100, two objects that are distant of\n*   less than 100m will be considered as identical for \"geo\" ranking parameter).\n*   At indexing, you should specify geoloc of an object with the _geoloc attribute\n*   (in the form {\"_geoloc\":{\"lat\":48.853409, \"lng\":2.348800}})\n* - insideBoundingBox: search entries inside a given area defined by the two extreme points\n* of a rectangle (defined by 4 floats: p1Lat,p1Lng,p2Lat,p2Lng).\n*   For example insideBoundingBox=47.3165,4.9665,47.3424,5.0201).\n*   At indexing, you should specify geoloc of an object with the _geoloc attribute\n*   (in the form {\"_geoloc\":{\"lat\":48.853409, \"lng\":2.348800}})\n* - numericFilters: a string that contains the list of numeric filters you want to\n* apply separated by a comma.\n*   The syntax of one filter is `attributeName` followed by `operand` followed by `value`.\n*   Supported operands are `<`, `<=`, `=`, `>` and `>=`.\n*   You can have multiple conditions on one attribute like for example numericFilters=price>100,price<1000.\n*   You can also use an array (for example numericFilters: [\"price>100\",\"price<1000\"]).\n* - tagFilters: filter the query by a set of tags. You can AND tags by separating them by commas.\n*   To OR tags, you must add parentheses. For example, tags=tag1,(tag2,tag3) means tag1 AND (tag2 OR tag3).\n*   You can also use an array, for example tagFilters: [\"tag1\",[\"tag2\",\"tag3\"]]\n*   means tag1 AND (tag2 OR tag3).\n*   At indexing, tags should be added in the _tags** attribute\n*   of objects (for example {\"_tags\":[\"tag1\",\"tag2\"]}).\n* - facetFilters: filter the query by a list of facets.\n*   Facets are separated by commas and each facet is encoded as `attributeName:value`.\n*   For example: `facetFilters=category:Book,author:John%20Doe`.\n*   You can also use an array (for example `[\"category:Book\",\"author:John%20Doe\"]`).\n* - facets: List of object attributes that you want to use for faceting.\n*   Comma separated list: `\"category,author\"` or array `['category','author']`\n*   Only attributes that have been added in **attributesForFaceting** index setting\n*   can be used in this parameter.\n*   You can also use `*` to perform faceting on all attributes specified in **attributesForFaceting**.\n* - queryType: select how the query words are interpreted, it can be one of the following value:\n*    - prefixAll: all query words are interpreted as prefixes,\n*    - prefixLast: only the last word is interpreted as a prefix (default behavior),\n*    - prefixNone: no query word is interpreted as a prefix. This option is not recommended.\n* - optionalWords: a string that contains the list of words that should\n* be considered as optional when found in the query.\n*   Comma separated and array are accepted.\n* - distinct: If set to 1, enable the distinct feature (disabled by default)\n* if the attributeForDistinct index setting is set.\n*   This feature is similar to the SQL \"distinct\" keyword: when enabled\n*   in a query with the distinct=1 parameter,\n*   all hits containing a duplicate value for the attributeForDistinct attribute are removed from results.\n*   For example, if the chosen attribute is show_name and several hits have\n*   the same value for show_name, then only the best\n*   one is kept and others are removed.\n* - restrictSearchableAttributes: List of attributes you want to use for\n* textual search (must be a subset of the attributesToIndex index setting)\n* either comma separated or as an array\n* @param {function} [callback] the result callback called with two arguments:\n*  error: null or Error('message'). If false, the content contains the error.\n*  content: the server answer that contains the list of results.\n*/\nIndexCore.prototype.search = buildSearchMethod('query');\n\n/*\n* -- BETA --\n* Search a record similar to the query inside the index using XMLHttpRequest request (Using a POST query to\n* minimize number of OPTIONS queries: Cross-Origin Resource Sharing).\n*\n* @param {string} [query] the similar query\n* @param {object} [args] (optional) if set, contains an object with query parameters.\n*   All search parameters are supported (see search function), restrictSearchableAttributes and facetFilters\n*   are the two most useful to restrict the similar results and get more relevant content\n*/\nIndexCore.prototype.similarSearch = deprecate(\n  buildSearchMethod('similarQuery'),\n  deprecatedMessage(\n    'index.similarSearch(query[, callback])',\n    'index.search({ similarQuery: query }[, callback])'\n  )\n);\n\n/*\n* Browse index content. The response content will have a `cursor` property that you can use\n* to browse subsequent pages for this query. Use `index.browseFrom(cursor)` when you want.\n*\n* @param {string} query - The full text query\n* @param {Object} [queryParameters] - Any search query parameter\n* @param {Function} [callback] - The result callback called with two arguments\n*   error: null or Error('message')\n*   content: the server answer with the browse result\n* @return {Promise|undefined} Returns a promise if no callback given\n* @example\n* index.browse('cool songs', {\n*   tagFilters: 'public,comments',\n*   hitsPerPage: 500\n* }, callback);\n* @see {@link https://www.algolia.com/doc/rest_api#Browse|Algolia REST API Documentation}\n*/\nIndexCore.prototype.browse = function(query, queryParameters, callback) {\n  var merge = require('./merge.js');\n\n  var indexObj = this;\n\n  var page;\n  var hitsPerPage;\n\n  // we check variadic calls that are not the one defined\n  // .browse()/.browse(fn)\n  // => page = 0\n  if (arguments.length === 0 || arguments.length === 1 && typeof arguments[0] === 'function') {\n    page = 0;\n    callback = arguments[0];\n    query = undefined;\n  } else if (typeof arguments[0] === 'number') {\n    // .browse(2)/.browse(2, 10)/.browse(2, fn)/.browse(2, 10, fn)\n    page = arguments[0];\n    if (typeof arguments[1] === 'number') {\n      hitsPerPage = arguments[1];\n    } else if (typeof arguments[1] === 'function') {\n      callback = arguments[1];\n      hitsPerPage = undefined;\n    }\n    query = undefined;\n    queryParameters = undefined;\n  } else if (typeof arguments[0] === 'object') {\n    // .browse(queryParameters)/.browse(queryParameters, cb)\n    if (typeof arguments[1] === 'function') {\n      callback = arguments[1];\n    }\n    queryParameters = arguments[0];\n    query = undefined;\n  } else if (typeof arguments[0] === 'string' && typeof arguments[1] === 'function') {\n    // .browse(query, cb)\n    callback = arguments[1];\n    queryParameters = undefined;\n  }\n\n  // otherwise it's a .browse(query)/.browse(query, queryParameters)/.browse(query, queryParameters, cb)\n\n  // get search query parameters combining various possible calls\n  // to .browse();\n  queryParameters = merge({}, queryParameters || {}, {\n    page: page,\n    hitsPerPage: hitsPerPage,\n    query: query\n  });\n\n  var params = this.as._getSearchParams(queryParameters, '');\n\n  return this.as._jsonRequest({\n    method: 'POST',\n    url: '/1/indexes/' + encodeURIComponent(indexObj.indexName) + '/browse',\n    body: {params: params},\n    hostType: 'read',\n    callback: callback\n  });\n};\n\n/*\n* Continue browsing from a previous position (cursor), obtained via a call to `.browse()`.\n*\n* @param {string} query - The full text query\n* @param {Object} [queryParameters] - Any search query parameter\n* @param {Function} [callback] - The result callback called with two arguments\n*   error: null or Error('message')\n*   content: the server answer with the browse result\n* @return {Promise|undefined} Returns a promise if no callback given\n* @example\n* index.browseFrom('14lkfsakl32', callback);\n* @see {@link https://www.algolia.com/doc/rest_api#Browse|Algolia REST API Documentation}\n*/\nIndexCore.prototype.browseFrom = function(cursor, callback) {\n  return this.as._jsonRequest({\n    method: 'POST',\n    url: '/1/indexes/' + encodeURIComponent(this.indexName) + '/browse',\n    body: {cursor: cursor},\n    hostType: 'read',\n    callback: callback\n  });\n};\n\n/*\n* Search for facet values\n* https://www.algolia.com/doc/rest-api/search#search-for-facet-values\n*\n* @param {string} params.facetName Facet name, name of the attribute to search for values in.\n* Must be declared as a facet\n* @param {string} params.facetQuery Query for the facet search\n* @param {string} [params.*] Any search parameter of Algolia,\n* see https://www.algolia.com/doc/api-client/javascript/search#search-parameters\n* Pagination is not supported. The page and hitsPerPage parameters will be ignored.\n* @param callback (optional)\n*/\nIndexCore.prototype.searchForFacetValues = function(params, callback) {\n  var clone = require('./clone.js');\n  var omit = require('./omit.js');\n  var usage = 'Usage: index.searchForFacetValues({facetName, facetQuery, ...params}[, callback])';\n\n  if (params.facetName === undefined || params.facetQuery === undefined) {\n    throw new Error(usage);\n  }\n\n  var facetName = params.facetName;\n  var filteredParams = omit(clone(params), function(keyName) {\n    return keyName === 'facetName';\n  });\n  var searchParameters = this.as._getSearchParams(filteredParams, '');\n\n  return this.as._jsonRequest({\n    method: 'POST',\n    url: '/1/indexes/' +\n      encodeURIComponent(this.indexName) + '/facets/' + encodeURIComponent(facetName) + '/query',\n    hostType: 'read',\n    body: {params: searchParameters},\n    callback: callback\n  });\n};\n\nIndexCore.prototype.searchFacet = deprecate(function(params, callback) {\n  return this.searchForFacetValues(params, callback);\n}, deprecatedMessage(\n  'index.searchFacet(params[, callback])',\n  'index.searchForFacetValues(params[, callback])'\n));\n\nIndexCore.prototype._search = function(params, url, callback, additionalUA) {\n  return this.as._jsonRequest({\n    cache: this.cache,\n    method: 'POST',\n    url: url || '/1/indexes/' + encodeURIComponent(this.indexName) + '/query',\n    body: {params: params},\n    hostType: 'read',\n    fallback: {\n      method: 'GET',\n      url: '/1/indexes/' + encodeURIComponent(this.indexName),\n      body: {params: params}\n    },\n    callback: callback,\n    additionalUA: additionalUA\n  });\n};\n\n/*\n* Get an object from this index\n*\n* @param objectID the unique identifier of the object to retrieve\n* @param attrs (optional) if set, contains the array of attribute names to retrieve\n* @param callback (optional) the result callback called with two arguments\n*  error: null or Error('message')\n*  content: the object to retrieve or the error message if a failure occurred\n*/\nIndexCore.prototype.getObject = function(objectID, attrs, callback) {\n  var indexObj = this;\n\n  if (arguments.length === 1 || typeof attrs === 'function') {\n    callback = attrs;\n    attrs = undefined;\n  }\n\n  var params = '';\n  if (attrs !== undefined) {\n    params = '?attributes=';\n    for (var i = 0; i < attrs.length; ++i) {\n      if (i !== 0) {\n        params += ',';\n      }\n      params += attrs[i];\n    }\n  }\n\n  return this.as._jsonRequest({\n    method: 'GET',\n    url: '/1/indexes/' + encodeURIComponent(indexObj.indexName) + '/' + encodeURIComponent(objectID) + params,\n    hostType: 'read',\n    callback: callback\n  });\n};\n\n/*\n* Get several objects from this index\n*\n* @param objectIDs the array of unique identifier of objects to retrieve\n*/\nIndexCore.prototype.getObjects = function(objectIDs, attributesToRetrieve, callback) {\n  var isArray = require('isarray');\n  var map = require('./map.js');\n\n  var usage = 'Usage: index.getObjects(arrayOfObjectIDs[, callback])';\n\n  if (!isArray(objectIDs)) {\n    throw new Error(usage);\n  }\n\n  var indexObj = this;\n\n  if (arguments.length === 1 || typeof attributesToRetrieve === 'function') {\n    callback = attributesToRetrieve;\n    attributesToRetrieve = undefined;\n  }\n\n  var body = {\n    requests: map(objectIDs, function prepareRequest(objectID) {\n      var request = {\n        indexName: indexObj.indexName,\n        objectID: objectID\n      };\n\n      if (attributesToRetrieve) {\n        request.attributesToRetrieve = attributesToRetrieve.join(',');\n      }\n\n      return request;\n    })\n  };\n\n  return this.as._jsonRequest({\n    method: 'POST',\n    url: '/1/indexes/*/objects',\n    hostType: 'read',\n    body: body,\n    callback: callback\n  });\n};\n\nIndexCore.prototype.as = null;\nIndexCore.prototype.indexName = null;\nIndexCore.prototype.typeAheadArgs = null;\nIndexCore.prototype.typeAheadValueOption = null;\n", "module.exports = function deprecate(fn, message) {\n  var warned = false;\n\n  function deprecated() {\n    if (!warned) {\n      /* eslint no-console:0 */\n      console.warn(message);\n      warned = true;\n    }\n\n    return fn.apply(this, arguments);\n  }\n\n  return deprecated;\n};\n", "module.exports = function deprecatedMessage(previousUsage, newUsage) {\n  var githubAnchorLink = previousUsage.toLowerCase()\n    .replace(/[\\.\\(\\)]/g, '');\n\n  return 'algoliasearch: `' + previousUsage + '` was replaced by `' + newUsage +\n    '`. Please see https://github.com/algolia/algoliasearch-client-javascript/wiki/Deprecated#' + githubAnchorLink;\n};\n", "var foreach = require('foreach');\n\nmodule.exports = function merge(destination/* , sources */) {\n  var sources = Array.prototype.slice.call(arguments);\n\n  foreach(sources, function(source) {\n    for (var keyName in source) {\n      if (source.hasOwnProperty(keyName)) {\n        if (typeof destination[keyName] === 'object' && typeof source[keyName] === 'object') {\n          destination[keyName] = merge({}, destination[keyName], source[keyName]);\n        } else if (source[keyName] !== undefined) {\n          destination[keyName] = source[keyName];\n        }\n      }\n    }\n  });\n\n  return destination;\n};\n", "'use strict';\n\nvar slice = Array.prototype.slice;\nvar isArgs = require('./isArguments');\n\nvar origKeys = Object.keys;\nvar keysShim = origKeys ? function keys(o) { return origKeys(o); } : require('./implementation');\n\nvar originalKeys = Object.keys;\n\nkeysShim.shim = function shimObjectKeys() {\n\tif (Object.keys) {\n\t\tvar keysWorksWithArguments = (function () {\n\t\t\t// Safari 5.0 bug\n\t\t\tvar args = Object.keys(arguments);\n\t\t\treturn args && args.length === arguments.length;\n\t\t}(1, 2));\n\t\tif (!keysWorksWithArguments) {\n\t\t\tObject.keys = function keys(object) { // eslint-disable-line func-name-matching\n\t\t\t\tif (isArgs(object)) {\n\t\t\t\t\treturn originalKeys(slice.call(object));\n\t\t\t\t}\n\t\t\t\treturn originalKeys(object);\n\t\t\t};\n\t\t}\n\t} else {\n\t\tObject.keys = keysShim;\n\t}\n\treturn Object.keys || keysShim;\n};\n\nmodule.exports = keysShim;\n", "'use strict';\n\nvar keysShim;\nif (!Object.keys) {\n\t// modified from https://github.com/es-shims/es5-shim\n\tvar has = Object.prototype.hasOwnProperty;\n\tvar toStr = Object.prototype.toString;\n\tvar isArgs = require('./isArguments'); // eslint-disable-line global-require\n\tvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\tvar hasDontEnumBug = !isEnumerable.call({ toString: null }, 'toString');\n\tvar hasProtoEnumBug = isEnumerable.call(function () {}, 'prototype');\n\tvar dontEnums = [\n\t\t'toString',\n\t\t'toLocaleString',\n\t\t'valueOf',\n\t\t'hasOwnProperty',\n\t\t'isPrototypeOf',\n\t\t'propertyIsEnumerable',\n\t\t'constructor'\n\t];\n\tvar equalsConstructorPrototype = function (o) {\n\t\tvar ctor = o.constructor;\n\t\treturn ctor && ctor.prototype === o;\n\t};\n\tvar excludedKeys = {\n\t\t$applicationCache: true,\n\t\t$console: true,\n\t\t$external: true,\n\t\t$frame: true,\n\t\t$frameElement: true,\n\t\t$frames: true,\n\t\t$innerHeight: true,\n\t\t$innerWidth: true,\n\t\t$onmozfullscreenchange: true,\n\t\t$onmozfullscreenerror: true,\n\t\t$outerHeight: true,\n\t\t$outerWidth: true,\n\t\t$pageXOffset: true,\n\t\t$pageYOffset: true,\n\t\t$parent: true,\n\t\t$scrollLeft: true,\n\t\t$scrollTop: true,\n\t\t$scrollX: true,\n\t\t$scrollY: true,\n\t\t$self: true,\n\t\t$webkitIndexedDB: true,\n\t\t$webkitStorageInfo: true,\n\t\t$window: true\n\t};\n\tvar hasAutomationEqualityBug = (function () {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined') { return false; }\n\t\tfor (var k in window) {\n\t\t\ttry {\n\t\t\t\tif (!excludedKeys['$' + k] && has.call(window, k) && window[k] !== null && typeof window[k] === 'object') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tequalsConstructorPrototype(window[k]);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}());\n\tvar equalsConstructorPrototypeIfNotBuggy = function (o) {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined' || !hasAutomationEqualityBug) {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t}\n\t\ttry {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t};\n\n\tkeysShim = function keys(object) {\n\t\tvar isObject = object !== null && typeof object === 'object';\n\t\tvar isFunction = toStr.call(object) === '[object Function]';\n\t\tvar isArguments = isArgs(object);\n\t\tvar isString = isObject && toStr.call(object) === '[object String]';\n\t\tvar theKeys = [];\n\n\t\tif (!isObject && !isFunction && !isArguments) {\n\t\t\tthrow new TypeError('Object.keys called on a non-object');\n\t\t}\n\n\t\tvar skipProto = hasProtoEnumBug && isFunction;\n\t\tif (isString && object.length > 0 && !has.call(object, 0)) {\n\t\t\tfor (var i = 0; i < object.length; ++i) {\n\t\t\t\ttheKeys.push(String(i));\n\t\t\t}\n\t\t}\n\n\t\tif (isArguments && object.length > 0) {\n\t\t\tfor (var j = 0; j < object.length; ++j) {\n\t\t\t\ttheKeys.push(String(j));\n\t\t\t}\n\t\t} else {\n\t\t\tfor (var name in object) {\n\t\t\t\tif (!(skipProto && name === 'prototype') && has.call(object, name)) {\n\t\t\t\t\ttheKeys.push(String(name));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (hasDontEnumBug) {\n\t\t\tvar skipConstructor = equalsConstructorPrototypeIfNotBuggy(object);\n\n\t\t\tfor (var k = 0; k < dontEnums.length; ++k) {\n\t\t\t\tif (!(skipConstructor && dontEnums[k] === 'constructor') && has.call(object, dontEnums[k])) {\n\t\t\t\t\ttheKeys.push(dontEnums[k]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn theKeys;\n\t};\n}\nmodule.exports = keysShim;\n", "var debug = require('debug')('algoliasearch:src/hostIndexState.js');\nvar localStorageNamespace = 'algoliasearch-client-js';\n\nvar store;\nvar moduleStore = {\n  state: {},\n  set: function(key, data) {\n    this.state[key] = data;\n    return this.state[key];\n  },\n  get: function(key) {\n    return this.state[key] || null;\n  }\n};\n\nvar localStorageStore = {\n  set: function(key, data) {\n    moduleStore.set(key, data); // always replicate localStorageStore to moduleStore in case of failure\n\n    try {\n      var namespace = JSON.parse(global.localStorage[localStorageNamespace]);\n      namespace[key] = data;\n      global.localStorage[localStorageNamespace] = JSON.stringify(namespace);\n      return namespace[key];\n    } catch (e) {\n      return localStorageFailure(key, e);\n    }\n  },\n  get: function(key) {\n    try {\n      return JSON.parse(global.localStorage[localStorageNamespace])[key] || null;\n    } catch (e) {\n      return localStorageFailure(key, e);\n    }\n  }\n};\n\nfunction localStorageFailure(key, e) {\n  debug('localStorage failed with', e);\n  cleanup();\n  store = moduleStore;\n  return store.get(key);\n}\n\nstore = supportsLocalStorage() ? localStorageStore : moduleStore;\n\nmodule.exports = {\n  get: getOrSet,\n  set: getOrSet,\n  supportsLocalStorage: supportsLocalStorage\n};\n\nfunction getOrSet(key, data) {\n  if (arguments.length === 1) {\n    return store.get(key);\n  }\n\n  return store.set(key, data);\n}\n\nfunction supportsLocalStorage() {\n  try {\n    if ('localStorage' in global &&\n      global.localStorage !== null) {\n      if (!global.localStorage[localStorageNamespace]) {\n        // actual creation of the namespace\n        global.localStorage.setItem(localStorageNamespace, JSON.stringify({}));\n      }\n      return true;\n    }\n\n    return false;\n  } catch (_) {\n    return false;\n  }\n}\n\n// In case of any error on localStorage, we clean our own namespace, this should handle\n// quota errors when a lot of keys + data are used\nfunction cleanup() {\n  try {\n    global.localStorage.removeItem(localStorageNamespace);\n  } catch (_) {\n    // nothing to do\n  }\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Previous log timestamp.\n */\n\nvar prevTime;\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  return debug;\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (var i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isNaN(val) === false) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  if (ms >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (ms >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (ms >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (ms >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  return plural(ms, d, 'day') ||\n    plural(ms, h, 'hour') ||\n    plural(ms, m, 'minute') ||\n    plural(ms, s, 'second') ||\n    ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, n, name) {\n  if (ms < n) {\n    return;\n  }\n  if (ms < n * 1.5) {\n    return Math.floor(ms / n) + ' ' + name;\n  }\n  return Math.ceil(ms / n) + ' ' + name + 's';\n}\n", "'use strict';\n\nvar global = require('global');\nvar Promise = global.Promise || require('es6-promise').Promise;\n\n// This is the standalone browser build entry point\n// Browser implementation of the Algolia Search JavaScript client,\n// using XMLHttpRequest, XDomainRequest and JSONP as fallback\nmodule.exports = function createAlgoliasearch(AlgoliaSearch, uaSuffix) {\n  var inherits = require('inherits');\n  var errors = require('../errors');\n  var inlineHeaders = require('./inline-headers');\n  var jsonpRequest = require('./jsonp-request');\n  var places = require('../places.js');\n  uaSuffix = uaSuffix || '';\n\n  if (process.env.NODE_ENV === 'debug') {\n    require('debug').enable('algoliasearch*');\n  }\n\n  function algoliasearch(applicationID, apiKey, opts) {\n    var cloneDeep = require('../clone.js');\n\n    opts = cloneDeep(opts || {});\n\n    opts._ua = opts._ua || algoliasearch.ua;\n\n    return new AlgoliaSearchBrowser(applicationID, apiKey, opts);\n  }\n\n  algoliasearch.version = require('../version.js');\n\n  algoliasearch.ua =\n    'Algolia for JavaScript (' + algoliasearch.version + '); ' + uaSuffix;\n\n  algoliasearch.initPlaces = places(algoliasearch);\n\n  // we expose into window no matter how we are used, this will allow\n  // us to easily debug any website running algolia\n  global.__algolia = {\n    debug: require('debug'),\n    algoliasearch: algoliasearch\n  };\n\n  var support = {\n    hasXMLHttpRequest: 'XMLHttpRequest' in global,\n    hasXDomainRequest: 'XDomainRequest' in global\n  };\n\n  if (support.hasXMLHttpRequest) {\n    support.cors = 'withCredentials' in new XMLHttpRequest();\n  }\n\n  function AlgoliaSearchBrowser() {\n    // call AlgoliaSearch constructor\n    AlgoliaSearch.apply(this, arguments);\n  }\n\n  inherits(AlgoliaSearchBrowser, AlgoliaSearch);\n\n  AlgoliaSearchBrowser.prototype._request = function request(url, opts) {\n    return new Promise(function wrapRequest(resolve, reject) {\n      // no cors or XDomainRequest, no request\n      if (!support.cors && !support.hasXDomainRequest) {\n        // very old browser, not supported\n        reject(new errors.Network('CORS not supported'));\n        return;\n      }\n\n      url = inlineHeaders(url, opts.headers);\n\n      var body = opts.body;\n      var req = support.cors ? new XMLHttpRequest() : new XDomainRequest();\n      var reqTimeout;\n      var timedOut;\n      var connected = false;\n\n      reqTimeout = setTimeout(onTimeout, opts.timeouts.connect);\n      // we set an empty onprogress listener\n      // so that XDomainRequest on IE9 is not aborted\n      // refs:\n      //  - https://github.com/algolia/algoliasearch-client-js/issues/76\n      //  - https://social.msdn.microsoft.com/Forums/ie/en-US/30ef3add-767c-4436-b8a9-f1ca19b4812e/ie9-rtm-xdomainrequest-issued-requests-may-abort-if-all-event-handlers-not-specified?forum=iewebdevelopment\n      req.onprogress = onProgress;\n      if ('onreadystatechange' in req) req.onreadystatechange = onReadyStateChange;\n      req.onload = onLoad;\n      req.onerror = onError;\n\n      // do not rely on default XHR async flag, as some analytics code like hotjar\n      // breaks it and set it to false by default\n      if (req instanceof XMLHttpRequest) {\n        req.open(opts.method, url, true);\n\n        // The Analytics API never accepts Auth headers as query string\n        // this option exists specifically for them.\n        if (opts.forceAuthHeaders) {\n          req.setRequestHeader(\n            'x-algolia-application-id',\n            opts.headers['x-algolia-application-id']\n          );\n          req.setRequestHeader(\n            'x-algolia-api-key',\n            opts.headers['x-algolia-api-key']\n          );\n        }\n      } else {\n        req.open(opts.method, url);\n      }\n\n      // headers are meant to be sent after open\n      if (support.cors) {\n        if (body) {\n          if (opts.method === 'POST') {\n            // https://developer.mozilla.org/en-US/docs/Web/HTTP/Access_control_CORS#Simple_requests\n            req.setRequestHeader('content-type', 'application/x-www-form-urlencoded');\n          } else {\n            req.setRequestHeader('content-type', 'application/json');\n          }\n        }\n        req.setRequestHeader('accept', 'application/json');\n      }\n\n      if (body) {\n        req.send(body);\n      } else {\n        req.send();\n      }\n\n      // event object not received in IE8, at least\n      // but we do not use it, still important to note\n      function onLoad(/* event */) {\n        // When browser does not supports req.timeout, we can\n        // have both a load and timeout event, since handled by a dumb setTimeout\n        if (timedOut) {\n          return;\n        }\n\n        clearTimeout(reqTimeout);\n\n        var out;\n\n        try {\n          out = {\n            body: JSON.parse(req.responseText),\n            responseText: req.responseText,\n            statusCode: req.status,\n            // XDomainRequest does not have any response headers\n            headers: req.getAllResponseHeaders && req.getAllResponseHeaders() || {}\n          };\n        } catch (e) {\n          out = new errors.UnparsableJSON({\n            more: req.responseText\n          });\n        }\n\n        if (out instanceof errors.UnparsableJSON) {\n          reject(out);\n        } else {\n          resolve(out);\n        }\n      }\n\n      function onError(event) {\n        if (timedOut) {\n          return;\n        }\n\n        clearTimeout(reqTimeout);\n\n        // error event is trigerred both with XDR/XHR on:\n        //   - DNS error\n        //   - unallowed cross domain request\n        reject(\n          new errors.Network({\n            more: event\n          })\n        );\n      }\n\n      function onTimeout() {\n        timedOut = true;\n        req.abort();\n\n        reject(new errors.RequestTimeout());\n      }\n\n      function onConnect() {\n        connected = true;\n        clearTimeout(reqTimeout);\n        reqTimeout = setTimeout(onTimeout, opts.timeouts.complete);\n      }\n\n      function onProgress() {\n        if (!connected) onConnect();\n      }\n\n      function onReadyStateChange() {\n        if (!connected && req.readyState > 1) onConnect();\n      }\n    });\n  };\n\n  AlgoliaSearchBrowser.prototype._request.fallback = function requestFallback(url, opts) {\n    url = inlineHeaders(url, opts.headers);\n\n    return new Promise(function wrapJsonpRequest(resolve, reject) {\n      jsonpRequest(url, opts, function jsonpRequestDone(err, content) {\n        if (err) {\n          reject(err);\n          return;\n        }\n\n        resolve(content);\n      });\n    });\n  };\n\n  AlgoliaSearchBrowser.prototype._promise = {\n    reject: function rejectPromise(val) {\n      return Promise.reject(val);\n    },\n    resolve: function resolvePromise(val) {\n      return Promise.resolve(val);\n    },\n    delay: function delayPromise(ms) {\n      return new Promise(function resolveOnTimeout(resolve/* , reject*/) {\n        setTimeout(resolve, ms);\n      });\n    },\n    all: function all(promises) {\n      return Promise.all(promises);\n    }\n  };\n\n  return algoliasearch;\n};\n", "var win;\n\nif (typeof window !== \"undefined\") {\n    win = window;\n} else if (typeof global !== \"undefined\") {\n    win = global;\n} else if (typeof self !== \"undefined\"){\n    win = self;\n} else {\n    win = {};\n}\n\nmodule.exports = win;\n", "/*!\n * @overview es6-promise - a tiny implementation of Promises/A+.\n * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)\n * @license   Licensed under MIT license\n *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n * @version   v4.2.8+1e68dce6\n */\n\n(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.ES6Promise = factory());\n}(this, (function () { 'use strict';\n\nfunction objectOrFunction(x) {\n  var type = typeof x;\n  return x !== null && (type === 'object' || type === 'function');\n}\n\nfunction isFunction(x) {\n  return typeof x === 'function';\n}\n\n\n\nvar _isArray = void 0;\nif (Array.isArray) {\n  _isArray = Array.isArray;\n} else {\n  _isArray = function (x) {\n    return Object.prototype.toString.call(x) === '[object Array]';\n  };\n}\n\nvar isArray = _isArray;\n\nvar len = 0;\nvar vertxNext = void 0;\nvar customSchedulerFn = void 0;\n\nvar asap = function asap(callback, arg) {\n  queue[len] = callback;\n  queue[len + 1] = arg;\n  len += 2;\n  if (len === 2) {\n    // If len is 2, that means that we need to schedule an async flush.\n    // If additional callbacks are queued before the queue is flushed, they\n    // will be processed by this flush that we are scheduling.\n    if (customSchedulerFn) {\n      customSchedulerFn(flush);\n    } else {\n      scheduleFlush();\n    }\n  }\n};\n\nfunction setScheduler(scheduleFn) {\n  customSchedulerFn = scheduleFn;\n}\n\nfunction setAsap(asapFn) {\n  asap = asapFn;\n}\n\nvar browserWindow = typeof window !== 'undefined' ? window : undefined;\nvar browserGlobal = browserWindow || {};\nvar BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\nvar isNode = typeof self === 'undefined' && typeof process !== 'undefined' && {}.toString.call(process) === '[object process]';\n\n// test for web worker but not in IE10\nvar isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n// node\nfunction useNextTick() {\n  // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n  // see https://github.com/cujojs/when/issues/410 for details\n  return function () {\n    return process.nextTick(flush);\n  };\n}\n\n// vertx\nfunction useVertxTimer() {\n  if (typeof vertxNext !== 'undefined') {\n    return function () {\n      vertxNext(flush);\n    };\n  }\n\n  return useSetTimeout();\n}\n\nfunction useMutationObserver() {\n  var iterations = 0;\n  var observer = new BrowserMutationObserver(flush);\n  var node = document.createTextNode('');\n  observer.observe(node, { characterData: true });\n\n  return function () {\n    node.data = iterations = ++iterations % 2;\n  };\n}\n\n// web worker\nfunction useMessageChannel() {\n  var channel = new MessageChannel();\n  channel.port1.onmessage = flush;\n  return function () {\n    return channel.port2.postMessage(0);\n  };\n}\n\nfunction useSetTimeout() {\n  // Store setTimeout reference so es6-promise will be unaffected by\n  // other code modifying setTimeout (like sinon.useFakeTimers())\n  var globalSetTimeout = setTimeout;\n  return function () {\n    return globalSetTimeout(flush, 1);\n  };\n}\n\nvar queue = new Array(1000);\nfunction flush() {\n  for (var i = 0; i < len; i += 2) {\n    var callback = queue[i];\n    var arg = queue[i + 1];\n\n    callback(arg);\n\n    queue[i] = undefined;\n    queue[i + 1] = undefined;\n  }\n\n  len = 0;\n}\n\nfunction attemptVertx() {\n  try {\n    var vertx = Function('return this')().require('vertx');\n    vertxNext = vertx.runOnLoop || vertx.runOnContext;\n    return useVertxTimer();\n  } catch (e) {\n    return useSetTimeout();\n  }\n}\n\nvar scheduleFlush = void 0;\n// Decide what async method to use to triggering processing of queued callbacks:\nif (isNode) {\n  scheduleFlush = useNextTick();\n} else if (BrowserMutationObserver) {\n  scheduleFlush = useMutationObserver();\n} else if (isWorker) {\n  scheduleFlush = useMessageChannel();\n} else if (browserWindow === undefined && typeof require === 'function') {\n  scheduleFlush = attemptVertx();\n} else {\n  scheduleFlush = useSetTimeout();\n}\n\nfunction then(onFulfillment, onRejection) {\n  var parent = this;\n\n  var child = new this.constructor(noop);\n\n  if (child[PROMISE_ID] === undefined) {\n    makePromise(child);\n  }\n\n  var _state = parent._state;\n\n\n  if (_state) {\n    var callback = arguments[_state - 1];\n    asap(function () {\n      return invokeCallback(_state, child, callback, parent._result);\n    });\n  } else {\n    subscribe(parent, child, onFulfillment, onRejection);\n  }\n\n  return child;\n}\n\n/**\n  `Promise.resolve` returns a promise that will become resolved with the\n  passed `value`. It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    resolve(1);\n  });\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.resolve(1);\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  @method resolve\n  @static\n  @param {Any} value value that the returned promise will be resolved with\n  Useful for tooling.\n  @return {Promise} a promise that will become fulfilled with the given\n  `value`\n*/\nfunction resolve$1(object) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (object && typeof object === 'object' && object.constructor === Constructor) {\n    return object;\n  }\n\n  var promise = new Constructor(noop);\n  resolve(promise, object);\n  return promise;\n}\n\nvar PROMISE_ID = Math.random().toString(36).substring(2);\n\nfunction noop() {}\n\nvar PENDING = void 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\n\nfunction selfFulfillment() {\n  return new TypeError(\"You cannot resolve a promise with itself\");\n}\n\nfunction cannotReturnOwn() {\n  return new TypeError('A promises callback cannot return that same promise.');\n}\n\nfunction tryThen(then$$1, value, fulfillmentHandler, rejectionHandler) {\n  try {\n    then$$1.call(value, fulfillmentHandler, rejectionHandler);\n  } catch (e) {\n    return e;\n  }\n}\n\nfunction handleForeignThenable(promise, thenable, then$$1) {\n  asap(function (promise) {\n    var sealed = false;\n    var error = tryThen(then$$1, thenable, function (value) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n      if (thenable !== value) {\n        resolve(promise, value);\n      } else {\n        fulfill(promise, value);\n      }\n    }, function (reason) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n\n      reject(promise, reason);\n    }, 'Settle: ' + (promise._label || ' unknown promise'));\n\n    if (!sealed && error) {\n      sealed = true;\n      reject(promise, error);\n    }\n  }, promise);\n}\n\nfunction handleOwnThenable(promise, thenable) {\n  if (thenable._state === FULFILLED) {\n    fulfill(promise, thenable._result);\n  } else if (thenable._state === REJECTED) {\n    reject(promise, thenable._result);\n  } else {\n    subscribe(thenable, undefined, function (value) {\n      return resolve(promise, value);\n    }, function (reason) {\n      return reject(promise, reason);\n    });\n  }\n}\n\nfunction handleMaybeThenable(promise, maybeThenable, then$$1) {\n  if (maybeThenable.constructor === promise.constructor && then$$1 === then && maybeThenable.constructor.resolve === resolve$1) {\n    handleOwnThenable(promise, maybeThenable);\n  } else {\n    if (then$$1 === undefined) {\n      fulfill(promise, maybeThenable);\n    } else if (isFunction(then$$1)) {\n      handleForeignThenable(promise, maybeThenable, then$$1);\n    } else {\n      fulfill(promise, maybeThenable);\n    }\n  }\n}\n\nfunction resolve(promise, value) {\n  if (promise === value) {\n    reject(promise, selfFulfillment());\n  } else if (objectOrFunction(value)) {\n    var then$$1 = void 0;\n    try {\n      then$$1 = value.then;\n    } catch (error) {\n      reject(promise, error);\n      return;\n    }\n    handleMaybeThenable(promise, value, then$$1);\n  } else {\n    fulfill(promise, value);\n  }\n}\n\nfunction publishRejection(promise) {\n  if (promise._onerror) {\n    promise._onerror(promise._result);\n  }\n\n  publish(promise);\n}\n\nfunction fulfill(promise, value) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n\n  promise._result = value;\n  promise._state = FULFILLED;\n\n  if (promise._subscribers.length !== 0) {\n    asap(publish, promise);\n  }\n}\n\nfunction reject(promise, reason) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n  promise._state = REJECTED;\n  promise._result = reason;\n\n  asap(publishRejection, promise);\n}\n\nfunction subscribe(parent, child, onFulfillment, onRejection) {\n  var _subscribers = parent._subscribers;\n  var length = _subscribers.length;\n\n\n  parent._onerror = null;\n\n  _subscribers[length] = child;\n  _subscribers[length + FULFILLED] = onFulfillment;\n  _subscribers[length + REJECTED] = onRejection;\n\n  if (length === 0 && parent._state) {\n    asap(publish, parent);\n  }\n}\n\nfunction publish(promise) {\n  var subscribers = promise._subscribers;\n  var settled = promise._state;\n\n  if (subscribers.length === 0) {\n    return;\n  }\n\n  var child = void 0,\n      callback = void 0,\n      detail = promise._result;\n\n  for (var i = 0; i < subscribers.length; i += 3) {\n    child = subscribers[i];\n    callback = subscribers[i + settled];\n\n    if (child) {\n      invokeCallback(settled, child, callback, detail);\n    } else {\n      callback(detail);\n    }\n  }\n\n  promise._subscribers.length = 0;\n}\n\nfunction invokeCallback(settled, promise, callback, detail) {\n  var hasCallback = isFunction(callback),\n      value = void 0,\n      error = void 0,\n      succeeded = true;\n\n  if (hasCallback) {\n    try {\n      value = callback(detail);\n    } catch (e) {\n      succeeded = false;\n      error = e;\n    }\n\n    if (promise === value) {\n      reject(promise, cannotReturnOwn());\n      return;\n    }\n  } else {\n    value = detail;\n  }\n\n  if (promise._state !== PENDING) {\n    // noop\n  } else if (hasCallback && succeeded) {\n    resolve(promise, value);\n  } else if (succeeded === false) {\n    reject(promise, error);\n  } else if (settled === FULFILLED) {\n    fulfill(promise, value);\n  } else if (settled === REJECTED) {\n    reject(promise, value);\n  }\n}\n\nfunction initializePromise(promise, resolver) {\n  try {\n    resolver(function resolvePromise(value) {\n      resolve(promise, value);\n    }, function rejectPromise(reason) {\n      reject(promise, reason);\n    });\n  } catch (e) {\n    reject(promise, e);\n  }\n}\n\nvar id = 0;\nfunction nextId() {\n  return id++;\n}\n\nfunction makePromise(promise) {\n  promise[PROMISE_ID] = id++;\n  promise._state = undefined;\n  promise._result = undefined;\n  promise._subscribers = [];\n}\n\nfunction validationError() {\n  return new Error('Array Methods must be provided an Array');\n}\n\nvar Enumerator = function () {\n  function Enumerator(Constructor, input) {\n    this._instanceConstructor = Constructor;\n    this.promise = new Constructor(noop);\n\n    if (!this.promise[PROMISE_ID]) {\n      makePromise(this.promise);\n    }\n\n    if (isArray(input)) {\n      this.length = input.length;\n      this._remaining = input.length;\n\n      this._result = new Array(this.length);\n\n      if (this.length === 0) {\n        fulfill(this.promise, this._result);\n      } else {\n        this.length = this.length || 0;\n        this._enumerate(input);\n        if (this._remaining === 0) {\n          fulfill(this.promise, this._result);\n        }\n      }\n    } else {\n      reject(this.promise, validationError());\n    }\n  }\n\n  Enumerator.prototype._enumerate = function _enumerate(input) {\n    for (var i = 0; this._state === PENDING && i < input.length; i++) {\n      this._eachEntry(input[i], i);\n    }\n  };\n\n  Enumerator.prototype._eachEntry = function _eachEntry(entry, i) {\n    var c = this._instanceConstructor;\n    var resolve$$1 = c.resolve;\n\n\n    if (resolve$$1 === resolve$1) {\n      var _then = void 0;\n      var error = void 0;\n      var didError = false;\n      try {\n        _then = entry.then;\n      } catch (e) {\n        didError = true;\n        error = e;\n      }\n\n      if (_then === then && entry._state !== PENDING) {\n        this._settledAt(entry._state, i, entry._result);\n      } else if (typeof _then !== 'function') {\n        this._remaining--;\n        this._result[i] = entry;\n      } else if (c === Promise$1) {\n        var promise = new c(noop);\n        if (didError) {\n          reject(promise, error);\n        } else {\n          handleMaybeThenable(promise, entry, _then);\n        }\n        this._willSettleAt(promise, i);\n      } else {\n        this._willSettleAt(new c(function (resolve$$1) {\n          return resolve$$1(entry);\n        }), i);\n      }\n    } else {\n      this._willSettleAt(resolve$$1(entry), i);\n    }\n  };\n\n  Enumerator.prototype._settledAt = function _settledAt(state, i, value) {\n    var promise = this.promise;\n\n\n    if (promise._state === PENDING) {\n      this._remaining--;\n\n      if (state === REJECTED) {\n        reject(promise, value);\n      } else {\n        this._result[i] = value;\n      }\n    }\n\n    if (this._remaining === 0) {\n      fulfill(promise, this._result);\n    }\n  };\n\n  Enumerator.prototype._willSettleAt = function _willSettleAt(promise, i) {\n    var enumerator = this;\n\n    subscribe(promise, undefined, function (value) {\n      return enumerator._settledAt(FULFILLED, i, value);\n    }, function (reason) {\n      return enumerator._settledAt(REJECTED, i, reason);\n    });\n  };\n\n  return Enumerator;\n}();\n\n/**\n  `Promise.all` accepts an array of promises, and returns a new promise which\n  is fulfilled with an array of fulfillment values for the passed promises, or\n  rejected with the reason of the first passed promise to be rejected. It casts all\n  elements of the passed iterable to promises as it runs this algorithm.\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = resolve(2);\n  let promise3 = resolve(3);\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // The array here would be [ 1, 2, 3 ];\n  });\n  ```\n\n  If any of the `promises` given to `all` are rejected, the first promise\n  that is rejected will be given as an argument to the returned promises's\n  rejection handler. For example:\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = reject(new Error(\"2\"));\n  let promise3 = reject(new Error(\"3\"));\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // Code here never runs because there are rejected promises!\n  }, function(error) {\n    // error.message === \"2\"\n  });\n  ```\n\n  @method all\n  @static\n  @param {Array} entries array of promises\n  @param {String} label optional string for labeling the promise.\n  Useful for tooling.\n  @return {Promise} promise that is fulfilled when all `promises` have been\n  fulfilled, or rejected if any of them become rejected.\n  @static\n*/\nfunction all(entries) {\n  return new Enumerator(this, entries).promise;\n}\n\n/**\n  `Promise.race` returns a new promise which is settled in the same way as the\n  first passed promise to settle.\n\n  Example:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 2');\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // result === 'promise 2' because it was resolved before promise1\n    // was resolved.\n  });\n  ```\n\n  `Promise.race` is deterministic in that only the state of the first\n  settled promise matters. For example, even if other promises given to the\n  `promises` array argument are resolved, but the first settled promise has\n  become rejected before the other promises became fulfilled, the returned\n  promise will become rejected:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      reject(new Error('promise 2'));\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // Code here never runs\n  }, function(reason){\n    // reason.message === 'promise 2' because promise 2 became rejected before\n    // promise 1 became fulfilled\n  });\n  ```\n\n  An example real-world use case is implementing timeouts:\n\n  ```javascript\n  Promise.race([ajax('foo.json'), timeout(5000)])\n  ```\n\n  @method race\n  @static\n  @param {Array} promises array of promises to observe\n  Useful for tooling.\n  @return {Promise} a promise which settles in the same way as the first passed\n  promise to settle.\n*/\nfunction race(entries) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (!isArray(entries)) {\n    return new Constructor(function (_, reject) {\n      return reject(new TypeError('You must pass an array to race.'));\n    });\n  } else {\n    return new Constructor(function (resolve, reject) {\n      var length = entries.length;\n      for (var i = 0; i < length; i++) {\n        Constructor.resolve(entries[i]).then(resolve, reject);\n      }\n    });\n  }\n}\n\n/**\n  `Promise.reject` returns a promise rejected with the passed `reason`.\n  It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    reject(new Error('WHOOPS'));\n  });\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.reject(new Error('WHOOPS'));\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  @method reject\n  @static\n  @param {Any} reason value that the returned promise will be rejected with.\n  Useful for tooling.\n  @return {Promise} a promise rejected with the given `reason`.\n*/\nfunction reject$1(reason) {\n  /*jshint validthis:true */\n  var Constructor = this;\n  var promise = new Constructor(noop);\n  reject(promise, reason);\n  return promise;\n}\n\nfunction needsResolver() {\n  throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n}\n\nfunction needsNew() {\n  throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n}\n\n/**\n  Promise objects represent the eventual result of an asynchronous operation. The\n  primary way of interacting with a promise is through its `then` method, which\n  registers callbacks to receive either a promise's eventual value or the reason\n  why the promise cannot be fulfilled.\n\n  Terminology\n  -----------\n\n  - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n  - `thenable` is an object or function that defines a `then` method.\n  - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n  - `exception` is a value that is thrown using the throw statement.\n  - `reason` is a value that indicates why a promise was rejected.\n  - `settled` the final resting state of a promise, fulfilled or rejected.\n\n  A promise can be in one of three states: pending, fulfilled, or rejected.\n\n  Promises that are fulfilled have a fulfillment value and are in the fulfilled\n  state.  Promises that are rejected have a rejection reason and are in the\n  rejected state.  A fulfillment value is never a thenable.\n\n  Promises can also be said to *resolve* a value.  If this value is also a\n  promise, then the original promise's settled state will match the value's\n  settled state.  So a promise that *resolves* a promise that rejects will\n  itself reject, and a promise that *resolves* a promise that fulfills will\n  itself fulfill.\n\n\n  Basic Usage:\n  ------------\n\n  ```js\n  let promise = new Promise(function(resolve, reject) {\n    // on success\n    resolve(value);\n\n    // on failure\n    reject(reason);\n  });\n\n  promise.then(function(value) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Advanced Usage:\n  ---------------\n\n  Promises shine when abstracting away asynchronous interactions such as\n  `XMLHttpRequest`s.\n\n  ```js\n  function getJSON(url) {\n    return new Promise(function(resolve, reject){\n      let xhr = new XMLHttpRequest();\n\n      xhr.open('GET', url);\n      xhr.onreadystatechange = handler;\n      xhr.responseType = 'json';\n      xhr.setRequestHeader('Accept', 'application/json');\n      xhr.send();\n\n      function handler() {\n        if (this.readyState === this.DONE) {\n          if (this.status === 200) {\n            resolve(this.response);\n          } else {\n            reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n          }\n        }\n      };\n    });\n  }\n\n  getJSON('/posts.json').then(function(json) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Unlike callbacks, promises are great composable primitives.\n\n  ```js\n  Promise.all([\n    getJSON('/posts'),\n    getJSON('/comments')\n  ]).then(function(values){\n    values[0] // => postsJSON\n    values[1] // => commentsJSON\n\n    return values;\n  });\n  ```\n\n  @class Promise\n  @param {Function} resolver\n  Useful for tooling.\n  @constructor\n*/\n\nvar Promise$1 = function () {\n  function Promise(resolver) {\n    this[PROMISE_ID] = nextId();\n    this._result = this._state = undefined;\n    this._subscribers = [];\n\n    if (noop !== resolver) {\n      typeof resolver !== 'function' && needsResolver();\n      this instanceof Promise ? initializePromise(this, resolver) : needsNew();\n    }\n  }\n\n  /**\n  The primary way of interacting with a promise is through its `then` method,\n  which registers callbacks to receive either a promise's eventual value or the\n  reason why the promise cannot be fulfilled.\n   ```js\n  findUser().then(function(user){\n    // user is available\n  }, function(reason){\n    // user is unavailable, and you are given the reason why\n  });\n  ```\n   Chaining\n  --------\n   The return value of `then` is itself a promise.  This second, 'downstream'\n  promise is resolved with the return value of the first promise's fulfillment\n  or rejection handler, or rejected if the handler throws an exception.\n   ```js\n  findUser().then(function (user) {\n    return user.name;\n  }, function (reason) {\n    return 'default name';\n  }).then(function (userName) {\n    // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n    // will be `'default name'`\n  });\n   findUser().then(function (user) {\n    throw new Error('Found user, but still unhappy');\n  }, function (reason) {\n    throw new Error('`findUser` rejected and we're unhappy');\n  }).then(function (value) {\n    // never reached\n  }, function (reason) {\n    // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n    // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n  });\n  ```\n  If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n   ```js\n  findUser().then(function (user) {\n    throw new PedagogicalException('Upstream error');\n  }).then(function (value) {\n    // never reached\n  }).then(function (value) {\n    // never reached\n  }, function (reason) {\n    // The `PedgagocialException` is propagated all the way down to here\n  });\n  ```\n   Assimilation\n  ------------\n   Sometimes the value you want to propagate to a downstream promise can only be\n  retrieved asynchronously. This can be achieved by returning a promise in the\n  fulfillment or rejection handler. The downstream promise will then be pending\n  until the returned promise is settled. This is called *assimilation*.\n   ```js\n  findUser().then(function (user) {\n    return findCommentsByAuthor(user);\n  }).then(function (comments) {\n    // The user's comments are now available\n  });\n  ```\n   If the assimliated promise rejects, then the downstream promise will also reject.\n   ```js\n  findUser().then(function (user) {\n    return findCommentsByAuthor(user);\n  }).then(function (comments) {\n    // If `findCommentsByAuthor` fulfills, we'll have the value here\n  }, function (reason) {\n    // If `findCommentsByAuthor` rejects, we'll have the reason here\n  });\n  ```\n   Simple Example\n  --------------\n   Synchronous Example\n   ```javascript\n  let result;\n   try {\n    result = findResult();\n    // success\n  } catch(reason) {\n    // failure\n  }\n  ```\n   Errback Example\n   ```js\n  findResult(function(result, err){\n    if (err) {\n      // failure\n    } else {\n      // success\n    }\n  });\n  ```\n   Promise Example;\n   ```javascript\n  findResult().then(function(result){\n    // success\n  }, function(reason){\n    // failure\n  });\n  ```\n   Advanced Example\n  --------------\n   Synchronous Example\n   ```javascript\n  let author, books;\n   try {\n    author = findAuthor();\n    books  = findBooksByAuthor(author);\n    // success\n  } catch(reason) {\n    // failure\n  }\n  ```\n   Errback Example\n   ```js\n   function foundBooks(books) {\n   }\n   function failure(reason) {\n   }\n   findAuthor(function(author, err){\n    if (err) {\n      failure(err);\n      // failure\n    } else {\n      try {\n        findBoooksByAuthor(author, function(books, err) {\n          if (err) {\n            failure(err);\n          } else {\n            try {\n              foundBooks(books);\n            } catch(reason) {\n              failure(reason);\n            }\n          }\n        });\n      } catch(error) {\n        failure(err);\n      }\n      // success\n    }\n  });\n  ```\n   Promise Example;\n   ```javascript\n  findAuthor().\n    then(findBooksByAuthor).\n    then(function(books){\n      // found books\n  }).catch(function(reason){\n    // something went wrong\n  });\n  ```\n   @method then\n  @param {Function} onFulfilled\n  @param {Function} onRejected\n  Useful for tooling.\n  @return {Promise}\n  */\n\n  /**\n  `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n  as the catch block of a try/catch statement.\n  ```js\n  function findAuthor(){\n  throw new Error('couldn't find that author');\n  }\n  // synchronous\n  try {\n  findAuthor();\n  } catch(reason) {\n  // something went wrong\n  }\n  // async with promises\n  findAuthor().catch(function(reason){\n  // something went wrong\n  });\n  ```\n  @method catch\n  @param {Function} onRejection\n  Useful for tooling.\n  @return {Promise}\n  */\n\n\n  Promise.prototype.catch = function _catch(onRejection) {\n    return this.then(null, onRejection);\n  };\n\n  /**\n    `finally` will be invoked regardless of the promise's fate just as native\n    try/catch/finally behaves\n  \n    Synchronous example:\n  \n    ```js\n    findAuthor() {\n      if (Math.random() > 0.5) {\n        throw new Error();\n      }\n      return new Author();\n    }\n  \n    try {\n      return findAuthor(); // succeed or fail\n    } catch(error) {\n      return findOtherAuther();\n    } finally {\n      // always runs\n      // doesn't affect the return value\n    }\n    ```\n  \n    Asynchronous example:\n  \n    ```js\n    findAuthor().catch(function(reason){\n      return findOtherAuther();\n    }).finally(function(){\n      // author was either found, or not\n    });\n    ```\n  \n    @method finally\n    @param {Function} callback\n    @return {Promise}\n  */\n\n\n  Promise.prototype.finally = function _finally(callback) {\n    var promise = this;\n    var constructor = promise.constructor;\n\n    if (isFunction(callback)) {\n      return promise.then(function (value) {\n        return constructor.resolve(callback()).then(function () {\n          return value;\n        });\n      }, function (reason) {\n        return constructor.resolve(callback()).then(function () {\n          throw reason;\n        });\n      });\n    }\n\n    return promise.then(callback, callback);\n  };\n\n  return Promise;\n}();\n\nPromise$1.prototype.then = then;\nPromise$1.all = all;\nPromise$1.race = race;\nPromise$1.resolve = resolve$1;\nPromise$1.reject = reject$1;\nPromise$1._setScheduler = setScheduler;\nPromise$1._setAsap = setAsap;\nPromise$1._asap = asap;\n\n/*global self*/\nfunction polyfill() {\n  var local = void 0;\n\n  if (typeof global !== 'undefined') {\n    local = global;\n  } else if (typeof self !== 'undefined') {\n    local = self;\n  } else {\n    try {\n      local = Function('return this')();\n    } catch (e) {\n      throw new Error('polyfill failed because global object is unavailable in this environment');\n    }\n  }\n\n  var P = local.Promise;\n\n  if (P) {\n    var promiseToString = null;\n    try {\n      promiseToString = Object.prototype.toString.call(P.resolve());\n    } catch (e) {\n      // silently ignored\n    }\n\n    if (promiseToString === '[object Promise]' && !P.cast) {\n      return;\n    }\n  }\n\n  local.Promise = Promise$1;\n}\n\n// Strange compat..\nPromise$1.polyfill = polyfill;\nPromise$1.Promise = Promise$1;\n\nreturn Promise$1;\n\n})));\n\n\n\n//# sourceMappingURL=es6-promise.map\n", "'use strict';\n\nmodule.exports = inlineHeaders;\n\nvar encode = require('querystring-es3/encode');\n\nfunction inlineHeaders(url, headers) {\n  if (/\\?/.test(url)) {\n    url += '&';\n  } else {\n    url += '?';\n  }\n\n  return url + encode(headers);\n}\n", "'use strict';\n\nmodule.exports = jsonpRequest;\n\nvar errors = require('../errors');\n\nvar JSONPCounter = 0;\n\nfunction jsonpRequest(url, opts, cb) {\n  if (opts.method !== 'GET') {\n    cb(new Error('Method ' + opts.method + ' ' + url + ' is not supported by JSONP.'));\n    return;\n  }\n\n  opts.debug('JSONP: start');\n\n  var cbCalled = false;\n  var timedOut = false;\n\n  JSONPCounter += 1;\n  var head = document.getElementsByTagName('head')[0];\n  var script = document.createElement('script');\n  var cbName = 'algoliaJSONP_' + JSONPCounter;\n  var done = false;\n\n  window[cbName] = function(data) {\n    removeGlobals();\n\n    if (timedOut) {\n      opts.debug('JSONP: Late answer, ignoring');\n      return;\n    }\n\n    cbCalled = true;\n\n    clean();\n\n    cb(null, {\n      body: data,\n      responseText: JSON.stringify(data)/* ,\n      // We do not send the statusCode, there's no statusCode in JSONP, it will be\n      // computed using data.status && data.message like with XDR\n      statusCode*/\n    });\n  };\n\n  // add callback by hand\n  url += '&callback=' + cbName;\n\n  // add body params manually\n  if (opts.jsonBody && opts.jsonBody.params) {\n    url += '&' + opts.jsonBody.params;\n  }\n\n  var ontimeout = setTimeout(timeout, opts.timeouts.complete);\n\n  // script onreadystatechange needed only for\n  // <= IE8\n  // https://github.com/angular/angular.js/issues/4523\n  script.onreadystatechange = readystatechange;\n  script.onload = success;\n  script.onerror = error;\n\n  script.async = true;\n  script.defer = true;\n  script.src = url;\n  head.appendChild(script);\n\n  function success() {\n    opts.debug('JSONP: success');\n\n    if (done || timedOut) {\n      return;\n    }\n\n    done = true;\n\n    // script loaded but did not call the fn => script loading error\n    if (!cbCalled) {\n      opts.debug('JSONP: Fail. Script loaded but did not call the callback');\n      clean();\n      cb(new errors.JSONPScriptFail());\n    }\n  }\n\n  function readystatechange() {\n    if (this.readyState === 'loaded' || this.readyState === 'complete') {\n      success();\n    }\n  }\n\n  function clean() {\n    clearTimeout(ontimeout);\n    script.onload = null;\n    script.onreadystatechange = null;\n    script.onerror = null;\n    head.removeChild(script);\n  }\n\n  function removeGlobals() {\n    try {\n      delete window[cbName];\n      delete window[cbName + '_loaded'];\n    } catch (e) {\n      window[cbName] = window[cbName + '_loaded'] = undefined;\n    }\n  }\n\n  function timeout() {\n    opts.debug('JSONP: Script timeout');\n    timedOut = true;\n    clean();\n    cb(new errors.RequestTimeout());\n  }\n\n  function error() {\n    opts.debug('JSONP: Script error');\n\n    if (done || timedOut) {\n      return;\n    }\n\n    clean();\n    cb(new errors.JSONPScriptError());\n  }\n}\n", "module.exports = createPlacesClient;\n\nvar qs3 = require('querystring-es3');\nvar buildSearchMethod = require('./buildSearchMethod.js');\n\nfunction createPlacesClient(algoliasearch) {\n  return function places(appID, apiKey, opts) {\n    var cloneDeep = require('./clone.js');\n\n    opts = opts && cloneDeep(opts) || {};\n    opts.hosts = opts.hosts || [\n      'places-dsn.algolia.net',\n      'places-1.algolianet.com',\n      'places-2.algolianet.com',\n      'places-3.algolianet.com'\n    ];\n\n    // allow initPlaces() no arguments => community rate limited\n    if (arguments.length === 0 || typeof appID === 'object' || appID === undefined) {\n      appID = '';\n      apiKey = '';\n      opts._allowEmptyCredentials = true;\n    }\n\n    var client = algoliasearch(appID, apiKey, opts);\n    var index = client.initIndex('places');\n    index.search = buildSearchMethod('query', '/1/places/query');\n    index.reverse = function(options, callback) {\n      var encoded = qs3.encode(options);\n\n      return this.as._jsonRequest({\n        method: 'GET',\n        url: '/1/places/reverse?' + encoded,\n        hostType: 'read',\n        callback: callback\n      });\n    };\n\n    index.getObject = function(objectID, callback) {\n      return this.as._jsonRequest({\n        method: 'GET',\n        url: '/1/places/' + encodeURIComponent(objectID),\n        hostType: 'read',\n        callback: callback\n      });\n    };\n    return index;\n  };\n}\n", "'use strict';\n\nexports.decode = exports.parse = require('./decode');\nexports.encode = exports.stringify = require('./encode');\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n// If obj.hasOwnProperty has been overridden, then calling\n// obj.hasOwnProperty(prop) will break.\n// See: https://github.com/joyent/node/issues/1707\nfunction hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nmodule.exports = function(qs, sep, eq, options) {\n  sep = sep || '&';\n  eq = eq || '=';\n  var obj = {};\n\n  if (typeof qs !== 'string' || qs.length === 0) {\n    return obj;\n  }\n\n  var regexp = /\\+/g;\n  qs = qs.split(sep);\n\n  var maxKeys = 1000;\n  if (options && typeof options.maxKeys === 'number') {\n    maxKeys = options.maxKeys;\n  }\n\n  var len = qs.length;\n  // maxKeys <= 0 means that we should not limit keys count\n  if (maxKeys > 0 && len > maxKeys) {\n    len = maxKeys;\n  }\n\n  for (var i = 0; i < len; ++i) {\n    var x = qs[i].replace(regexp, '%20'),\n        idx = x.indexOf(eq),\n        kstr, vstr, k, v;\n\n    if (idx >= 0) {\n      kstr = x.substr(0, idx);\n      vstr = x.substr(idx + 1);\n    } else {\n      kstr = x;\n      vstr = '';\n    }\n\n    k = decodeURIComponent(kstr);\n    v = decodeURIComponent(vstr);\n\n    if (!hasOwnProperty(obj, k)) {\n      obj[k] = v;\n    } else if (isArray(obj[k])) {\n      obj[k].push(v);\n    } else {\n      obj[k] = [obj[k], v];\n    }\n  }\n\n  return obj;\n};\n\nvar isArray = Array.isArray || function (xs) {\n  return Object.prototype.toString.call(xs) === '[object Array]';\n};\n", "'use strict';\n\nmodule.exports = '3.35.1';\n", "'use strict';\n\n// this will inject <PERSON>epto in window, unfortunately no easy commonJS zepto build\nvar zepto = require('../../zepto.js');\n\n// setup DOM element\nvar DOM = require('../common/dom.js');\nDOM.element = zepto;\n\n// setup utils functions\nvar _ = require('../common/utils.js');\n_.isArray = zepto.isArray;\n_.isFunction = zepto.isFunction;\n_.isObject = zepto.isPlainObject;\n_.bind = zepto.proxy;\n_.each = function(collection, cb) {\n  // stupid argument order for jQuery.each\n  zepto.each(collection, reverseArgs);\n  function reverseArgs(index, value) {\n    return cb(value, index);\n  }\n};\n_.map = zepto.map;\n_.mixin = zepto.extend;\n_.Event = zepto.Event;\n\nvar typeaheadKey = 'aaAutocomplete';\nvar Typeahead = require('../autocomplete/typeahead.js');\nvar EventBus = require('../autocomplete/event_bus.js');\n\nfunction autocomplete(selector, options, datasets, typeaheadObject) {\n  datasets = _.isArray(datasets) ? datasets : [].slice.call(arguments, 2);\n\n  var inputs = zepto(selector).each(function(i, input) {\n    var $input = zepto(input);\n    var eventBus = new EventBus({el: $input});\n    var typeahead = typeaheadObject || new Typeahead({\n      input: $input,\n      eventBus: eventBus,\n      dropdownMenuContainer: options.dropdownMenuContainer,\n      hint: options.hint === undefined ? true : !!options.hint,\n      minLength: options.minLength,\n      autoselect: options.autoselect,\n      autoselectOnBlur: options.autoselectOnBlur,\n      tabAutocomplete: options.tabAutocomplete,\n      openOnFocus: options.openOnFocus,\n      templates: options.templates,\n      debug: options.debug,\n      clearOnSelected: options.clearOnSelected,\n      cssClasses: options.cssClasses,\n      datasets: datasets,\n      keyboardShortcuts: options.keyboardShortcuts,\n      appendTo: options.appendTo,\n      autoWidth: options.autoWidth,\n      ariaLabel: options.ariaLabel || input.getAttribute('aria-label')\n    });\n    $input.data(typeaheadKey, typeahead);\n  });\n\n  // expose all methods in the `autocomplete` attribute\n  inputs.autocomplete = {};\n  _.each(['open', 'close', 'getVal', 'setVal', 'destroy', 'getWrapper'], function(method) {\n    inputs.autocomplete[method] = function() {\n      var methodArguments = arguments;\n      var result;\n      inputs.each(function(j, input) {\n        var typeahead = zepto(input).data(typeaheadKey);\n        result = typeahead[method].apply(typeahead, methodArguments);\n      });\n      return result;\n    };\n  });\n\n  return inputs;\n}\n\nautocomplete.sources = Typeahead.sources;\nautocomplete.escapeHighlightedString = _.escapeHighlightedString;\n\nvar wasAutocompleteSet = 'autocomplete' in window;\nvar oldAutocomplete = window.autocomplete;\nautocomplete.noConflict = function noConflict() {\n  if (wasAutocompleteSet) {\n    window.autocomplete = oldAutocomplete;\n  } else {\n    delete window.autocomplete;\n  }\n  return autocomplete;\n};\n\nmodule.exports = autocomplete;\n", "/* istanbul ignore next */\n/* Zepto v1.2.0 - zepto event assets data - zeptojs.com/license */\n(function(global, factory) {\n  module.exports = factory(global);\n}(/* this ##### UPDATED: here we want to use window/global instead of this which is the current file context ##### */ window, function(window) {\n  var Zepto = (function() {\n  var undefined, key, $, classList, emptyArray = [], concat = emptyArray.concat, filter = emptyArray.filter, slice = emptyArray.slice,\n    document = window.document,\n    elementDisplay = {}, classCache = {},\n    cssNumber = { 'column-count': 1, 'columns': 1, 'font-weight': 1, 'line-height': 1,'opacity': 1, 'z-index': 1, 'zoom': 1 },\n    fragmentRE = /^\\s*<(\\w+|!)[^>]*>/,\n    singleTagRE = /^<(\\w+)\\s*\\/?>(?:<\\/\\1>|)$/,\n    tagExpanderRE = /<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\\w:]+)[^>]*)\\/>/ig,\n    rootNodeRE = /^(?:body|html)$/i,\n    capitalRE = /([A-Z])/g,\n\n    // special attributes that should be get/set via method calls\n    methodAttributes = ['val', 'css', 'html', 'text', 'data', 'width', 'height', 'offset'],\n\n    adjacencyOperators = [ 'after', 'prepend', 'before', 'append' ],\n    table = document.createElement('table'),\n    tableRow = document.createElement('tr'),\n    containers = {\n      'tr': document.createElement('tbody'),\n      'tbody': table, 'thead': table, 'tfoot': table,\n      'td': tableRow, 'th': tableRow,\n      '*': document.createElement('div')\n    },\n    readyRE = /complete|loaded|interactive/,\n    simpleSelectorRE = /^[\\w-]*$/,\n    class2type = {},\n    toString = class2type.toString,\n    zepto = {},\n    camelize, uniq,\n    tempParent = document.createElement('div'),\n    propMap = {\n      'tabindex': 'tabIndex',\n      'readonly': 'readOnly',\n      'for': 'htmlFor',\n      'class': 'className',\n      'maxlength': 'maxLength',\n      'cellspacing': 'cellSpacing',\n      'cellpadding': 'cellPadding',\n      'rowspan': 'rowSpan',\n      'colspan': 'colSpan',\n      'usemap': 'useMap',\n      'frameborder': 'frameBorder',\n      'contenteditable': 'contentEditable'\n    },\n    isArray = Array.isArray ||\n      function(object){ return object instanceof Array }\n\n  zepto.matches = function(element, selector) {\n    if (!selector || !element || element.nodeType !== 1) return false\n    var matchesSelector = element.matches || element.webkitMatchesSelector ||\n                          element.mozMatchesSelector || element.oMatchesSelector ||\n                          element.matchesSelector\n    if (matchesSelector) return matchesSelector.call(element, selector)\n    // fall back to performing a selector:\n    var match, parent = element.parentNode, temp = !parent\n    if (temp) (parent = tempParent).appendChild(element)\n    match = ~zepto.qsa(parent, selector).indexOf(element)\n    temp && tempParent.removeChild(element)\n    return match\n  }\n\n  function type(obj) {\n    return obj == null ? String(obj) :\n      class2type[toString.call(obj)] || \"object\"\n  }\n\n  function isFunction(value) { return type(value) == \"function\" }\n  function isWindow(obj)     { return obj != null && obj == obj.window }\n  function isDocument(obj)   { return obj != null && obj.nodeType == obj.DOCUMENT_NODE }\n  function isObject(obj)     { return type(obj) == \"object\" }\n  function isPlainObject(obj) {\n    return isObject(obj) && !isWindow(obj) && Object.getPrototypeOf(obj) == Object.prototype\n  }\n\n  function likeArray(obj) {\n    var length = !!obj && 'length' in obj && obj.length,\n      type = $.type(obj)\n\n    return 'function' != type && !isWindow(obj) && (\n      'array' == type || length === 0 ||\n        (typeof length == 'number' && length > 0 && (length - 1) in obj)\n    )\n  }\n\n  function compact(array) { return filter.call(array, function(item){ return item != null }) }\n  function flatten(array) { return array.length > 0 ? $.fn.concat.apply([], array) : array }\n  camelize = function(str){ return str.replace(/-+(.)?/g, function(match, chr){ return chr ? chr.toUpperCase() : '' }) }\n  function dasherize(str) {\n    return str.replace(/::/g, '/')\n           .replace(/([A-Z]+)([A-Z][a-z])/g, '$1_$2')\n           .replace(/([a-z\\d])([A-Z])/g, '$1_$2')\n           .replace(/_/g, '-')\n           .toLowerCase()\n  }\n  uniq = function(array){ return filter.call(array, function(item, idx){ return array.indexOf(item) == idx }) }\n\n  function classRE(name) {\n    return name in classCache ?\n      classCache[name] : (classCache[name] = new RegExp('(^|\\\\s)' + name + '(\\\\s|$)'))\n  }\n\n  function maybeAddPx(name, value) {\n    return (typeof value == \"number\" && !cssNumber[dasherize(name)]) ? value + \"px\" : value\n  }\n\n  function defaultDisplay(nodeName) {\n    var element, display\n    if (!elementDisplay[nodeName]) {\n      element = document.createElement(nodeName)\n      document.body.appendChild(element)\n      display = getComputedStyle(element, '').getPropertyValue(\"display\")\n      element.parentNode.removeChild(element)\n      display == \"none\" && (display = \"block\")\n      elementDisplay[nodeName] = display\n    }\n    return elementDisplay[nodeName]\n  }\n\n  function children(element) {\n    return 'children' in element ?\n      slice.call(element.children) :\n      $.map(element.childNodes, function(node){ if (node.nodeType == 1) return node })\n  }\n\n  function Z(dom, selector) {\n    var i, len = dom ? dom.length : 0\n    for (i = 0; i < len; i++) this[i] = dom[i]\n    this.length = len\n    this.selector = selector || ''\n  }\n\n  // `$.zepto.fragment` takes a html string and an optional tag name\n  // to generate DOM nodes from the given html string.\n  // The generated DOM nodes are returned as an array.\n  // This function can be overridden in plugins for example to make\n  // it compatible with browsers that don't support the DOM fully.\n  zepto.fragment = function(html, name, properties) {\n    var dom, nodes, container\n\n    // A special case optimization for a single tag\n    if (singleTagRE.test(html)) dom = $(document.createElement(RegExp.$1))\n\n    if (!dom) {\n      if (html.replace) html = html.replace(tagExpanderRE, \"<$1></$2>\")\n      if (name === undefined) name = fragmentRE.test(html) && RegExp.$1\n      if (!(name in containers)) name = '*'\n\n      container = containers[name]\n      container.innerHTML = '' + html\n      dom = $.each(slice.call(container.childNodes), function(){\n        container.removeChild(this)\n      })\n    }\n\n    if (isPlainObject(properties)) {\n      nodes = $(dom)\n      $.each(properties, function(key, value) {\n        if (methodAttributes.indexOf(key) > -1) nodes[key](value)\n        else nodes.attr(key, value)\n      })\n    }\n\n    return dom\n  }\n\n  // `$.zepto.Z` swaps out the prototype of the given `dom` array\n  // of nodes with `$.fn` and thus supplying all the Zepto functions\n  // to the array. This method can be overridden in plugins.\n  zepto.Z = function(dom, selector) {\n    return new Z(dom, selector)\n  }\n\n  // `$.zepto.isZ` should return `true` if the given object is a Zepto\n  // collection. This method can be overridden in plugins.\n  zepto.isZ = function(object) {\n    return object instanceof zepto.Z\n  }\n\n  // `$.zepto.init` is Zepto's counterpart to jQuery's `$.fn.init` and\n  // takes a CSS selector and an optional context (and handles various\n  // special cases).\n  // This method can be overridden in plugins.\n  zepto.init = function(selector, context) {\n    var dom\n    // If nothing given, return an empty Zepto collection\n    if (!selector) return zepto.Z()\n    // Optimize for string selectors\n    else if (typeof selector == 'string') {\n      selector = selector.trim()\n      // If it's a html fragment, create nodes from it\n      // Note: In both Chrome 21 and Firefox 15, DOM error 12\n      // is thrown if the fragment doesn't begin with <\n      if (selector[0] == '<' && fragmentRE.test(selector))\n        dom = zepto.fragment(selector, RegExp.$1, context), selector = null\n      // If there's a context, create a collection on that context first, and select\n      // nodes from there\n      else if (context !== undefined) return $(context).find(selector)\n      // If it's a CSS selector, use it to select nodes.\n      else dom = zepto.qsa(document, selector)\n    }\n    // If a function is given, call it when the DOM is ready\n    else if (isFunction(selector)) return $(document).ready(selector)\n    // If a Zepto collection is given, just return it\n    else if (zepto.isZ(selector)) return selector\n    else {\n      // normalize array if an array of nodes is given\n      if (isArray(selector)) dom = compact(selector)\n      // Wrap DOM nodes.\n      else if (isObject(selector))\n        dom = [selector], selector = null\n      // If it's a html fragment, create nodes from it\n      else if (fragmentRE.test(selector))\n        dom = zepto.fragment(selector.trim(), RegExp.$1, context), selector = null\n      // If there's a context, create a collection on that context first, and select\n      // nodes from there\n      else if (context !== undefined) return $(context).find(selector)\n      // And last but no least, if it's a CSS selector, use it to select nodes.\n      else dom = zepto.qsa(document, selector)\n    }\n    // create a new Zepto collection from the nodes found\n    return zepto.Z(dom, selector)\n  }\n\n  // `$` will be the base `Zepto` object. When calling this\n  // function just call `$.zepto.init, which makes the implementation\n  // details of selecting nodes and creating Zepto collections\n  // patchable in plugins.\n  $ = function(selector, context){\n    return zepto.init(selector, context)\n  }\n\n  function extend(target, source, deep) {\n    for (key in source)\n      if (deep && (isPlainObject(source[key]) || isArray(source[key]))) {\n        if (isPlainObject(source[key]) && !isPlainObject(target[key]))\n          target[key] = {}\n        if (isArray(source[key]) && !isArray(target[key]))\n          target[key] = []\n        extend(target[key], source[key], deep)\n      }\n      else if (source[key] !== undefined) target[key] = source[key]\n  }\n\n  // Copy all but undefined properties from one or more\n  // objects to the `target` object.\n  $.extend = function(target){\n    var deep, args = slice.call(arguments, 1)\n    if (typeof target == 'boolean') {\n      deep = target\n      target = args.shift()\n    }\n    args.forEach(function(arg){ extend(target, arg, deep) })\n    return target\n  }\n\n  // `$.zepto.qsa` is Zepto's CSS selector implementation which\n  // uses `document.querySelectorAll` and optimizes for some special cases, like `#id`.\n  // This method can be overridden in plugins.\n  zepto.qsa = function(element, selector){\n    var found,\n        maybeID = selector[0] == '#',\n        maybeClass = !maybeID && selector[0] == '.',\n        nameOnly = maybeID || maybeClass ? selector.slice(1) : selector, // Ensure that a 1 char tag name still gets checked\n        isSimple = simpleSelectorRE.test(nameOnly)\n    return (element.getElementById && isSimple && maybeID) ? // Safari DocumentFragment doesn't have getElementById\n      ( (found = element.getElementById(nameOnly)) ? [found] : [] ) :\n      (element.nodeType !== 1 && element.nodeType !== 9 && element.nodeType !== 11) ? [] :\n      slice.call(\n        isSimple && !maybeID && element.getElementsByClassName ? // DocumentFragment doesn't have getElementsByClassName/TagName\n          maybeClass ? element.getElementsByClassName(nameOnly) : // If it's simple, it could be a class\n          element.getElementsByTagName(selector) : // Or a tag\n          element.querySelectorAll(selector) // Or it's not simple, and we need to query all\n      )\n  }\n\n  function filtered(nodes, selector) {\n    return selector == null ? $(nodes) : $(nodes).filter(selector)\n  }\n\n  $.contains = document.documentElement.contains ?\n    function(parent, node) {\n      return parent !== node && parent.contains(node)\n    } :\n    function(parent, node) {\n      while (node && (node = node.parentNode))\n        if (node === parent) return true\n      return false\n    }\n\n  function funcArg(context, arg, idx, payload) {\n    return isFunction(arg) ? arg.call(context, idx, payload) : arg\n  }\n\n  function setAttribute(node, name, value) {\n    value == null ? node.removeAttribute(name) : node.setAttribute(name, value)\n  }\n\n  // access className property while respecting SVGAnimatedString\n  function className(node, value){\n    var klass = node.className || '',\n        svg   = klass && klass.baseVal !== undefined\n\n    if (value === undefined) return svg ? klass.baseVal : klass\n    svg ? (klass.baseVal = value) : (node.className = value)\n  }\n\n  // \"true\"  => true\n  // \"false\" => false\n  // \"null\"  => null\n  // \"42\"    => 42\n  // \"42.5\"  => 42.5\n  // \"08\"    => \"08\"\n  // JSON    => parse if valid\n  // String  => self\n  function deserializeValue(value) {\n    try {\n      return value ?\n        value == \"true\" ||\n        ( value == \"false\" ? false :\n          value == \"null\" ? null :\n          +value + \"\" == value ? +value :\n          /^[\\[\\{]/.test(value) ? $.parseJSON(value) :\n          value )\n        : value\n    } catch(e) {\n      return value\n    }\n  }\n\n  $.type = type\n  $.isFunction = isFunction\n  $.isWindow = isWindow\n  $.isArray = isArray\n  $.isPlainObject = isPlainObject\n\n  $.isEmptyObject = function(obj) {\n    var name\n    for (name in obj) return false\n    return true\n  }\n\n  $.isNumeric = function(val) {\n    var num = Number(val), type = typeof val\n    return val != null && type != 'boolean' &&\n      (type != 'string' || val.length) &&\n      !isNaN(num) && isFinite(num) || false\n  }\n\n  $.inArray = function(elem, array, i){\n    return emptyArray.indexOf.call(array, elem, i)\n  }\n\n  $.camelCase = camelize\n  $.trim = function(str) {\n    return str == null ? \"\" : String.prototype.trim.call(str)\n  }\n\n  // plugin compatibility\n  $.uuid = 0\n  $.support = { }\n  $.expr = { }\n  $.noop = function() {}\n\n  $.map = function(elements, callback){\n    var value, values = [], i, key\n    if (likeArray(elements))\n      for (i = 0; i < elements.length; i++) {\n        value = callback(elements[i], i)\n        if (value != null) values.push(value)\n      }\n    else\n      for (key in elements) {\n        value = callback(elements[key], key)\n        if (value != null) values.push(value)\n      }\n    return flatten(values)\n  }\n\n  $.each = function(elements, callback){\n    var i, key\n    if (likeArray(elements)) {\n      for (i = 0; i < elements.length; i++)\n        if (callback.call(elements[i], i, elements[i]) === false) return elements\n    } else {\n      for (key in elements)\n        if (callback.call(elements[key], key, elements[key]) === false) return elements\n    }\n\n    return elements\n  }\n\n  $.grep = function(elements, callback){\n    return filter.call(elements, callback)\n  }\n\n  if (window.JSON) $.parseJSON = JSON.parse\n\n  // Populate the class2type map\n  $.each(\"Boolean Number String Function Array Date RegExp Object Error\".split(\" \"), function(i, name) {\n    class2type[ \"[object \" + name + \"]\" ] = name.toLowerCase()\n  })\n\n  // Define methods that will be available on all\n  // Zepto collections\n  $.fn = {\n    constructor: zepto.Z,\n    length: 0,\n\n    // Because a collection acts like an array\n    // copy over these useful array functions.\n    forEach: emptyArray.forEach,\n    reduce: emptyArray.reduce,\n    push: emptyArray.push,\n    sort: emptyArray.sort,\n    splice: emptyArray.splice,\n    indexOf: emptyArray.indexOf,\n    concat: function(){\n      var i, value, args = []\n      for (i = 0; i < arguments.length; i++) {\n        value = arguments[i]\n        args[i] = zepto.isZ(value) ? value.toArray() : value\n      }\n      return concat.apply(zepto.isZ(this) ? this.toArray() : this, args)\n    },\n\n    // `map` and `slice` in the jQuery API work differently\n    // from their array counterparts\n    map: function(fn){\n      return $($.map(this, function(el, i){ return fn.call(el, i, el) }))\n    },\n    slice: function(){\n      return $(slice.apply(this, arguments))\n    },\n\n    ready: function(callback){\n      // need to check if document.body exists for IE as that browser reports\n      // document ready when it hasn't yet created the body element\n      if (readyRE.test(document.readyState) && document.body) callback($)\n      else document.addEventListener('DOMContentLoaded', function(){ callback($) }, false)\n      return this\n    },\n    get: function(idx){\n      return idx === undefined ? slice.call(this) : this[idx >= 0 ? idx : idx + this.length]\n    },\n    toArray: function(){ return this.get() },\n    size: function(){\n      return this.length\n    },\n    remove: function(){\n      return this.each(function(){\n        if (this.parentNode != null)\n          this.parentNode.removeChild(this)\n      })\n    },\n    each: function(callback){\n      emptyArray.every.call(this, function(el, idx){\n        return callback.call(el, idx, el) !== false\n      })\n      return this\n    },\n    filter: function(selector){\n      if (isFunction(selector)) return this.not(this.not(selector))\n      return $(filter.call(this, function(element){\n        return zepto.matches(element, selector)\n      }))\n    },\n    add: function(selector,context){\n      return $(uniq(this.concat($(selector,context))))\n    },\n    is: function(selector){\n      return this.length > 0 && zepto.matches(this[0], selector)\n    },\n    not: function(selector){\n      var nodes=[]\n      if (isFunction(selector) && selector.call !== undefined)\n        this.each(function(idx){\n          if (!selector.call(this,idx)) nodes.push(this)\n        })\n      else {\n        var excludes = typeof selector == 'string' ? this.filter(selector) :\n          (likeArray(selector) && isFunction(selector.item)) ? slice.call(selector) : $(selector)\n        this.forEach(function(el){\n          if (excludes.indexOf(el) < 0) nodes.push(el)\n        })\n      }\n      return $(nodes)\n    },\n    has: function(selector){\n      return this.filter(function(){\n        return isObject(selector) ?\n          $.contains(this, selector) :\n          $(this).find(selector).size()\n      })\n    },\n    eq: function(idx){\n      return idx === -1 ? this.slice(idx) : this.slice(idx, + idx + 1)\n    },\n    first: function(){\n      var el = this[0]\n      return el && !isObject(el) ? el : $(el)\n    },\n    last: function(){\n      var el = this[this.length - 1]\n      return el && !isObject(el) ? el : $(el)\n    },\n    find: function(selector){\n      var result, $this = this\n      if (!selector) result = $()\n      else if (typeof selector == 'object')\n        result = $(selector).filter(function(){\n          var node = this\n          return emptyArray.some.call($this, function(parent){\n            return $.contains(parent, node)\n          })\n        })\n      else if (this.length == 1) result = $(zepto.qsa(this[0], selector))\n      else result = this.map(function(){ return zepto.qsa(this, selector) })\n      return result\n    },\n    closest: function(selector, context){\n      var nodes = [], collection = typeof selector == 'object' && $(selector)\n      this.each(function(_, node){\n        while (node && !(collection ? collection.indexOf(node) >= 0 : zepto.matches(node, selector)))\n          node = node !== context && !isDocument(node) && node.parentNode\n        if (node && nodes.indexOf(node) < 0) nodes.push(node)\n      })\n      return $(nodes)\n    },\n    parents: function(selector){\n      var ancestors = [], nodes = this\n      while (nodes.length > 0)\n        nodes = $.map(nodes, function(node){\n          if ((node = node.parentNode) && !isDocument(node) && ancestors.indexOf(node) < 0) {\n            ancestors.push(node)\n            return node\n          }\n        })\n      return filtered(ancestors, selector)\n    },\n    parent: function(selector){\n      return filtered(uniq(this.pluck('parentNode')), selector)\n    },\n    children: function(selector){\n      return filtered(this.map(function(){ return children(this) }), selector)\n    },\n    contents: function() {\n      return this.map(function() { return this.contentDocument || slice.call(this.childNodes) })\n    },\n    siblings: function(selector){\n      return filtered(this.map(function(i, el){\n        return filter.call(children(el.parentNode), function(child){ return child!==el })\n      }), selector)\n    },\n    empty: function(){\n      return this.each(function(){ this.innerHTML = '' })\n    },\n    // `pluck` is borrowed from Prototype.js\n    pluck: function(property){\n      return $.map(this, function(el){ return el[property] })\n    },\n    show: function(){\n      return this.each(function(){\n        this.style.display == \"none\" && (this.style.display = '')\n        if (getComputedStyle(this, '').getPropertyValue(\"display\") == \"none\")\n          this.style.display = defaultDisplay(this.nodeName)\n      })\n    },\n    replaceWith: function(newContent){\n      return this.before(newContent).remove()\n    },\n    wrap: function(structure){\n      var func = isFunction(structure)\n      if (this[0] && !func)\n        var dom   = $(structure).get(0),\n            clone = dom.parentNode || this.length > 1\n\n      return this.each(function(index){\n        $(this).wrapAll(\n          func ? structure.call(this, index) :\n            clone ? dom.cloneNode(true) : dom\n        )\n      })\n    },\n    wrapAll: function(structure){\n      if (this[0]) {\n        $(this[0]).before(structure = $(structure))\n        var children\n        // drill down to the inmost element\n        while ((children = structure.children()).length) structure = children.first()\n        $(structure).append(this)\n      }\n      return this\n    },\n    wrapInner: function(structure){\n      var func = isFunction(structure)\n      return this.each(function(index){\n        var self = $(this), contents = self.contents(),\n            dom  = func ? structure.call(this, index) : structure\n        contents.length ? contents.wrapAll(dom) : self.append(dom)\n      })\n    },\n    unwrap: function(){\n      this.parent().each(function(){\n        $(this).replaceWith($(this).children())\n      })\n      return this\n    },\n    clone: function(){\n      return this.map(function(){ return this.cloneNode(true) })\n    },\n    hide: function(){\n      return this.css(\"display\", \"none\")\n    },\n    toggle: function(setting){\n      return this.each(function(){\n        var el = $(this)\n        ;(setting === undefined ? el.css(\"display\") == \"none\" : setting) ? el.show() : el.hide()\n      })\n    },\n    prev: function(selector){ return $(this.pluck('previousElementSibling')).filter(selector || '*') },\n    next: function(selector){ return $(this.pluck('nextElementSibling')).filter(selector || '*') },\n    html: function(html){\n      return 0 in arguments ?\n        this.each(function(idx){\n          var originHtml = this.innerHTML\n          $(this).empty().append( funcArg(this, html, idx, originHtml) )\n        }) :\n        (0 in this ? this[0].innerHTML : null)\n    },\n    text: function(text){\n      return 0 in arguments ?\n        this.each(function(idx){\n          var newText = funcArg(this, text, idx, this.textContent)\n          this.textContent = newText == null ? '' : ''+newText\n        }) :\n        (0 in this ? this.pluck('textContent').join(\"\") : null)\n    },\n    attr: function(name, value){\n      var result\n      return (typeof name == 'string' && !(1 in arguments)) ?\n        (0 in this && this[0].nodeType == 1 && (result = this[0].getAttribute(name)) != null ? result : undefined) :\n        this.each(function(idx){\n          if (this.nodeType !== 1) return\n          if (isObject(name)) for (key in name) setAttribute(this, key, name[key])\n          else setAttribute(this, name, funcArg(this, value, idx, this.getAttribute(name)))\n        })\n    },\n    removeAttr: function(name){\n      return this.each(function(){ this.nodeType === 1 && name.split(' ').forEach(function(attribute){\n        setAttribute(this, attribute)\n      }, this)})\n    },\n    prop: function(name, value){\n      name = propMap[name] || name\n      return (1 in arguments) ?\n        this.each(function(idx){\n          this[name] = funcArg(this, value, idx, this[name])\n        }) :\n        (this[0] && this[0][name])\n    },\n    removeProp: function(name){\n      name = propMap[name] || name\n      return this.each(function(){ delete this[name] })\n    },\n    data: function(name, value){\n      var attrName = 'data-' + name.replace(capitalRE, '-$1').toLowerCase()\n\n      var data = (1 in arguments) ?\n        this.attr(attrName, value) :\n        this.attr(attrName)\n\n      return data !== null ? deserializeValue(data) : undefined\n    },\n    val: function(value){\n      if (0 in arguments) {\n        if (value == null) value = \"\"\n        return this.each(function(idx){\n          this.value = funcArg(this, value, idx, this.value)\n        })\n      } else {\n        return this[0] && (this[0].multiple ?\n           $(this[0]).find('option').filter(function(){ return this.selected }).pluck('value') :\n           this[0].value)\n      }\n    },\n    offset: function(coordinates){\n      if (coordinates) return this.each(function(index){\n        var $this = $(this),\n            coords = funcArg(this, coordinates, index, $this.offset()),\n            parentOffset = $this.offsetParent().offset(),\n            props = {\n              top:  coords.top  - parentOffset.top,\n              left: coords.left - parentOffset.left\n            }\n\n        if ($this.css('position') == 'static') props['position'] = 'relative'\n        $this.css(props)\n      })\n      if (!this.length) return null\n      if (document.documentElement !== this[0] && !$.contains(document.documentElement, this[0]))\n        return {top: 0, left: 0}\n      var obj = this[0].getBoundingClientRect()\n      return {\n        left: obj.left + window.pageXOffset,\n        top: obj.top + window.pageYOffset,\n        width: Math.round(obj.width),\n        height: Math.round(obj.height)\n      }\n    },\n    css: function(property, value){\n      if (arguments.length < 2) {\n        var element = this[0]\n        if (typeof property == 'string') {\n          if (!element) return\n          return element.style[camelize(property)] || getComputedStyle(element, '').getPropertyValue(property)\n        } else if (isArray(property)) {\n          if (!element) return\n          var props = {}\n          var computedStyle = getComputedStyle(element, '')\n          $.each(property, function(_, prop){\n            props[prop] = (element.style[camelize(prop)] || computedStyle.getPropertyValue(prop))\n          })\n          return props\n        }\n      }\n\n      var css = ''\n      if (type(property) == 'string') {\n        if (!value && value !== 0)\n          this.each(function(){ this.style.removeProperty(dasherize(property)) })\n        else\n          css = dasherize(property) + \":\" + maybeAddPx(property, value)\n      } else {\n        for (key in property)\n          if (!property[key] && property[key] !== 0)\n            this.each(function(){ this.style.removeProperty(dasherize(key)) })\n          else\n            css += dasherize(key) + ':' + maybeAddPx(key, property[key]) + ';'\n      }\n\n      return this.each(function(){ this.style.cssText += ';' + css })\n    },\n    index: function(element){\n      return element ? this.indexOf($(element)[0]) : this.parent().children().indexOf(this[0])\n    },\n    hasClass: function(name){\n      if (!name) return false\n      return emptyArray.some.call(this, function(el){\n        return this.test(className(el))\n      }, classRE(name))\n    },\n    addClass: function(name){\n      if (!name) return this\n      return this.each(function(idx){\n        if (!('className' in this)) return\n        classList = []\n        var cls = className(this), newName = funcArg(this, name, idx, cls)\n        newName.split(/\\s+/g).forEach(function(klass){\n          if (!$(this).hasClass(klass)) classList.push(klass)\n        }, this)\n        classList.length && className(this, cls + (cls ? \" \" : \"\") + classList.join(\" \"))\n      })\n    },\n    removeClass: function(name){\n      return this.each(function(idx){\n        if (!('className' in this)) return\n        if (name === undefined) return className(this, '')\n        classList = className(this)\n        funcArg(this, name, idx, classList).split(/\\s+/g).forEach(function(klass){\n          classList = classList.replace(classRE(klass), \" \")\n        })\n        className(this, classList.trim())\n      })\n    },\n    toggleClass: function(name, when){\n      if (!name) return this\n      return this.each(function(idx){\n        var $this = $(this), names = funcArg(this, name, idx, className(this))\n        names.split(/\\s+/g).forEach(function(klass){\n          (when === undefined ? !$this.hasClass(klass) : when) ?\n            $this.addClass(klass) : $this.removeClass(klass)\n        })\n      })\n    },\n    scrollTop: function(value){\n      if (!this.length) return\n      var hasScrollTop = 'scrollTop' in this[0]\n      if (value === undefined) return hasScrollTop ? this[0].scrollTop : this[0].pageYOffset\n      return this.each(hasScrollTop ?\n        function(){ this.scrollTop = value } :\n        function(){ this.scrollTo(this.scrollX, value) })\n    },\n    scrollLeft: function(value){\n      if (!this.length) return\n      var hasScrollLeft = 'scrollLeft' in this[0]\n      if (value === undefined) return hasScrollLeft ? this[0].scrollLeft : this[0].pageXOffset\n      return this.each(hasScrollLeft ?\n        function(){ this.scrollLeft = value } :\n        function(){ this.scrollTo(value, this.scrollY) })\n    },\n    position: function() {\n      if (!this.length) return\n\n      var elem = this[0],\n        // Get *real* offsetParent\n        offsetParent = this.offsetParent(),\n        // Get correct offsets\n        offset       = this.offset(),\n        parentOffset = rootNodeRE.test(offsetParent[0].nodeName) ? { top: 0, left: 0 } : offsetParent.offset()\n\n      // Subtract element margins\n      // note: when an element has margin: auto the offsetLeft and marginLeft\n      // are the same in Safari causing offset.left to incorrectly be 0\n      offset.top  -= parseFloat( $(elem).css('margin-top') ) || 0\n      offset.left -= parseFloat( $(elem).css('margin-left') ) || 0\n\n      // Add offsetParent borders\n      parentOffset.top  += parseFloat( $(offsetParent[0]).css('border-top-width') ) || 0\n      parentOffset.left += parseFloat( $(offsetParent[0]).css('border-left-width') ) || 0\n\n      // Subtract the two offsets\n      return {\n        top:  offset.top  - parentOffset.top,\n        left: offset.left - parentOffset.left\n      }\n    },\n    offsetParent: function() {\n      return this.map(function(){\n        var parent = this.offsetParent || document.body\n        while (parent && !rootNodeRE.test(parent.nodeName) && $(parent).css(\"position\") == \"static\")\n          parent = parent.offsetParent\n        return parent\n      })\n    }\n  }\n\n  // for now\n  $.fn.detach = $.fn.remove\n\n  // Generate the `width` and `height` functions\n  ;['width', 'height'].forEach(function(dimension){\n    var dimensionProperty =\n      dimension.replace(/./, function(m){ return m[0].toUpperCase() })\n\n    $.fn[dimension] = function(value){\n      var offset, el = this[0]\n      if (value === undefined) return isWindow(el) ? el['inner' + dimensionProperty] :\n        isDocument(el) ? el.documentElement['scroll' + dimensionProperty] :\n        (offset = this.offset()) && offset[dimension]\n      else return this.each(function(idx){\n        el = $(this)\n        el.css(dimension, funcArg(this, value, idx, el[dimension]()))\n      })\n    }\n  })\n\n  function traverseNode(node, fun) {\n    fun(node)\n    for (var i = 0, len = node.childNodes.length; i < len; i++)\n      traverseNode(node.childNodes[i], fun)\n  }\n\n  // Generate the `after`, `prepend`, `before`, `append`,\n  // `insertAfter`, `insertBefore`, `appendTo`, and `prependTo` methods.\n  adjacencyOperators.forEach(function(operator, operatorIndex) {\n    var inside = operatorIndex % 2 //=> prepend, append\n\n    $.fn[operator] = function(){\n      // arguments can be nodes, arrays of nodes, Zepto objects and HTML strings\n      var argType, nodes = $.map(arguments, function(arg) {\n            var arr = []\n            argType = type(arg)\n            if (argType == \"array\") {\n              arg.forEach(function(el) {\n                if (el.nodeType !== undefined) return arr.push(el)\n                else if ($.zepto.isZ(el)) return arr = arr.concat(el.get())\n                arr = arr.concat(zepto.fragment(el))\n              })\n              return arr\n            }\n            return argType == \"object\" || arg == null ?\n              arg : zepto.fragment(arg)\n          }),\n          parent, copyByClone = this.length > 1\n      if (nodes.length < 1) return this\n\n      return this.each(function(_, target){\n        parent = inside ? target : target.parentNode\n\n        // convert all methods to a \"before\" operation\n        target = operatorIndex == 0 ? target.nextSibling :\n                 operatorIndex == 1 ? target.firstChild :\n                 operatorIndex == 2 ? target :\n                 null\n\n        var parentInDocument = $.contains(document.documentElement, parent)\n\n        nodes.forEach(function(node){\n          if (copyByClone) node = node.cloneNode(true)\n          else if (!parent) return $(node).remove()\n\n          parent.insertBefore(node, target)\n          if (parentInDocument) traverseNode(node, function(el){\n            if (el.nodeName != null && el.nodeName.toUpperCase() === 'SCRIPT' &&\n               (!el.type || el.type === 'text/javascript') && !el.src){\n              var target = el.ownerDocument ? el.ownerDocument.defaultView : window\n              target['eval'].call(target, el.innerHTML)\n            }\n          })\n        })\n      })\n    }\n\n    // after    => insertAfter\n    // prepend  => prependTo\n    // before   => insertBefore\n    // append   => appendTo\n    $.fn[inside ? operator+'To' : 'insert'+(operatorIndex ? 'Before' : 'After')] = function(html){\n      $(html)[operator](this)\n      return this\n    }\n  })\n\n  zepto.Z.prototype = Z.prototype = $.fn\n\n  // Export internal API functions in the `$.zepto` namespace\n  zepto.uniq = uniq\n  zepto.deserializeValue = deserializeValue\n  $.zepto = zepto\n\n  return $\n})()\n\n;(function($){\n  var _zid = 1, undefined,\n      slice = Array.prototype.slice,\n      isFunction = $.isFunction,\n      isString = function(obj){ return typeof obj == 'string' },\n      handlers = {},\n      specialEvents={},\n      focusinSupported = 'onfocusin' in window,\n      focus = { focus: 'focusin', blur: 'focusout' },\n      hover = { mouseenter: 'mouseover', mouseleave: 'mouseout' }\n\n  specialEvents.click = specialEvents.mousedown = specialEvents.mouseup = specialEvents.mousemove = 'MouseEvents'\n\n  function zid(element) {\n    return element._zid || (element._zid = _zid++)\n  }\n  function findHandlers(element, event, fn, selector) {\n    event = parse(event)\n    if (event.ns) var matcher = matcherFor(event.ns)\n    return (handlers[zid(element)] || []).filter(function(handler) {\n      return handler\n        && (!event.e  || handler.e == event.e)\n        && (!event.ns || matcher.test(handler.ns))\n        && (!fn       || zid(handler.fn) === zid(fn))\n        && (!selector || handler.sel == selector)\n    })\n  }\n  function parse(event) {\n    var parts = ('' + event).split('.')\n    return {e: parts[0], ns: parts.slice(1).sort().join(' ')}\n  }\n  function matcherFor(ns) {\n    return new RegExp('(?:^| )' + ns.replace(' ', ' .* ?') + '(?: |$)')\n  }\n\n  function eventCapture(handler, captureSetting) {\n    return handler.del &&\n      (!focusinSupported && (handler.e in focus)) ||\n      !!captureSetting\n  }\n\n  function realEvent(type) {\n    return hover[type] || (focusinSupported && focus[type]) || type\n  }\n\n  function add(element, events, fn, data, selector, delegator, capture){\n    var id = zid(element), set = (handlers[id] || (handlers[id] = []))\n    events.split(/\\s/).forEach(function(event){\n      if (event == 'ready') return $(document).ready(fn)\n      var handler   = parse(event)\n      handler.fn    = fn\n      handler.sel   = selector\n      // emulate mouseenter, mouseleave\n      if (handler.e in hover) fn = function(e){\n        var related = e.relatedTarget\n        if (!related || (related !== this && !$.contains(this, related)))\n          return handler.fn.apply(this, arguments)\n      }\n      handler.del   = delegator\n      var callback  = delegator || fn\n      handler.proxy = function(e){\n        e = compatible(e)\n        if (e.isImmediatePropagationStopped()) return\n        try {\n          var dataPropDescriptor = Object.getOwnPropertyDescriptor(e, 'data')\n          if (!dataPropDescriptor || dataPropDescriptor.writable)\n            e.data = data\n        } catch (e) {} // when using strict mode dataPropDescriptor will be undefined when e is InputEvent (even though data property exists). So we surround with try/catch\n        var result = callback.apply(element, e._args == undefined ? [e] : [e].concat(e._args))\n        if (result === false) e.preventDefault(), e.stopPropagation()\n        return result\n      }\n      handler.i = set.length\n      set.push(handler)\n      if ('addEventListener' in element)\n        element.addEventListener(realEvent(handler.e), handler.proxy, eventCapture(handler, capture))\n    })\n  }\n  function remove(element, events, fn, selector, capture){\n    var id = zid(element)\n    ;(events || '').split(/\\s/).forEach(function(event){\n      findHandlers(element, event, fn, selector).forEach(function(handler){\n        delete handlers[id][handler.i]\n      if ('removeEventListener' in element)\n        element.removeEventListener(realEvent(handler.e), handler.proxy, eventCapture(handler, capture))\n      })\n    })\n  }\n\n  $.event = { add: add, remove: remove }\n\n  $.proxy = function(fn, context) {\n    var args = (2 in arguments) && slice.call(arguments, 2)\n    if (isFunction(fn)) {\n      var proxyFn = function(){ return fn.apply(context, args ? args.concat(slice.call(arguments)) : arguments) }\n      proxyFn._zid = zid(fn)\n      return proxyFn\n    } else if (isString(context)) {\n      if (args) {\n        args.unshift(fn[context], fn)\n        return $.proxy.apply(null, args)\n      } else {\n        return $.proxy(fn[context], fn)\n      }\n    } else {\n      throw new TypeError(\"expected function\")\n    }\n  }\n\n  $.fn.bind = function(event, data, callback){\n    return this.on(event, data, callback)\n  }\n  $.fn.unbind = function(event, callback){\n    return this.off(event, callback)\n  }\n  $.fn.one = function(event, selector, data, callback){\n    return this.on(event, selector, data, callback, 1)\n  }\n\n  var returnTrue = function(){return true},\n      returnFalse = function(){return false},\n      ignoreProperties = /^([A-Z]|returnValue$|layer[XY]$|webkitMovement[XY]$)/,\n      eventMethods = {\n        preventDefault: 'isDefaultPrevented',\n        stopImmediatePropagation: 'isImmediatePropagationStopped',\n        stopPropagation: 'isPropagationStopped'\n      }\n\n  function compatible(event, source) {\n    if (source || !event.isDefaultPrevented) {\n      source || (source = event)\n\n      $.each(eventMethods, function(name, predicate) {\n        var sourceMethod = source[name]\n        event[name] = function(){\n          this[predicate] = returnTrue\n          return sourceMethod && sourceMethod.apply(source, arguments)\n        }\n        event[predicate] = returnFalse\n      })\n\n      try {\n        event.timeStamp || (event.timeStamp = Date.now())\n      } catch (ignored) { }\n\n      if (source.defaultPrevented !== undefined ? source.defaultPrevented :\n          'returnValue' in source ? source.returnValue === false :\n          source.getPreventDefault && source.getPreventDefault())\n        event.isDefaultPrevented = returnTrue\n    }\n    return event\n  }\n\n  function createProxy(event) {\n    var key, proxy = { originalEvent: event }\n    for (key in event)\n      if (!ignoreProperties.test(key) && event[key] !== undefined) proxy[key] = event[key]\n\n    return compatible(proxy, event)\n  }\n\n  $.fn.delegate = function(selector, event, callback){\n    return this.on(event, selector, callback)\n  }\n  $.fn.undelegate = function(selector, event, callback){\n    return this.off(event, selector, callback)\n  }\n\n  $.fn.live = function(event, callback){\n    $(document.body).delegate(this.selector, event, callback)\n    return this\n  }\n  $.fn.die = function(event, callback){\n    $(document.body).undelegate(this.selector, event, callback)\n    return this\n  }\n\n  $.fn.on = function(event, selector, data, callback, one){\n    var autoRemove, delegator, $this = this\n    if (event && !isString(event)) {\n      $.each(event, function(type, fn){\n        $this.on(type, selector, data, fn, one)\n      })\n      return $this\n    }\n\n    if (!isString(selector) && !isFunction(callback) && callback !== false)\n      callback = data, data = selector, selector = undefined\n    if (callback === undefined || data === false)\n      callback = data, data = undefined\n\n    if (callback === false) callback = returnFalse\n\n    return $this.each(function(_, element){\n      if (one) autoRemove = function(e){\n        remove(element, e.type, callback)\n        return callback.apply(this, arguments)\n      }\n\n      if (selector) delegator = function(e){\n        var evt, match = $(e.target).closest(selector, element).get(0)\n        if (match && match !== element) {\n          evt = $.extend(createProxy(e), {currentTarget: match, liveFired: element})\n          return (autoRemove || callback).apply(match, [evt].concat(slice.call(arguments, 1)))\n        }\n      }\n\n      add(element, event, callback, data, selector, delegator || autoRemove)\n    })\n  }\n  $.fn.off = function(event, selector, callback){\n    var $this = this\n    if (event && !isString(event)) {\n      $.each(event, function(type, fn){\n        $this.off(type, selector, fn)\n      })\n      return $this\n    }\n\n    if (!isString(selector) && !isFunction(callback) && callback !== false)\n      callback = selector, selector = undefined\n\n    if (callback === false) callback = returnFalse\n\n    return $this.each(function(){\n      remove(this, event, callback, selector)\n    })\n  }\n\n  $.fn.trigger = function(event, args){\n    event = (isString(event) || $.isPlainObject(event)) ? $.Event(event) : compatible(event)\n    event._args = args\n    return this.each(function(){\n      // handle focus(), blur() by calling them directly\n      if (event.type in focus && typeof this[event.type] == \"function\") this[event.type]()\n      // items in the collection might not be DOM elements\n      else if ('dispatchEvent' in this) this.dispatchEvent(event)\n      else $(this).triggerHandler(event, args)\n    })\n  }\n\n  // triggers event handlers on current element just as if an event occurred,\n  // doesn't trigger an actual event, doesn't bubble\n  $.fn.triggerHandler = function(event, args){\n    var e, result\n    this.each(function(i, element){\n      e = createProxy(isString(event) ? $.Event(event) : event)\n      e._args = args\n      e.target = element\n      $.each(findHandlers(element, event.type || event), function(i, handler){\n        result = handler.proxy(e)\n        if (e.isImmediatePropagationStopped()) return false\n      })\n    })\n    return result\n  }\n\n  // shortcut methods for `.bind(event, fn)` for each event type\n  ;('focusin focusout focus blur load resize scroll unload click dblclick '+\n  'mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave '+\n  'change select keydown keypress keyup error').split(' ').forEach(function(event) {\n    $.fn[event] = function(callback) {\n      return (0 in arguments) ?\n        this.bind(event, callback) :\n        this.trigger(event)\n    }\n  })\n\n  $.Event = function(type, props) {\n    if (!isString(type)) props = type, type = props.type\n    var event = document.createEvent(specialEvents[type] || 'Events'), bubbles = true\n    if (props) for (var name in props) (name == 'bubbles') ? (bubbles = !!props[name]) : (event[name] = props[name])\n    event.initEvent(type, bubbles, true)\n    return compatible(event)\n  }\n\n})(Zepto)\n\n;(function($){\n  var cache = [], timeout\n\n  $.fn.remove = function(){\n    return this.each(function(){\n      if(this.parentNode){\n        if(this.tagName === 'IMG'){\n          cache.push(this)\n          this.src = 'data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs='\n          if (timeout) clearTimeout(timeout)\n          timeout = setTimeout(function(){ cache = [] }, 60000)\n        }\n        this.parentNode.removeChild(this)\n      }\n    })\n  }\n})(Zepto)\n\n;(function($){\n  var data = {}, dataAttr = $.fn.data, camelize = $.camelCase,\n    exp = $.expando = 'Zepto' + (+new Date()), emptyArray = []\n\n  // Get value from node:\n  // 1. first try key as given,\n  // 2. then try camelized key,\n  // 3. fall back to reading \"data-*\" attribute.\n  function getData(node, name) {\n    var id = node[exp], store = id && data[id]\n    if (name === undefined) return store || setData(node)\n    else {\n      if (store) {\n        if (name in store) return store[name]\n        var camelName = camelize(name)\n        if (camelName in store) return store[camelName]\n      }\n      return dataAttr.call($(node), name)\n    }\n  }\n\n  // Store value under camelized key on node\n  function setData(node, name, value) {\n    var id = node[exp] || (node[exp] = ++$.uuid),\n      store = data[id] || (data[id] = attributeData(node))\n    if (name !== undefined) store[camelize(name)] = value\n    return store\n  }\n\n  // Read all \"data-*\" attributes from a node\n  function attributeData(node) {\n    var store = {}\n    $.each(node.attributes || emptyArray, function(i, attr){\n      if (attr.name.indexOf('data-') == 0)\n        store[camelize(attr.name.replace('data-', ''))] =\n          $.zepto.deserializeValue(attr.value)\n    })\n    return store\n  }\n\n  $.fn.data = function(name, value) {\n    return value === undefined ?\n      // set multiple values via object\n      $.isPlainObject(name) ?\n        this.each(function(i, node){\n          $.each(name, function(key, value){ setData(node, key, value) })\n        }) :\n        // get value from first element\n        (0 in this ? getData(this[0], name) : undefined) :\n      // set value on all elements\n      this.each(function(){ setData(this, name, value) })\n  }\n\n  $.data = function(elem, name, value) {\n    return $(elem).data(name, value)\n  }\n\n  $.hasData = function(elem) {\n    var id = elem[exp], store = id && data[id]\n    return store ? !$.isEmptyObject(store) : false\n  }\n\n  $.fn.removeData = function(names) {\n    if (typeof names == 'string') names = names.split(/\\s+/)\n    return this.each(function(){\n      var id = this[exp], store = id && data[id]\n      if (store) $.each(names || store, function(key){\n        delete store[names ? camelize(this) : key]\n      })\n    })\n  }\n\n  // Generate extended `remove` and `empty` functions\n  ;['remove', 'empty'].forEach(function(methodName){\n    var origFn = $.fn[methodName]\n    $.fn[methodName] = function() {\n      var elements = this.find('*')\n      if (methodName === 'remove') elements = elements.add(this)\n      elements.removeData()\n      return origFn.call(this)\n    }\n  })\n})(Zepto)\n  return Zepto\n}))\n", "'use strict';\n\nvar attrsKey = 'aaAttrs';\n\nvar _ = require('../common/utils.js');\nvar DOM = require('../common/dom.js');\nvar EventBus = require('./event_bus.js');\nvar Input = require('./input.js');\nvar Dropdown = require('./dropdown.js');\nvar html = require('./html.js');\nvar css = require('./css.js');\n\n// constructor\n// -----------\n\n// THOUGHT: what if datasets could dynamically be added/removed?\nfunction Typeahead(o) {\n  var $menu;\n  var $hint;\n\n  o = o || {};\n\n  if (!o.input) {\n    _.error('missing input');\n  }\n\n  this.isActivated = false;\n  this.debug = !!o.debug;\n  this.autoselect = !!o.autoselect;\n  this.autoselectOnBlur = !!o.autoselectOnBlur;\n  this.openOnFocus = !!o.openOnFocus;\n  this.minLength = _.isNumber(o.minLength) ? o.minLength : 1;\n  this.autoWidth = (o.autoWidth === undefined) ? true : !!o.autoWidth;\n  this.clearOnSelected = !!o.clearOnSelected;\n  this.tabAutocomplete = (o.tabAutocomplete === undefined) ? true : !!o.tabAutocomplete;\n\n  o.hint = !!o.hint;\n\n  if (o.hint && o.appendTo) {\n    throw new Error('[autocomplete.js] hint and appendTo options can\\'t be used at the same time');\n  }\n\n  this.css = o.css = _.mixin({}, css, o.appendTo ? css.appendTo : {});\n  this.cssClasses = o.cssClasses = _.mixin({}, css.defaultClasses, o.cssClasses || {});\n  this.cssClasses.prefix =\n    o.cssClasses.formattedPrefix = _.formatPrefix(this.cssClasses.prefix, this.cssClasses.noPrefix);\n  this.listboxId = o.listboxId = [this.cssClasses.root, 'listbox', _.getUniqueId()].join('-');\n\n  var domElts = buildDom(o);\n\n  this.$node = domElts.wrapper;\n  var $input = this.$input = domElts.input;\n  $menu = domElts.menu;\n  $hint = domElts.hint;\n\n  if (o.dropdownMenuContainer) {\n    DOM.element(o.dropdownMenuContainer)\n      .css('position', 'relative') // ensure the container has a relative position\n      .append($menu.css('top', '0')); // override the top: 100%\n  }\n\n  // #705: if there's scrollable overflow, ie doesn't support\n  // blur cancellations when the scrollbar is clicked\n  //\n  // #351: preventDefault won't cancel blurs in ie <= 8\n  $input.on('blur.aa', function($e) {\n    var active = document.activeElement;\n    if (_.isMsie() && ($menu[0] === active || $menu[0].contains(active))) {\n      $e.preventDefault();\n      // stop immediate in order to prevent Input#_onBlur from\n      // getting exectued\n      $e.stopImmediatePropagation();\n      _.defer(function() { $input.focus(); });\n    }\n  });\n\n  // #351: prevents input blur due to clicks within dropdown menu\n  $menu.on('mousedown.aa', function($e) { $e.preventDefault(); });\n\n  this.eventBus = o.eventBus || new EventBus({el: $input});\n\n  this.dropdown = new Typeahead.Dropdown({\n    appendTo: o.appendTo,\n    wrapper: this.$node,\n    menu: $menu,\n    datasets: o.datasets,\n    templates: o.templates,\n    cssClasses: o.cssClasses,\n    minLength: this.minLength\n  })\n    .onSync('suggestionClicked', this._onSuggestionClicked, this)\n    .onSync('cursorMoved', this._onCursorMoved, this)\n    .onSync('cursorRemoved', this._onCursorRemoved, this)\n    .onSync('opened', this._onOpened, this)\n    .onSync('closed', this._onClosed, this)\n    .onSync('shown', this._onShown, this)\n    .onSync('empty', this._onEmpty, this)\n    .onSync('redrawn', this._onRedrawn, this)\n    .onAsync('datasetRendered', this._onDatasetRendered, this);\n\n  this.input = new Typeahead.Input({input: $input, hint: $hint})\n    .onSync('focused', this._onFocused, this)\n    .onSync('blurred', this._onBlurred, this)\n    .onSync('enterKeyed', this._onEnterKeyed, this)\n    .onSync('tabKeyed', this._onTabKeyed, this)\n    .onSync('escKeyed', this._onEscKeyed, this)\n    .onSync('upKeyed', this._onUpKeyed, this)\n    .onSync('downKeyed', this._onDownKeyed, this)\n    .onSync('leftKeyed', this._onLeftKeyed, this)\n    .onSync('rightKeyed', this._onRightKeyed, this)\n    .onSync('queryChanged', this._onQueryChanged, this)\n    .onSync('whitespaceChanged', this._onWhitespaceChanged, this);\n\n  this._bindKeyboardShortcuts(o);\n\n  this._setLanguageDirection();\n}\n\n// instance methods\n// ----------------\n\n_.mixin(Typeahead.prototype, {\n  // ### private\n\n  _bindKeyboardShortcuts: function(options) {\n    if (!options.keyboardShortcuts) {\n      return;\n    }\n    var $input = this.$input;\n    var keyboardShortcuts = [];\n    _.each(options.keyboardShortcuts, function(key) {\n      if (typeof key === 'string') {\n        key = key.toUpperCase().charCodeAt(0);\n      }\n      keyboardShortcuts.push(key);\n    });\n    DOM.element(document).keydown(function(event) {\n      var elt = (event.target || event.srcElement);\n      var tagName = elt.tagName;\n      if (elt.isContentEditable || tagName === 'INPUT' || tagName === 'SELECT' || tagName === 'TEXTAREA') {\n        // already in an input\n        return;\n      }\n\n      var which = event.which || event.keyCode;\n      if (keyboardShortcuts.indexOf(which) === -1) {\n        // not the right shortcut\n        return;\n      }\n\n      $input.focus();\n      event.stopPropagation();\n      event.preventDefault();\n    });\n  },\n\n  _onSuggestionClicked: function onSuggestionClicked(type, $el) {\n    var datum;\n    var context = {selectionMethod: 'click'};\n\n    if (datum = this.dropdown.getDatumForSuggestion($el)) {\n      this._select(datum, context);\n    }\n  },\n\n  _onCursorMoved: function onCursorMoved(event, updateInput) {\n    var datum = this.dropdown.getDatumForCursor();\n    var currentCursorId = this.dropdown.getCurrentCursor().attr('id');\n    this.input.setActiveDescendant(currentCursorId);\n\n    if (datum) {\n      if (updateInput) {\n        this.input.setInputValue(datum.value, true);\n      }\n\n      this.eventBus.trigger('cursorchanged', datum.raw, datum.datasetName);\n    }\n  },\n\n  _onCursorRemoved: function onCursorRemoved() {\n    this.input.resetInputValue();\n    this._updateHint();\n    this.eventBus.trigger('cursorremoved');\n  },\n\n  _onDatasetRendered: function onDatasetRendered() {\n    this._updateHint();\n\n    this.eventBus.trigger('updated');\n  },\n\n  _onOpened: function onOpened() {\n    this._updateHint();\n    this.input.expand();\n\n    this.eventBus.trigger('opened');\n  },\n\n  _onEmpty: function onEmpty() {\n    this.eventBus.trigger('empty');\n  },\n\n  _onRedrawn: function onRedrawn() {\n    this.$node.css('top', 0 + 'px');\n    this.$node.css('left', 0 + 'px');\n\n    var inputRect = this.$input[0].getBoundingClientRect();\n\n    if (this.autoWidth) {\n      this.$node.css('width', inputRect.width + 'px');\n    }\n\n    var wrapperRect = this.$node[0].getBoundingClientRect();\n\n    var top = inputRect.bottom - wrapperRect.top;\n    this.$node.css('top', top + 'px');\n    var left = inputRect.left - wrapperRect.left;\n    this.$node.css('left', left + 'px');\n\n    this.eventBus.trigger('redrawn');\n  },\n\n  _onShown: function onShown() {\n    this.eventBus.trigger('shown');\n    if (this.autoselect) {\n      this.dropdown.cursorTopSuggestion();\n    }\n  },\n\n  _onClosed: function onClosed() {\n    this.input.clearHint();\n    this.input.removeActiveDescendant();\n    this.input.collapse();\n\n    this.eventBus.trigger('closed');\n  },\n\n  _onFocused: function onFocused() {\n    this.isActivated = true;\n\n    if (this.openOnFocus) {\n      var query = this.input.getQuery();\n      if (query.length >= this.minLength) {\n        this.dropdown.update(query);\n      } else {\n        this.dropdown.empty();\n      }\n\n      this.dropdown.open();\n    }\n  },\n\n  _onBlurred: function onBlurred() {\n    var cursorDatum;\n    var topSuggestionDatum;\n\n    cursorDatum = this.dropdown.getDatumForCursor();\n    topSuggestionDatum = this.dropdown.getDatumForTopSuggestion();\n    var context = {selectionMethod: 'blur'};\n\n    if (!this.debug) {\n      if (this.autoselectOnBlur && cursorDatum) {\n        this._select(cursorDatum, context);\n      } else if (this.autoselectOnBlur && topSuggestionDatum) {\n        this._select(topSuggestionDatum, context);\n      } else {\n        this.isActivated = false;\n        this.dropdown.empty();\n        this.dropdown.close();\n      }\n    }\n  },\n\n  _onEnterKeyed: function onEnterKeyed(type, $e) {\n    var cursorDatum;\n    var topSuggestionDatum;\n\n    cursorDatum = this.dropdown.getDatumForCursor();\n    topSuggestionDatum = this.dropdown.getDatumForTopSuggestion();\n    var context = {selectionMethod: 'enterKey'};\n\n    if (cursorDatum) {\n      this._select(cursorDatum, context);\n      $e.preventDefault();\n    } else if (this.autoselect && topSuggestionDatum) {\n      this._select(topSuggestionDatum, context);\n      $e.preventDefault();\n    }\n  },\n\n  _onTabKeyed: function onTabKeyed(type, $e) {\n    if (!this.tabAutocomplete) {\n      // Closing the dropdown enables further tabbing\n      this.dropdown.close();\n      return;\n    }\n\n    var datum;\n    var context = {selectionMethod: 'tabKey'};\n\n    if (datum = this.dropdown.getDatumForCursor()) {\n      this._select(datum, context);\n      $e.preventDefault();\n    } else {\n      this._autocomplete(true);\n    }\n  },\n\n  _onEscKeyed: function onEscKeyed() {\n    this.dropdown.close();\n    this.input.resetInputValue();\n  },\n\n  _onUpKeyed: function onUpKeyed() {\n    var query = this.input.getQuery();\n\n    if (this.dropdown.isEmpty && query.length >= this.minLength) {\n      this.dropdown.update(query);\n    } else {\n      this.dropdown.moveCursorUp();\n    }\n\n    this.dropdown.open();\n  },\n\n  _onDownKeyed: function onDownKeyed() {\n    var query = this.input.getQuery();\n\n    if (this.dropdown.isEmpty && query.length >= this.minLength) {\n      this.dropdown.update(query);\n    } else {\n      this.dropdown.moveCursorDown();\n    }\n\n    this.dropdown.open();\n  },\n\n  _onLeftKeyed: function onLeftKeyed() {\n    if (this.dir === 'rtl') {\n      this._autocomplete();\n    }\n  },\n\n  _onRightKeyed: function onRightKeyed() {\n    if (this.dir === 'ltr') {\n      this._autocomplete();\n    }\n  },\n\n  _onQueryChanged: function onQueryChanged(e, query) {\n    this.input.clearHintIfInvalid();\n\n    if (query.length >= this.minLength) {\n      this.dropdown.update(query);\n    } else {\n      this.dropdown.empty();\n    }\n\n    this.dropdown.open();\n    this._setLanguageDirection();\n  },\n\n  _onWhitespaceChanged: function onWhitespaceChanged() {\n    this._updateHint();\n    this.dropdown.open();\n  },\n\n  _setLanguageDirection: function setLanguageDirection() {\n    var dir = this.input.getLanguageDirection();\n\n    if (this.dir !== dir) {\n      this.dir = dir;\n      this.$node.css('direction', dir);\n      this.dropdown.setLanguageDirection(dir);\n    }\n  },\n\n  _updateHint: function updateHint() {\n    var datum;\n    var val;\n    var query;\n    var escapedQuery;\n    var frontMatchRegEx;\n    var match;\n\n    datum = this.dropdown.getDatumForTopSuggestion();\n\n    if (datum && this.dropdown.isVisible() && !this.input.hasOverflow()) {\n      val = this.input.getInputValue();\n      query = Input.normalizeQuery(val);\n      escapedQuery = _.escapeRegExChars(query);\n\n      // match input value, then capture trailing text\n      frontMatchRegEx = new RegExp('^(?:' + escapedQuery + ')(.+$)', 'i');\n      match = frontMatchRegEx.exec(datum.value);\n\n      // clear hint if there's no trailing text\n      if (match) {\n        this.input.setHint(val + match[1]);\n      } else {\n        this.input.clearHint();\n      }\n    } else {\n      this.input.clearHint();\n    }\n  },\n\n  _autocomplete: function autocomplete(laxCursor) {\n    var hint;\n    var query;\n    var isCursorAtEnd;\n    var datum;\n\n    hint = this.input.getHint();\n    query = this.input.getQuery();\n    isCursorAtEnd = laxCursor || this.input.isCursorAtEnd();\n\n    if (hint && query !== hint && isCursorAtEnd) {\n      datum = this.dropdown.getDatumForTopSuggestion();\n      if (datum) {\n        this.input.setInputValue(datum.value);\n      }\n\n      this.eventBus.trigger('autocompleted', datum.raw, datum.datasetName);\n    }\n  },\n\n  _select: function select(datum, context) {\n    if (typeof datum.value !== 'undefined') {\n      this.input.setQuery(datum.value);\n    }\n    if (this.clearOnSelected) {\n      this.setVal('');\n    } else {\n      this.input.setInputValue(datum.value, true);\n    }\n\n    this._setLanguageDirection();\n\n    var event = this.eventBus.trigger('selected', datum.raw, datum.datasetName, context);\n    if (event.isDefaultPrevented() === false) {\n      this.dropdown.close();\n\n      // #118: allow click event to bubble up to the body before removing\n      // the suggestions otherwise we break event delegation\n      _.defer(_.bind(this.dropdown.empty, this.dropdown));\n    }\n  },\n\n  // ### public\n\n  open: function open() {\n    // if the menu is not activated yet, we need to update\n    // the underlying dropdown menu to trigger the search\n    // otherwise we're not gonna see anything\n    if (!this.isActivated) {\n      var query = this.input.getInputValue();\n      if (query.length >= this.minLength) {\n        this.dropdown.update(query);\n      } else {\n        this.dropdown.empty();\n      }\n    }\n    this.dropdown.open();\n  },\n\n  close: function close() {\n    this.dropdown.close();\n  },\n\n  setVal: function setVal(val) {\n    // expect val to be a string, so be safe, and coerce\n    val = _.toStr(val);\n\n    if (this.isActivated) {\n      this.input.setInputValue(val);\n    } else {\n      this.input.setQuery(val);\n      this.input.setInputValue(val, true);\n    }\n\n    this._setLanguageDirection();\n  },\n\n  getVal: function getVal() {\n    return this.input.getQuery();\n  },\n\n  destroy: function destroy() {\n    this.input.destroy();\n    this.dropdown.destroy();\n\n    destroyDomStructure(this.$node, this.cssClasses);\n\n    this.$node = null;\n  },\n\n  getWrapper: function getWrapper() {\n    return this.dropdown.$container[0];\n  }\n});\n\nfunction buildDom(options) {\n  var $input;\n  var $wrapper;\n  var $dropdown;\n  var $hint;\n\n  $input = DOM.element(options.input);\n  $wrapper = DOM\n    .element(html.wrapper.replace('%ROOT%', options.cssClasses.root))\n    .css(options.css.wrapper);\n\n  // override the display property with the table-cell value\n  // if the parent element is a table and the original input was a block\n  //  -> https://github.com/algolia/autocomplete.js/issues/16\n  if (!options.appendTo && $input.css('display') === 'block' && $input.parent().css('display') === 'table') {\n    $wrapper.css('display', 'table-cell');\n  }\n  var dropdownHtml = html.dropdown.\n    replace('%PREFIX%', options.cssClasses.prefix).\n    replace('%DROPDOWN_MENU%', options.cssClasses.dropdownMenu);\n  $dropdown = DOM.element(dropdownHtml)\n    .css(options.css.dropdown)\n    .attr({\n      role: 'listbox',\n      id: options.listboxId\n    });\n  if (options.templates && options.templates.dropdownMenu) {\n    $dropdown.html(_.templatify(options.templates.dropdownMenu)());\n  }\n  $hint = $input.clone().css(options.css.hint).css(getBackgroundStyles($input));\n\n  $hint\n    .val('')\n    .addClass(_.className(options.cssClasses.prefix, options.cssClasses.hint, true))\n    .removeAttr('id name placeholder required')\n    .prop('readonly', true)\n    .attr({\n      'aria-hidden': 'true',\n      autocomplete: 'off',\n      spellcheck: 'false',\n      tabindex: -1\n    });\n  if ($hint.removeData) {\n    $hint.removeData();\n  }\n\n  // store the original values of the attrs that get modified\n  // so modifications can be reverted on destroy\n  $input.data(attrsKey, {\n    'aria-autocomplete': $input.attr('aria-autocomplete'),\n    'aria-expanded': $input.attr('aria-expanded'),\n    'aria-owns': $input.attr('aria-owns'),\n    autocomplete: $input.attr('autocomplete'),\n    dir: $input.attr('dir'),\n    role: $input.attr('role'),\n    spellcheck: $input.attr('spellcheck'),\n    style: $input.attr('style'),\n    type: $input.attr('type')\n  });\n\n  $input\n    .addClass(_.className(options.cssClasses.prefix, options.cssClasses.input, true))\n    .attr({\n      autocomplete: 'off',\n      spellcheck: false,\n\n      // Accessibility features\n      // Give the field a presentation of a \"select\".\n      // Combobox is the combined presentation of a single line textfield\n      // with a listbox popup.\n      // https://www.w3.org/WAI/PF/aria/roles#combobox\n      role: 'combobox',\n      // Let the screen reader know the field has an autocomplete\n      // feature to it.\n      'aria-autocomplete': (options.datasets &&\n        options.datasets[0] && options.datasets[0].displayKey ? 'both' : 'list'),\n      // Indicates whether the dropdown it controls is currently expanded or collapsed\n      'aria-expanded': 'false',\n      'aria-label': options.ariaLabel,\n      // Explicitly point to the listbox,\n      // which is a list of suggestions (aka options)\n      'aria-owns': options.listboxId\n    })\n    .css(options.hint ? options.css.input : options.css.inputWithNoHint);\n\n  // ie7 does not like it when dir is set to auto\n  try {\n    if (!$input.attr('dir')) {\n      $input.attr('dir', 'auto');\n    }\n  } catch (e) {\n    // ignore\n  }\n\n  $wrapper = options.appendTo\n    ? $wrapper.appendTo(DOM.element(options.appendTo).eq(0)).eq(0)\n    : $input.wrap($wrapper).parent();\n\n  $wrapper\n    .prepend(options.hint ? $hint : null)\n    .append($dropdown);\n\n  return {\n    wrapper: $wrapper,\n    input: $input,\n    hint: $hint,\n    menu: $dropdown\n  };\n}\n\nfunction getBackgroundStyles($el) {\n  return {\n    backgroundAttachment: $el.css('background-attachment'),\n    backgroundClip: $el.css('background-clip'),\n    backgroundColor: $el.css('background-color'),\n    backgroundImage: $el.css('background-image'),\n    backgroundOrigin: $el.css('background-origin'),\n    backgroundPosition: $el.css('background-position'),\n    backgroundRepeat: $el.css('background-repeat'),\n    backgroundSize: $el.css('background-size')\n  };\n}\n\nfunction destroyDomStructure($node, cssClasses) {\n  var $input = $node.find(_.className(cssClasses.prefix, cssClasses.input));\n\n  // need to remove attrs that weren't previously defined and\n  // revert attrs that originally had a value\n  _.each($input.data(attrsKey), function(val, key) {\n    if (val === undefined) {\n      $input.removeAttr(key);\n    } else {\n      $input.attr(key, val);\n    }\n  });\n\n  $input\n    .detach()\n    .removeClass(_.className(cssClasses.prefix, cssClasses.input, true))\n    .insertAfter($node);\n  if ($input.removeData) {\n    $input.removeData(attrsKey);\n  }\n\n  $node.remove();\n}\n\nTypeahead.Dropdown = Dropdown;\nTypeahead.Input = Input;\nTypeahead.sources = require('../sources/index.js');\n\nmodule.exports = Typeahead;\n", "'use strict';\n\nvar specialKeyCodeMap;\n\nspecialKeyCodeMap = {\n  9: 'tab',\n  27: 'esc',\n  37: 'left',\n  39: 'right',\n  13: 'enter',\n  38: 'up',\n  40: 'down'\n};\n\nvar _ = require('../common/utils.js');\nvar DOM = require('../common/dom.js');\nvar EventEmitter = require('./event_emitter.js');\n\n// constructor\n// -----------\n\nfunction Input(o) {\n  var that = this;\n  var onBlur;\n  var onFocus;\n  var onKeydown;\n  var onInput;\n\n  o = o || {};\n\n  if (!o.input) {\n    _.error('input is missing');\n  }\n\n  // bound functions\n  onBlur = _.bind(this._onBlur, this);\n  onFocus = _.bind(this._onFocus, this);\n  onKeydown = _.bind(this._onKeydown, this);\n  onInput = _.bind(this._onInput, this);\n\n  this.$hint = DOM.element(o.hint);\n  this.$input = DOM.element(o.input)\n    .on('blur.aa', onBlur)\n    .on('focus.aa', onFocus)\n    .on('keydown.aa', onKeydown);\n\n  // if no hint, noop all the hint related functions\n  if (this.$hint.length === 0) {\n    this.setHint = this.getHint = this.clearHint = this.clearHintIfInvalid = _.noop;\n  }\n\n  // ie7 and ie8 don't support the input event\n  // ie9 doesn't fire the input event when characters are removed\n  // not sure if ie10 is compatible\n  if (!_.isMsie()) {\n    this.$input.on('input.aa', onInput);\n  } else {\n    this.$input.on('keydown.aa keypress.aa cut.aa paste.aa', function($e) {\n      // if a special key triggered this, ignore it\n      if (specialKeyCodeMap[$e.which || $e.keyCode]) {\n        return;\n      }\n\n      // give the browser a chance to update the value of the input\n      // before checking to see if the query changed\n      _.defer(_.bind(that._onInput, that, $e));\n    });\n  }\n\n  // the query defaults to whatever the value of the input is\n  // on initialization, it'll most likely be an empty string\n  this.query = this.$input.val();\n\n  // helps with calculating the width of the input's value\n  this.$overflowHelper = buildOverflowHelper(this.$input);\n}\n\n// static methods\n// --------------\n\nInput.normalizeQuery = function(str) {\n  // strips leading whitespace and condenses all whitespace\n  return (str || '').replace(/^\\s*/g, '').replace(/\\s{2,}/g, ' ');\n};\n\n// instance methods\n// ----------------\n\n_.mixin(Input.prototype, EventEmitter, {\n\n  // ### private\n\n  _onBlur: function onBlur() {\n    this.resetInputValue();\n    this.$input.removeAttr('aria-activedescendant');\n    this.trigger('blurred');\n  },\n\n  _onFocus: function onFocus() {\n    this.trigger('focused');\n  },\n\n  _onKeydown: function onKeydown($e) {\n    // which is normalized and consistent (but not for ie)\n    var keyName = specialKeyCodeMap[$e.which || $e.keyCode];\n\n    this._managePreventDefault(keyName, $e);\n    if (keyName && this._shouldTrigger(keyName, $e)) {\n      this.trigger(keyName + 'Keyed', $e);\n    }\n  },\n\n  _onInput: function onInput() {\n    this._checkInputValue();\n  },\n\n  _managePreventDefault: function managePreventDefault(keyName, $e) {\n    var preventDefault;\n    var hintValue;\n    var inputValue;\n\n    switch (keyName) {\n    case 'tab':\n      hintValue = this.getHint();\n      inputValue = this.getInputValue();\n\n      preventDefault = hintValue &&\n        hintValue !== inputValue &&\n        !withModifier($e);\n      break;\n\n    case 'up':\n    case 'down':\n      preventDefault = !withModifier($e);\n      break;\n\n    default:\n      preventDefault = false;\n    }\n\n    if (preventDefault) {\n      $e.preventDefault();\n    }\n  },\n\n  _shouldTrigger: function shouldTrigger(keyName, $e) {\n    var trigger;\n\n    switch (keyName) {\n    case 'tab':\n      trigger = !withModifier($e);\n      break;\n\n    default:\n      trigger = true;\n    }\n\n    return trigger;\n  },\n\n  _checkInputValue: function checkInputValue() {\n    var inputValue;\n    var areEquivalent;\n    var hasDifferentWhitespace;\n\n    inputValue = this.getInputValue();\n    areEquivalent = areQueriesEquivalent(inputValue, this.query);\n    hasDifferentWhitespace = areEquivalent && this.query ?\n      this.query.length !== inputValue.length : false;\n\n    this.query = inputValue;\n\n    if (!areEquivalent) {\n      this.trigger('queryChanged', this.query);\n    } else if (hasDifferentWhitespace) {\n      this.trigger('whitespaceChanged', this.query);\n    }\n  },\n\n  // ### public\n\n  focus: function focus() {\n    this.$input.focus();\n  },\n\n  blur: function blur() {\n    this.$input.blur();\n  },\n\n  getQuery: function getQuery() {\n    return this.query;\n  },\n\n  setQuery: function setQuery(query) {\n    this.query = query;\n  },\n\n  getInputValue: function getInputValue() {\n    return this.$input.val();\n  },\n\n  setInputValue: function setInputValue(value, silent) {\n    if (typeof value === 'undefined') {\n      value = this.query;\n    }\n    this.$input.val(value);\n\n    // silent prevents any additional events from being triggered\n    if (silent) {\n      this.clearHint();\n    } else {\n      this._checkInputValue();\n    }\n  },\n\n  expand: function expand() {\n    this.$input.attr('aria-expanded', 'true');\n  },\n\n  collapse: function collapse() {\n    this.$input.attr('aria-expanded', 'false');\n  },\n\n  setActiveDescendant: function setActiveDescendant(activedescendantId) {\n    this.$input.attr('aria-activedescendant', activedescendantId);\n  },\n\n  removeActiveDescendant: function removeActiveDescendant() {\n    this.$input.removeAttr('aria-activedescendant');\n  },\n\n  resetInputValue: function resetInputValue() {\n    this.setInputValue(this.query, true);\n  },\n\n  getHint: function getHint() {\n    return this.$hint.val();\n  },\n\n  setHint: function setHint(value) {\n    this.$hint.val(value);\n  },\n\n  clearHint: function clearHint() {\n    this.setHint('');\n  },\n\n  clearHintIfInvalid: function clearHintIfInvalid() {\n    var val;\n    var hint;\n    var valIsPrefixOfHint;\n    var isValid;\n\n    val = this.getInputValue();\n    hint = this.getHint();\n    valIsPrefixOfHint = val !== hint && hint.indexOf(val) === 0;\n    isValid = val !== '' && valIsPrefixOfHint && !this.hasOverflow();\n\n    if (!isValid) {\n      this.clearHint();\n    }\n  },\n\n  getLanguageDirection: function getLanguageDirection() {\n    return (this.$input.css('direction') || 'ltr').toLowerCase();\n  },\n\n  hasOverflow: function hasOverflow() {\n    // 2 is arbitrary, just picking a small number to handle edge cases\n    var constraint = this.$input.width() - 2;\n\n    this.$overflowHelper.text(this.getInputValue());\n\n    return this.$overflowHelper.width() >= constraint;\n  },\n\n  isCursorAtEnd: function() {\n    var valueLength;\n    var selectionStart;\n    var range;\n\n    valueLength = this.$input.val().length;\n    selectionStart = this.$input[0].selectionStart;\n\n    if (_.isNumber(selectionStart)) {\n      return selectionStart === valueLength;\n    } else if (document.selection) {\n      // NOTE: this won't work unless the input has focus, the good news\n      // is this code should only get called when the input has focus\n      range = document.selection.createRange();\n      range.moveStart('character', -valueLength);\n\n      return valueLength === range.text.length;\n    }\n\n    return true;\n  },\n\n  destroy: function destroy() {\n    this.$hint.off('.aa');\n    this.$input.off('.aa');\n\n    this.$hint = this.$input = this.$overflowHelper = null;\n  }\n});\n\n// helper functions\n// ----------------\n\nfunction buildOverflowHelper($input) {\n  return DOM.element('<pre aria-hidden=\"true\"></pre>')\n    .css({\n      // position helper off-screen\n      position: 'absolute',\n      visibility: 'hidden',\n      // avoid line breaks and whitespace collapsing\n      whiteSpace: 'pre',\n      // use same font css as input to calculate accurate width\n      fontFamily: $input.css('font-family'),\n      fontSize: $input.css('font-size'),\n      fontStyle: $input.css('font-style'),\n      fontVariant: $input.css('font-variant'),\n      fontWeight: $input.css('font-weight'),\n      wordSpacing: $input.css('word-spacing'),\n      letterSpacing: $input.css('letter-spacing'),\n      textIndent: $input.css('text-indent'),\n      textRendering: $input.css('text-rendering'),\n      textTransform: $input.css('text-transform')\n    })\n    .insertAfter($input);\n}\n\nfunction areQueriesEquivalent(a, b) {\n  return Input.normalizeQuery(a) === Input.normalizeQuery(b);\n}\n\nfunction withModifier($e) {\n  return $e.altKey || $e.ctrlKey || $e.metaKey || $e.shiftKey;\n}\n\nmodule.exports = Input;\n", "'use strict';\nvar types = [\n  require('./nextTick'),\n  require('./mutation.js'),\n  require('./messageChannel'),\n  require('./stateChange'),\n  require('./timeout')\n];\nvar draining;\nvar currentQueue;\nvar queueIndex = -1;\nvar queue = [];\nvar scheduled = false;\nfunction cleanUpNextTick() {\n  if (!draining || !currentQueue) {\n    return;\n  }\n  draining = false;\n  if (currentQueue.length) {\n    queue = currentQueue.concat(queue);\n  } else {\n    queueIndex = -1;\n  }\n  if (queue.length) {\n    nextTick();\n  }\n}\n\n//named nextTick for less confusing stack traces\nfunction nextTick() {\n  if (draining) {\n    return;\n  }\n  scheduled = false;\n  draining = true;\n  var len = queue.length;\n  var timeout = setTimeout(cleanUpNextTick);\n  while (len) {\n    currentQueue = queue;\n    queue = [];\n    while (currentQueue && ++queueIndex < len) {\n      currentQueue[queueIndex].run();\n    }\n    queueIndex = -1;\n    len = queue.length;\n  }\n  currentQueue = null;\n  queueIndex = -1;\n  draining = false;\n  clearTimeout(timeout);\n}\nvar scheduleDrain;\nvar i = -1;\nvar len = types.length;\nwhile (++i < len) {\n  if (types[i] && types[i].test && types[i].test()) {\n    scheduleDrain = types[i].install(nextTick);\n    break;\n  }\n}\n// v8 likes predictible objects\nfunction Item(fun, array) {\n  this.fun = fun;\n  this.array = array;\n}\nItem.prototype.run = function () {\n  var fun = this.fun;\n  var array = this.array;\n  switch (array.length) {\n  case 0:\n    return fun();\n  case 1:\n    return fun(array[0]);\n  case 2:\n    return fun(array[0], array[1]);\n  case 3:\n    return fun(array[0], array[1], array[2]);\n  default:\n    return fun.apply(null, array);\n  }\n\n};\nmodule.exports = immediate;\nfunction immediate(task) {\n  var args = new Array(arguments.length - 1);\n  if (arguments.length > 1) {\n    for (var i = 1; i < arguments.length; i++) {\n      args[i - 1] = arguments[i];\n    }\n  }\n  queue.push(new Item(task, args));\n  if (!scheduled && !draining) {\n    scheduled = true;\n    scheduleDrain();\n  }\n}\n", "'use strict';\nexports.test = function () {\n  // Don't get fooled by e.g. browserify environments.\n  return (typeof process !== 'undefined') && !process.browser;\n};\n\nexports.install = function (func) {\n  return function () {\n    process.nextTick(func);\n  };\n};\n", "'use strict';\n//based off rsvp https://github.com/tildeio/rsvp.js\n//license https://github.com/tildeio/rsvp.js/blob/master/LICENSE\n//https://github.com/tildeio/rsvp.js/blob/master/lib/rsvp/asap.js\n\nvar Mutation = global.MutationObserver || global.WebKitMutationObserver;\n\nexports.test = function () {\n  return Mutation;\n};\n\nexports.install = function (handle) {\n  var called = 0;\n  var observer = new Mutation(handle);\n  var element = global.document.createTextNode('');\n  observer.observe(element, {\n    characterData: true\n  });\n  return function () {\n    element.data = (called = ++called % 2);\n  };\n};", "'use strict';\n\nexports.test = function () {\n  if (global.setImmediate) {\n    // we can only get here in IE10\n    // which doesn't handel postMessage well\n    return false;\n  }\n  return typeof global.MessageChannel !== 'undefined';\n};\n\nexports.install = function (func) {\n  var channel = new global.MessageChannel();\n  channel.port1.onmessage = func;\n  return function () {\n    channel.port2.postMessage(0);\n  };\n};", "'use strict';\n\nexports.test = function () {\n  return 'document' in global && 'onreadystatechange' in global.document.createElement('script');\n};\n\nexports.install = function (handle) {\n  return function () {\n\n    // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n    // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n    var scriptEl = global.document.createElement('script');\n    scriptEl.onreadystatechange = function () {\n      handle();\n\n      scriptEl.onreadystatechange = null;\n      scriptEl.parentNode.removeChild(scriptEl);\n      scriptEl = null;\n    };\n    global.document.documentElement.appendChild(scriptEl);\n\n    return handle;\n  };\n};", "'use strict';\nexports.test = function () {\n  return true;\n};\n\nexports.install = function (t) {\n  return function () {\n    setTimeout(t, 0);\n  };\n};", "'use strict';\n\nvar _ = require('../common/utils.js');\nvar DOM = require('../common/dom.js');\nvar EventEmitter = require('./event_emitter.js');\nvar Dataset = require('./dataset.js');\nvar css = require('./css.js');\n\n// constructor\n// -----------\n\nfunction Dropdown(o) {\n  var that = this;\n  var onSuggestionClick;\n  var onSuggestionMouseEnter;\n  var onSuggestionMouseLeave;\n\n  o = o || {};\n\n  if (!o.menu) {\n    _.error('menu is required');\n  }\n\n  if (!_.isArray(o.datasets) && !_.isObject(o.datasets)) {\n    _.error('1 or more datasets required');\n  }\n  if (!o.datasets) {\n    _.error('datasets is required');\n  }\n\n  this.isOpen = false;\n  this.isEmpty = true;\n  this.minLength = o.minLength || 0;\n  this.templates = {};\n  this.appendTo = o.appendTo || false;\n  this.css = _.mixin({}, css, o.appendTo ? css.appendTo : {});\n  this.cssClasses = o.cssClasses = _.mixin({}, css.defaultClasses, o.cssClasses || {});\n  this.cssClasses.prefix =\n    o.cssClasses.formattedPrefix || _.formatPrefix(this.cssClasses.prefix, this.cssClasses.noPrefix);\n\n  // bound functions\n  onSuggestionClick = _.bind(this._onSuggestionClick, this);\n  onSuggestionMouseEnter = _.bind(this._onSuggestionMouseEnter, this);\n  onSuggestionMouseLeave = _.bind(this._onSuggestionMouseLeave, this);\n\n  var cssClass = _.className(this.cssClasses.prefix, this.cssClasses.suggestion);\n  this.$menu = DOM.element(o.menu)\n    .on('mouseenter.aa', cssClass, onSuggestionMouseEnter)\n    .on('mouseleave.aa', cssClass, onSuggestionMouseLeave)\n    .on('click.aa', cssClass, onSuggestionClick);\n\n  this.$container = o.appendTo ? o.wrapper : this.$menu;\n\n  if (o.templates && o.templates.header) {\n    this.templates.header = _.templatify(o.templates.header);\n    this.$menu.prepend(this.templates.header());\n  }\n\n  if (o.templates && o.templates.empty) {\n    this.templates.empty = _.templatify(o.templates.empty);\n    this.$empty = DOM.element('<div class=\"' +\n      _.className(this.cssClasses.prefix, this.cssClasses.empty, true) + '\">' +\n      '</div>');\n    this.$menu.append(this.$empty);\n    this.$empty.hide();\n  }\n\n  this.datasets = _.map(o.datasets, function(oDataset) {\n    return initializeDataset(that.$menu, oDataset, o.cssClasses);\n  });\n  _.each(this.datasets, function(dataset) {\n    var root = dataset.getRoot();\n    if (root && root.parent().length === 0) {\n      that.$menu.append(root);\n    }\n    dataset.onSync('rendered', that._onRendered, that);\n  });\n\n  if (o.templates && o.templates.footer) {\n    this.templates.footer = _.templatify(o.templates.footer);\n    this.$menu.append(this.templates.footer());\n  }\n\n  var self = this;\n  DOM.element(window).resize(function() {\n    self._redraw();\n  });\n}\n\n// instance methods\n// ----------------\n\n_.mixin(Dropdown.prototype, EventEmitter, {\n\n  // ### private\n\n  _onSuggestionClick: function onSuggestionClick($e) {\n    this.trigger('suggestionClicked', DOM.element($e.currentTarget));\n  },\n\n  _onSuggestionMouseEnter: function onSuggestionMouseEnter($e) {\n    var elt = DOM.element($e.currentTarget);\n    if (elt.hasClass(_.className(this.cssClasses.prefix, this.cssClasses.cursor, true))) {\n      // we're already on the cursor\n      // => we're probably entering it again after leaving it for a nested div\n      return;\n    }\n    this._removeCursor();\n\n    // Fixes iOS double tap behaviour, by modifying the DOM right before the\n    // native href clicks happens, iOS will requires another tap to follow\n    // a suggestion that has an <a href> element inside\n    // https://www.google.com/search?q=ios+double+tap+bug+href\n    var suggestion = this;\n    setTimeout(function() {\n      // this exact line, when inside the main loop, will trigger a double tap bug\n      // on iOS devices\n      suggestion._setCursor(elt, false);\n    }, 0);\n  },\n\n  _onSuggestionMouseLeave: function onSuggestionMouseLeave($e) {\n    // $e.relatedTarget is the `EventTarget` the pointing device entered to\n    if ($e.relatedTarget) {\n      var elt = DOM.element($e.relatedTarget);\n      if (elt.closest('.' + _.className(this.cssClasses.prefix, this.cssClasses.cursor, true)).length > 0) {\n        // our father is a cursor\n        // => it means we're just leaving the suggestion for a nested div\n        return;\n      }\n    }\n    this._removeCursor();\n    this.trigger('cursorRemoved');\n  },\n\n  _onRendered: function onRendered(e, query) {\n    this.isEmpty = _.every(this.datasets, isDatasetEmpty);\n\n    if (this.isEmpty) {\n      if (query.length >= this.minLength) {\n        this.trigger('empty');\n      }\n\n      if (this.$empty) {\n        if (query.length < this.minLength) {\n          this._hide();\n        } else {\n          var html = this.templates.empty({\n            query: this.datasets[0] && this.datasets[0].query\n          });\n          this.$empty.html(html);\n          this.$empty.show();\n          this._show();\n        }\n      } else if (_.any(this.datasets, hasEmptyTemplate)) {\n        if (query.length < this.minLength) {\n          this._hide();\n        } else {\n          this._show();\n        }\n      } else {\n        this._hide();\n      }\n    } else if (this.isOpen) {\n      if (this.$empty) {\n        this.$empty.empty();\n        this.$empty.hide();\n      }\n\n      if (query.length >= this.minLength) {\n        this._show();\n      } else {\n        this._hide();\n      }\n    }\n\n    this.trigger('datasetRendered');\n\n    function isDatasetEmpty(dataset) {\n      return dataset.isEmpty();\n    }\n\n    function hasEmptyTemplate(dataset) {\n      return dataset.templates && dataset.templates.empty;\n    }\n  },\n\n  _hide: function() {\n    this.$container.hide();\n  },\n\n  _show: function() {\n    // can't use jQuery#show because $menu is a span element we want\n    // display: block; not dislay: inline;\n    this.$container.css('display', 'block');\n\n    this._redraw();\n\n    this.trigger('shown');\n  },\n\n  _redraw: function redraw() {\n    if (!this.isOpen || !this.appendTo) return;\n\n    this.trigger('redrawn');\n  },\n\n  _getSuggestions: function getSuggestions() {\n    return this.$menu.find(_.className(this.cssClasses.prefix, this.cssClasses.suggestion));\n  },\n\n  _getCursor: function getCursor() {\n    return this.$menu.find(_.className(this.cssClasses.prefix, this.cssClasses.cursor)).first();\n  },\n\n  _setCursor: function setCursor($el, updateInput) {\n    $el.first()\n      .addClass(_.className(this.cssClasses.prefix, this.cssClasses.cursor, true))\n      .attr('aria-selected', 'true');\n    this.trigger('cursorMoved', updateInput);\n  },\n\n  _removeCursor: function removeCursor() {\n    this._getCursor()\n      .removeClass(_.className(this.cssClasses.prefix, this.cssClasses.cursor, true))\n      .removeAttr('aria-selected');\n  },\n\n  _moveCursor: function moveCursor(increment) {\n    var $suggestions;\n    var $oldCursor;\n    var newCursorIndex;\n    var $newCursor;\n\n    if (!this.isOpen) {\n      return;\n    }\n\n    $oldCursor = this._getCursor();\n    $suggestions = this._getSuggestions();\n\n    this._removeCursor();\n\n    // shifting before and after modulo to deal with -1 index\n    newCursorIndex = $suggestions.index($oldCursor) + increment;\n    newCursorIndex = (newCursorIndex + 1) % ($suggestions.length + 1) - 1;\n\n    if (newCursorIndex === -1) {\n      this.trigger('cursorRemoved');\n\n      return;\n    } else if (newCursorIndex < -1) {\n      newCursorIndex = $suggestions.length - 1;\n    }\n\n    this._setCursor($newCursor = $suggestions.eq(newCursorIndex), true);\n\n    // in the case of scrollable overflow\n    // make sure the cursor is visible in the menu\n    this._ensureVisible($newCursor);\n  },\n\n  _ensureVisible: function ensureVisible($el) {\n    var elTop;\n    var elBottom;\n    var menuScrollTop;\n    var menuHeight;\n\n    elTop = $el.position().top;\n    elBottom = elTop + $el.height() +\n      parseInt($el.css('margin-top'), 10) +\n      parseInt($el.css('margin-bottom'), 10);\n    menuScrollTop = this.$menu.scrollTop();\n    menuHeight = this.$menu.height() +\n      parseInt(this.$menu.css('padding-top'), 10) +\n      parseInt(this.$menu.css('padding-bottom'), 10);\n\n    if (elTop < 0) {\n      this.$menu.scrollTop(menuScrollTop + elTop);\n    } else if (menuHeight < elBottom) {\n      this.$menu.scrollTop(menuScrollTop + (elBottom - menuHeight));\n    }\n  },\n\n  // ### public\n\n  close: function close() {\n    if (this.isOpen) {\n      this.isOpen = false;\n\n      this._removeCursor();\n      this._hide();\n\n      this.trigger('closed');\n    }\n  },\n\n  open: function open() {\n    if (!this.isOpen) {\n      this.isOpen = true;\n\n      if (!this.isEmpty) {\n        this._show();\n      }\n\n      this.trigger('opened');\n    }\n  },\n\n  setLanguageDirection: function setLanguageDirection(dir) {\n    this.$menu.css(dir === 'ltr' ? this.css.ltr : this.css.rtl);\n  },\n\n  moveCursorUp: function moveCursorUp() {\n    this._moveCursor(-1);\n  },\n\n  moveCursorDown: function moveCursorDown() {\n    this._moveCursor(+1);\n  },\n\n  getDatumForSuggestion: function getDatumForSuggestion($el) {\n    var datum = null;\n\n    if ($el.length) {\n      datum = {\n        raw: Dataset.extractDatum($el),\n        value: Dataset.extractValue($el),\n        datasetName: Dataset.extractDatasetName($el)\n      };\n    }\n\n    return datum;\n  },\n\n  getCurrentCursor: function getCurrentCursor() {\n    return this._getCursor().first();\n  },\n\n  getDatumForCursor: function getDatumForCursor() {\n    return this.getDatumForSuggestion(this._getCursor().first());\n  },\n\n  getDatumForTopSuggestion: function getDatumForTopSuggestion() {\n    return this.getDatumForSuggestion(this._getSuggestions().first());\n  },\n\n  cursorTopSuggestion: function cursorTopSuggestion() {\n    this._setCursor(this._getSuggestions().first(), false);\n  },\n\n  update: function update(query) {\n    _.each(this.datasets, updateDataset);\n\n    function updateDataset(dataset) {\n      dataset.update(query);\n    }\n  },\n\n  empty: function empty() {\n    _.each(this.datasets, clearDataset);\n    this.isEmpty = true;\n\n    function clearDataset(dataset) {\n      dataset.clear();\n    }\n  },\n\n  isVisible: function isVisible() {\n    return this.isOpen && !this.isEmpty;\n  },\n\n  destroy: function destroy() {\n    this.$menu.off('.aa');\n\n    this.$menu = null;\n\n    _.each(this.datasets, destroyDataset);\n\n    function destroyDataset(dataset) {\n      dataset.destroy();\n    }\n  }\n});\n\n// helper functions\n// ----------------\nDropdown.Dataset = Dataset;\n\nfunction initializeDataset($menu, oDataset, cssClasses) {\n  return new Dropdown.Dataset(_.mixin({$menu: $menu, cssClasses: cssClasses}, oDataset));\n}\n\nmodule.exports = Dropdown;\n", "'use strict';\n\nvar datasetKey = 'aaDataset';\nvar valueKey = 'aaValue';\nvar datumKey = 'aaDatum';\n\nvar _ = require('../common/utils.js');\nvar DOM = require('../common/dom.js');\nvar html = require('./html.js');\nvar css = require('./css.js');\nvar EventEmitter = require('./event_emitter.js');\n\n// constructor\n// -----------\n\nfunction Dataset(o) {\n  o = o || {};\n  o.templates = o.templates || {};\n\n  if (!o.source) {\n    _.error('missing source');\n  }\n\n  if (o.name && !isValidName(o.name)) {\n    _.error('invalid dataset name: ' + o.name);\n  }\n\n  // tracks the last query the dataset was updated for\n  this.query = null;\n  this._isEmpty = true;\n\n  this.highlight = !!o.highlight;\n  this.name = typeof o.name === 'undefined' || o.name === null ? _.getUniqueId() : o.name;\n\n  this.source = o.source;\n  this.displayFn = getDisplayFn(o.display || o.displayKey);\n\n  this.debounce = o.debounce;\n\n  this.cache = o.cache !== false;\n\n  this.templates = getTemplates(o.templates, this.displayFn);\n\n  this.css = _.mixin({}, css, o.appendTo ? css.appendTo : {});\n  this.cssClasses = o.cssClasses = _.mixin({}, css.defaultClasses, o.cssClasses || {});\n  this.cssClasses.prefix =\n    o.cssClasses.formattedPrefix || _.formatPrefix(this.cssClasses.prefix, this.cssClasses.noPrefix);\n\n  var clazz = _.className(this.cssClasses.prefix, this.cssClasses.dataset);\n  this.$el = o.$menu && o.$menu.find(clazz + '-' + this.name).length > 0 ?\n    DOM.element(o.$menu.find(clazz + '-' + this.name)[0]) :\n    DOM.element(\n      html.dataset.replace('%CLASS%', this.name)\n        .replace('%PREFIX%', this.cssClasses.prefix)\n        .replace('%DATASET%', this.cssClasses.dataset)\n    );\n\n  this.$menu = o.$menu;\n  this.clearCachedSuggestions();\n}\n\n// static methods\n// --------------\n\nDataset.extractDatasetName = function extractDatasetName(el) {\n  return DOM.element(el).data(datasetKey);\n};\n\nDataset.extractValue = function extractValue(el) {\n  return DOM.element(el).data(valueKey);\n};\n\nDataset.extractDatum = function extractDatum(el) {\n  var datum = DOM.element(el).data(datumKey);\n  if (typeof datum === 'string') {\n    // Zepto has an automatic deserialization of the\n    // JSON encoded data attribute\n    datum = JSON.parse(datum);\n  }\n  return datum;\n};\n\n// instance methods\n// ----------------\n\n_.mixin(Dataset.prototype, EventEmitter, {\n\n  // ### private\n\n  _render: function render(query, suggestions) {\n    if (!this.$el) {\n      return;\n    }\n    var that = this;\n\n    var hasSuggestions;\n    var renderArgs = [].slice.call(arguments, 2);\n    this.$el.empty();\n\n    hasSuggestions = suggestions && suggestions.length;\n    this._isEmpty = !hasSuggestions;\n\n    if (!hasSuggestions && this.templates.empty) {\n      this.$el\n        .html(getEmptyHtml.apply(this, renderArgs))\n        .prepend(that.templates.header ? getHeaderHtml.apply(this, renderArgs) : null)\n        .append(that.templates.footer ? getFooterHtml.apply(this, renderArgs) : null);\n    } else if (hasSuggestions) {\n      this.$el\n        .html(getSuggestionsHtml.apply(this, renderArgs))\n        .prepend(that.templates.header ? getHeaderHtml.apply(this, renderArgs) : null)\n        .append(that.templates.footer ? getFooterHtml.apply(this, renderArgs) : null);\n    } else if (suggestions && !Array.isArray(suggestions)) {\n      throw new TypeError('suggestions must be an array');\n    }\n\n    if (this.$menu) {\n      this.$menu.addClass(\n        this.cssClasses.prefix + (hasSuggestions ? 'with' : 'without') + '-' + this.name\n      ).removeClass(\n        this.cssClasses.prefix + (hasSuggestions ? 'without' : 'with') + '-' + this.name\n      );\n    }\n\n    this.trigger('rendered', query);\n\n    function getEmptyHtml() {\n      var args = [].slice.call(arguments, 0);\n      args = [{query: query, isEmpty: true}].concat(args);\n      return that.templates.empty.apply(this, args);\n    }\n\n    function getSuggestionsHtml() {\n      var args = [].slice.call(arguments, 0);\n      var $suggestions;\n      var nodes;\n      var self = this;\n\n      var suggestionsHtml = html.suggestions.\n        replace('%PREFIX%', this.cssClasses.prefix).\n        replace('%SUGGESTIONS%', this.cssClasses.suggestions);\n      $suggestions = DOM\n        .element(suggestionsHtml)\n        .css(this.css.suggestions);\n\n      // jQuery#append doesn't support arrays as the first argument\n      // until version 1.8, see http://bugs.jquery.com/ticket/11231\n      nodes = _.map(suggestions, getSuggestionNode);\n      $suggestions.append.apply($suggestions, nodes);\n\n      return $suggestions;\n\n      function getSuggestionNode(suggestion) {\n        var $el;\n\n        var suggestionHtml = html.suggestion.\n          replace('%PREFIX%', self.cssClasses.prefix).\n          replace('%SUGGESTION%', self.cssClasses.suggestion);\n        $el = DOM.element(suggestionHtml)\n          .attr({\n            role: 'option',\n            id: ['option', Math.floor(Math.random() * 100000000)].join('-')\n          })\n          .append(that.templates.suggestion.apply(this, [suggestion].concat(args)));\n\n        $el.data(datasetKey, that.name);\n        $el.data(valueKey, that.displayFn(suggestion) || undefined); // this led to undefined return value\n        $el.data(datumKey, JSON.stringify(suggestion));\n        $el.children().each(function() { DOM.element(this).css(self.css.suggestionChild); });\n\n        return $el;\n      }\n    }\n\n    function getHeaderHtml() {\n      var args = [].slice.call(arguments, 0);\n      args = [{query: query, isEmpty: !hasSuggestions}].concat(args);\n      return that.templates.header.apply(this, args);\n    }\n\n    function getFooterHtml() {\n      var args = [].slice.call(arguments, 0);\n      args = [{query: query, isEmpty: !hasSuggestions}].concat(args);\n      return that.templates.footer.apply(this, args);\n    }\n  },\n\n  // ### public\n\n  getRoot: function getRoot() {\n    return this.$el;\n  },\n\n  update: function update(query) {\n    function handleSuggestions(suggestions) {\n      // if the update has been canceled or if the query has changed\n      // do not render the suggestions as they've become outdated\n      if (!this.canceled && query === this.query) {\n        // concat all the other arguments that could have been passed\n        // to the render function, and forward them to _render\n        var extraArgs = [].slice.call(arguments, 1);\n        this.cacheSuggestions(query, suggestions, extraArgs);\n        this._render.apply(this, [query, suggestions].concat(extraArgs));\n      }\n    }\n\n    this.query = query;\n    this.canceled = false;\n\n    if (this.shouldFetchFromCache(query)) {\n      handleSuggestions.apply(this, [this.cachedSuggestions].concat(this.cachedRenderExtraArgs));\n    } else {\n      var that = this;\n      var execSource = function() {\n        // When the call is debounced the condition avoid to do a useless\n        // request with the last character when the input has been cleared\n        if (!that.canceled) {\n          that.source(query, handleSuggestions.bind(that));\n        }\n      };\n\n      if (this.debounce) {\n        var later = function() {\n          that.debounceTimeout = null;\n          execSource();\n        };\n        clearTimeout(this.debounceTimeout);\n        this.debounceTimeout = setTimeout(later, this.debounce);\n      } else {\n        execSource();\n      }\n    }\n  },\n\n  cacheSuggestions: function cacheSuggestions(query, suggestions, extraArgs) {\n    this.cachedQuery = query;\n    this.cachedSuggestions = suggestions;\n    this.cachedRenderExtraArgs = extraArgs;\n  },\n\n  shouldFetchFromCache: function shouldFetchFromCache(query) {\n    return this.cache &&\n      this.cachedQuery === query &&\n      this.cachedSuggestions &&\n      this.cachedSuggestions.length;\n  },\n\n  clearCachedSuggestions: function clearCachedSuggestions() {\n    delete this.cachedQuery;\n    delete this.cachedSuggestions;\n    delete this.cachedRenderExtraArgs;\n  },\n\n  cancel: function cancel() {\n    this.canceled = true;\n  },\n\n  clear: function clear() {\n    if (this.$el) {\n      this.cancel();\n      this.$el.empty();\n      this.trigger('rendered', '');\n    }\n  },\n\n  isEmpty: function isEmpty() {\n    return this._isEmpty;\n  },\n\n  destroy: function destroy() {\n    this.clearCachedSuggestions();\n    this.$el = null;\n  }\n});\n\n// helper functions\n// ----------------\n\nfunction getDisplayFn(display) {\n  display = display || 'value';\n\n  return _.isFunction(display) ? display : displayFn;\n\n  function displayFn(obj) {\n    return obj[display];\n  }\n}\n\nfunction getTemplates(templates, displayFn) {\n  return {\n    empty: templates.empty && _.templatify(templates.empty),\n    header: templates.header && _.templatify(templates.header),\n    footer: templates.footer && _.templatify(templates.footer),\n    suggestion: templates.suggestion || suggestionTemplate\n  };\n\n  function suggestionTemplate(context) {\n    return '<p>' + displayFn(context) + '</p>';\n  }\n}\n\nfunction isValidName(str) {\n  // dashes, underscores, letters, and numbers\n  return (/^[_a-zA-Z0-9-]+$/).test(str);\n}\n\nmodule.exports = Dataset;\n", "'use strict';\n\nmodule.exports = {\n  hits: require('./hits.js'),\n  popularIn: require('./popularIn.js')\n};\n", "'use strict';\n\nvar _ = require('../common/utils.js');\nvar version = require('../../version.js');\nvar parseAlgoliaClientVersion = require('../common/parseAlgoliaClientVersion.js');\n\nmodule.exports = function search(index, params) {\n  var algoliaVersion = parseAlgoliaClientVersion(index.as._ua);\n  if (algoliaVersion && algoliaVersion[0] >= 3 && algoliaVersion[1] > 20) {\n    params = params || {};\n    params.additionalUA = 'autocomplete.js ' + version;\n  }\n  return sourceFn;\n\n  function sourceFn(query, cb) {\n    index.search(query, params, function(error, content) {\n      if (error) {\n        _.error(error.message);\n        return;\n      }\n      cb(content.hits, content);\n    });\n  }\n};\n", "'use strict';\n\nvar _ = require('../common/utils.js');\nvar version = require('../../version.js');\nvar parseAlgoliaClientVersion = require('../common/parseAlgoliaClientVersion.js');\n\nmodule.exports = function popularIn(index, params, details, options) {\n  var algoliaVersion = parseAlgoliaClientVersion(index.as._ua);\n  if (algoliaVersion && algoliaVersion[0] >= 3 && algoliaVersion[1] > 20) {\n    params = params || {};\n    params.additionalUA = 'autocomplete.js ' + version;\n  }\n  if (!details.source) {\n    return _.error(\"Missing 'source' key\");\n  }\n  var source = _.isFunction(details.source) ? details.source : function(hit) { return hit[details.source]; };\n\n  if (!details.index) {\n    return _.error(\"Missing 'index' key\");\n  }\n  var detailsIndex = details.index;\n\n  options = options || {};\n\n  return sourceFn;\n\n  function sourceFn(query, cb) {\n    index.search(query, params, function(error, content) {\n      if (error) {\n        _.error(error.message);\n        return;\n      }\n\n      if (content.hits.length > 0) {\n        var first = content.hits[0];\n\n        var detailsParams = _.mixin({hitsPerPage: 0}, details);\n        delete detailsParams.source; // not a query parameter\n        delete detailsParams.index; // not a query parameter\n\n        var detailsAlgoliaVersion = parseAlgoliaClientVersion(detailsIndex.as._ua);\n        if (detailsAlgoliaVersion && detailsAlgoliaVersion[0] >= 3 && detailsAlgoliaVersion[1] > 20) {\n          params.additionalUA = 'autocomplete.js ' + version;\n        }\n\n        detailsIndex.search(source(first), detailsParams, function(error2, content2) {\n          if (error2) {\n            _.error(error2.message);\n            return;\n          }\n\n          var suggestions = [];\n\n          // add the 'all department' entry before others\n          if (options.includeAll) {\n            var label = options.allTitle || 'All departments';\n            suggestions.push(_.mixin({\n              facet: {value: label, count: content2.nbHits}\n            }, _.cloneDeep(first)));\n          }\n\n          // enrich the first hit iterating over the facets\n          _.each(content2.facets, function(values, facet) {\n            _.each(values, function(count, value) {\n              suggestions.push(_.mixin({\n                facet: {facet: facet, value: value, count: count}\n              }, _.cloneDeep(first)));\n            });\n          });\n\n          // append all other hits\n          for (var i = 1; i < content.hits.length; ++i) {\n            suggestions.push(content.hits[i]);\n          }\n\n          cb(suggestions, content);\n        });\n\n        return;\n      }\n\n      cb([]);\n    });\n  }\n};\n", "// we need to export using commonjs for ease of usage in all\n// JavaScript environments\n// We therefore need to import in commonjs too. see:\n// https://github.com/webpack/webpack/issues/4039\n\n/* eslint-disable import/no-commonjs */\n\nconst places = require('./src/places');\nconst version = require('./src/version');\n\n// must use module.exports to be commonJS compatible\nmodule.exports = places.default;\nmodule.exports.version = version.default;\n/* eslint-enable import/no-commonjs */\n"], "sourceRoot": ""}