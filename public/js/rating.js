/**
 * Movie Rating System - Vanilla JavaScript Implementation
 * Integrates with MovieController::rateMovie() endpoint
 */

class MovieRating {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            maxStars: 5,
            currentRating: 0,
            movieSlug: '',
            ratingUrl: '',
            readonly: false,
            showHint: true,
            ...options
        };
        
        this.userRating = 0;
        this.isRated = false;
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.createStarElements();
        this.bindEvents();
        this.updateDisplay();
    }
    
    createStarElements() {
        this.container.innerHTML = '';
        this.container.className = 'movie-rating-stars flex items-center space-x-1';
        
        // Create stars container
        this.starsContainer = document.createElement('div');
        this.starsContainer.className = 'stars-container flex items-center space-x-1';
        
        // Create individual stars
        this.stars = [];
        for (let i = 1; i <= this.options.maxStars; i++) {
            const star = document.createElement('button');
            star.type = 'button';
            star.className = 'star-button text-2xl transition-all duration-200 focus:outline-none';
            star.innerHTML = '★';
            star.dataset.rating = i;
            
            if (this.options.readonly) {
                star.disabled = true;
                star.className += ' cursor-default';
            } else {
                star.className += ' cursor-pointer hover:scale-110';
            }
            
            this.stars.push(star);
            this.starsContainer.appendChild(star);
        }
        
        this.container.appendChild(this.starsContainer);
        
        // Create rating info
        if (this.options.showHint) {
            this.hintElement = document.createElement('span');
            this.hintElement.className = 'rating-hint text-sm text-gray-400 ml-3';
            this.container.appendChild(this.hintElement);
        }
        
        // Create loading indicator
        this.loadingElement = document.createElement('div');
        this.loadingElement.className = 'rating-loading hidden ml-3';
        this.loadingElement.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-main"></div>';
        this.container.appendChild(this.loadingElement);
        
        // Create message container
        this.messageElement = document.createElement('div');
        this.messageElement.className = 'rating-message mt-2 text-sm';
        this.container.appendChild(this.messageElement);
    }
    
    bindEvents() {
        if (this.options.readonly) return;
        
        this.stars.forEach((star, index) => {
            // Hover events
            star.addEventListener('mouseenter', () => this.handleHover(index + 1));
            star.addEventListener('mouseleave', () => this.handleMouseLeave());
            
            // Click events
            star.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleClick(index + 1);
            });
        });
        
        // Container mouse leave
        this.starsContainer.addEventListener('mouseleave', () => this.handleMouseLeave());
    }
    
    handleHover(rating) {
        if (this.isLoading || this.isRated) return;
        
        this.updateStarDisplay(rating);
        this.updateHint(`Đánh giá ${rating} sao`);
    }
    
    handleMouseLeave() {
        if (this.isLoading || this.isRated) return;
        
        this.updateStarDisplay(this.userRating || this.options.currentRating);
        this.updateHint('');
    }
    
    async handleClick(rating) {
        if (this.isLoading || this.isRated || this.options.readonly) return;
        
        this.userRating = rating;
        this.setLoading(true);
        
        try {
            const response = await this.submitRating(rating);
            
            if (response.status) {
                this.handleRatingSuccess(response);
            } else {
                this.handleRatingError(response.message || 'Có lỗi xảy ra khi đánh giá');
            }
        } catch (error) {
            console.error('Rating submission error:', error);
            this.handleRatingError('Không thể kết nối đến server');
        } finally {
            this.setLoading(false);
        }
    }
    
    async submitRating(rating) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        const response = await fetch(this.options.ratingUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                rating: rating
            })
        });
        
        if (!response.ok) {
            if (response.status === 401) {
                const data = await response.json();
                if (data.redirect) {
                    window.location.href = data.redirect;
                    return;
                }
                throw new Error('Bạn cần đăng nhập để đánh giá phim');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    handleRatingSuccess(response) {
        this.isRated = true;
        this.options.readonly = true;
        
        // Update stars to readonly state
        this.stars.forEach(star => {
            star.disabled = true;
            star.className = star.className.replace('cursor-pointer hover:scale-110', 'cursor-default');
        });
        
        // Update display with new rating data
        if (response.rating_star !== undefined) {
            this.options.currentRating = response.rating_star;
        }
        
        this.updateStarDisplay(this.userRating);
        this.showMessage(`Bạn đã đánh giá ${this.userRating} sao cho phim này!`, 'success');
        
        // Update rating statistics if elements exist
        this.updateRatingStats(response);
    }
    
    handleRatingError(message) {
        this.userRating = 0;
        this.updateStarDisplay(this.options.currentRating);
        this.showMessage(message, 'error');
    }
    
    updateStarDisplay(rating) {
        this.stars.forEach((star, index) => {
            const starRating = index + 1;
            
            if (starRating <= rating) {
                star.className = star.className.replace(/text-gray-\d+/, 'text-yellow-400');
            } else {
                star.className = star.className.replace(/text-yellow-\d+/, 'text-gray-300');
            }
        });
    }
    
    updateHint(text) {
        if (this.hintElement) {
            this.hintElement.textContent = text;
        }
    }
    
    showMessage(message, type = 'info') {
        if (!this.messageElement) return;
        
        this.messageElement.textContent = message;
        this.messageElement.className = `rating-message mt-2 text-sm ${
            type === 'success' ? 'text-green-500' : 
            type === 'error' ? 'text-red-500' : 
            'text-gray-400'
        }`;
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            this.messageElement.textContent = '';
        }, 5000);
    }
    
    setLoading(loading) {
        this.isLoading = loading;
        
        if (loading) {
            this.loadingElement.classList.remove('hidden');
            this.starsContainer.classList.add('opacity-50');
        } else {
            this.loadingElement.classList.add('hidden');
            this.starsContainer.classList.remove('opacity-50');
        }
    }
    
    updateRatingStats(response) {
        // Update average rating display
        const avgElement = document.querySelector('.rating-average');
        if (avgElement && response.rating_star !== undefined) {
            avgElement.textContent = response.rating_star.toFixed(1);
        }
        
        // Update rating count
        const countElement = document.querySelector('.rating-count');
        if (countElement && response.rating_count !== undefined) {
            countElement.textContent = response.rating_count;
        }
    }
    
    // Public methods
    setRating(rating) {
        this.options.currentRating = rating;
        this.updateStarDisplay(rating);
    }
    
    setReadonly(readonly) {
        this.options.readonly = readonly;
        this.stars.forEach(star => {
            star.disabled = readonly;
            if (readonly) {
                star.className = star.className.replace('cursor-pointer hover:scale-110', 'cursor-default');
            } else {
                star.className = star.className.replace('cursor-default', 'cursor-pointer hover:scale-110');
            }
        });
    }
    
    updateDisplay() {
        this.updateStarDisplay(this.options.currentRating);
    }
}

// Initialize rating components when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all rating containers
    document.querySelectorAll('[data-movie-rating]').forEach(container => {
        const options = {
            maxStars: parseInt(container.dataset.maxStars) || 5,
            currentRating: parseFloat(container.dataset.currentRating) || 0,
            movieSlug: container.dataset.movieSlug || '',
            ratingUrl: container.dataset.ratingUrl || '',
            readonly: container.dataset.readonly === 'true',
            showHint: container.dataset.showHint !== 'false'
        };
        
        new MovieRating(container, options);
    });
});

// Export for use in other scripts
window.MovieRating = MovieRating;
