{"appTitle": "CKFinder", "dir": "ltr", "langCode": "es-mx", "common": {"abort": "Abort", "cancel": "<PERSON><PERSON><PERSON>", "choose": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "copy": "Copy", "delete": "Bo<PERSON>r", "download": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "maximize": "Maximize", "messageTitle": "Información", "minimize": "Minimize", "move": "Move", "newNameDialogTitle": "Nuevo nombre", "ok": "OK", "pleaseWait": "Please wait.", "rememberDecision": "Recordar mi decisión", "rename": "Renombrar", "showMore": "Show more", "skip": "<PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "view": "<PERSON>er"}, "units": {"dateFormat": "dd/mm/yyyy H:MM", "dateAmPm": ["AM", "PM"], "kb": "{size} KB", "mb": "{size} MB", "gb": "{size} GB", "sizePerSecond": "{size}/s", "pixelShort": "px"}, "files": {"autoRename": "Auto-renombrar", "countMany": "{count} archivos", "countOne": "1 archivo", "deleteConfirmation": "Are you sure you want to delete {count} files?", "fileDeleteConfirmation": "¿Está seguro de que quiere borrar el archivo \"{name}\".?", "fileRenameExtensionConfirmation": "¿Está seguro de querer cambiar la extensión del archivo? El archivo puede dejar de ser usable.", "fileRenameLabel": "Por favor, escriba el nuevo nombre del archivo: ", "filesPaneTitle": "Files pane", "filesRefresh": "Refreshing files", "filterPlaceholder": "Filter", "gettingFileData": "Getting file data.", "overwrite": "Sobreescribir", "loadingFilesPane": {"title": "Loading...", "text": "The files are being loaded."}, "emptyFilesPane": {"title": "The folder is empty.", "text": "Use the <strong>Upload</strong> button or <strong>drag and drop</strong> your files here."}, "filterFilesEmpty": {"title": "Nothing found.", "text": "Please try using different search criteria."}}, "folders": {"deleteConfirmation": "¿Está seguro de que quiere borrar la carpeta \"{name}\"?", "destinationFolder": "Destination Folder", "newNameLabel": "Por favor, escriba el nombre para la nueva carpeta: ", "newSubfolder": "Nueva Subcarpeta", "renameDialogTitle": "<PERSON><PERSON>", "treeTitle": "Folders tree"}, "copy": {"dropMenuItem": "<PERSON><PERSON><PERSON> aqu<PERSON>", "errorDialogTitle": "Files that cannot be copied:", "manyFilesDialogTitle": "Copy {count} Files To...", "manyFilesWait": "Copying {count} files.", "oneFileDialogTitle": "Copy File To...", "oneFileWait": "Copying file.", "operationLabel": "Copying Files", "operationSummary": "Number of files copied: {count}."}, "move": {"dropMenuItem": "Mover aqu<PERSON>", "errorDialogTitle": "Files that cannot be moved:", "manyFilesDialogTitle": "Move {count} Files To...", "manyFilesWait": "Moving {count} files.", "oneFileDialogTitle": "Move File To...", "oneFileWait": "Moving file.", "operationLabel": "Moving Files", "operationSummary": "Number of files moved: {count}."}, "upload": {"addFiles": "Añadir archivos", "bytesCountProgress": "({bytesUploaded} of {bytesTotal})", "details": "Details", "filesCountProgress": "{filesUploaded} of {filesTotal}", "progressLabel": "Subida en progreso.", "progressMessage": "Uploading...", "selectFileLabel": "Elija el archivo a subir", "selectFiles": "Select files to upload", "success": "Upload finished!", "summary": "Uploaded files: {count}"}, "settings": {"display": "Mostrar:", "displayDate": "<PERSON><PERSON>", "displayName": "Nombre de archivo", "displaySize": "Tamaño del archivo", "sortAscending": "Ascending", "sortBy": "Sort by", "sortByOrder": "Order", "sortDescending": "Descending", "thumbnailSize": "<PERSON><PERSON><PERSON><PERSON>", "title": "Configuración", "viewType": "View", "viewTypeCompact": "Compact", "viewTypeList": "List", "viewTypeThumbnails": "Thumbnails"}, "errors": {"codes": {"10": "<PERSON><PERSON><PERSON>.", "11": "El tipo de recurso no ha sido especificado en la solicitud.", "12": "El tipo de recurso solicitado no es válido.", "13": "The connector configuration file is invalid.", "14": "Invalid connector plugin: {pluginName}.", "102": "Nombre de archivo o carpeta no válido.", "103": "No se ha podido completar la solicitud debido a las restricciones de autorización.", "104": "No ha sido posible completar la solicitud debido a restricciones en el sistema de archivos.", "105": "La extensión del archivo no es válida.", "109": "Petición inválida.", "110": "Error des<PERSON>.", "111": "It was not possible to complete the request due to resulting file size.", "115": "Ya existe un archivo o carpeta con ese nombre.", "116": "No se ha encontrado la carpeta. Por favor, actualice y pruebe de nuevo.", "117": "No se ha encontrado el archivo. Por favor, actualice la lista de archivos y pruebe de nuevo.", "118": "Las rutas origen y destino son iguales.", "201": "Ya existía un archivo con ese nombre. El archivo subido ha sido renombrado como \"{name}\".", "202": "Archivo inválido.", "203": "Archivo inválido. El tamaño es demasiado grande.", "204": "El archivo subido está corrupto.", "205": "La carpeta temporal no está disponible en el servidor para las subidas.", "206": "La subida se ha cancelado por razones de seguridad. El archivo contenía código HTML.", "207": "El archivo subido ha sido renombrado como \"{name}\".", "300": "Ha fallado el mover el(los) archivo(s).", "301": "Ha fallado el copiar el(los) archivo(s).", "302": "Deleting file(s) failed.", "500": "El navegador de archivos está deshabilitado por razones de seguridad. Por favor, contacte con el administrador de su sistema y compruebe el archivo de configuración de CKFinder.", "501": "El soporte para iconos está deshabilitado."}, "deleteFilePermissions": "Removing files is not allowed in this folder.", "fileInvalidCharacters": "El nombre del archivo no puede contener ninguno de los caracteres siguientes: {disallowedCharacters}", "fileNameNotEmpty": "The file name cannot be empty.", "folderInvalidCharacters": "El nombre de la carpeta no puede contener ninguno de los caracteres siguientes: {disallowedCharacters}", "incorrectExtension": "La extensión del archivo no está permitida en esta carpeta.", "missingFile": "The requested file is no longer available.", "missingFolder": "The folder you are trying to reach is no longer available.", "noUploadFolderSelected": "Por favor, escoja la carpeta antes de iniciar la subida.", "operationCompleted": "Operation completed with errors.", "renameFilePermissions": "Renaming files is not allowed in this folder.", "unknown": "No ha sido posible completar la solicitud. (Error {number})", "unknownUploadError": "Error enviando el archivo.", "uploadErrors": "Upload finished with errors.", "uploadPermissions": "No puede subir archivos."}, "chooseResizedImage": {"title": "Choose Resized", "originalSize": "Original Size", "sizes": {"custom": "Custom", "large": "Large", "max": "Max", "medium": "Medium", "small": "Small"}}, "editImage": {"adjust": "Adjust", "apply": "Apply", "confirmExit": "Are you sure you want to close? You have unsaved changes to the image.", "crop": "Crop", "downloadAction": "Downloading original image.", "keepAspectRatio": "Keep Aspect Ratio", "loading": "Loading image", "presets": "Presets", "reset": "Reset", "resize": "Resize", "rotate": "Rotate", "rotateAntiClockwise": "90&deg; Left", "rotateClockwise": "90&deg; Right", "save": "Save", "saveDialogFileExists": "File with the same name already exists in folder.", "saveDialogOverwrite": "Overwrite File", "saveDialogSaveAs": "Save as:", "saveDialogTitle": "Save Changes", "title": "Edit Image", "transformationAction": "Applying transformations.", "uploadAction": "Uploading edited image.", "filters": {"brightness": "Brightness", "clip": "Clip", "contrast": "Contrast", "exposure": "Exposure", "gamma": "Gamma", "hue": "<PERSON><PERSON>", "noise": "Noise", "saturation": "Saturation", "sepia": "Sepia", "sharpen": "Sharpen", "stackBlur": "Blur", "vibrance": "Vibrance"}, "preset": {"clarity": "Clarity", "concentrate": "Concentrate", "crossProcess": "Cross Process", "glowingSun": "Glowing Sun", "grungy": "Grungy", "hazyDays": "Hazy Days", "hemingway": "<PERSON><PERSON><PERSON>", "herMajesty": "Her Majesty", "jarques": "<PERSON><PERSON><PERSON>", "lomo": "Lomo", "love": "Love", "nostalgia": "Nostalgia", "oldBoot": "Old Boot", "orangePeel": "Orange Peel", "pinhole": "<PERSON><PERSON>ole", "sinCity": "Sin City", "sunrise": "Sunrise", "vintage": "Vintage"}}, "shortcuts": {"title": "Keyboard Shortcuts", "general": {"action": "Execute default action", "firstItem": "Go to first item", "focusFilesPane": "Focus files pane", "focusFoldersPane": "Focus folders pane or breadcrumbs", "focusNext": "Focus next pane", "focusToolbar": "Focus toolbar", "lastItem": "Go to last item", "listShortcuts": "Open this help dialog", "nextItem": "Go to next item", "previousItem": "Go to previous item", "showContextMenu": "Open context menu", "title": "General Interface"}, "files": {"addToSelectionAbove": "Add or remove files above to selection", "addToSelectionBelow": "Add or remove files below to selection", "addToSelectionLeft": "Add or remove file on the left to selection", "addToSelectionRight": "Add or remove file on the right to selection", "delete": "Delete file(s)", "refresh": "Refresh files", "rename": "Rename file", "selectAll": "Select all files", "upload": "Upload file(s)"}, "folders": {"collapseOrParent": "Collapse folder / Go to parent folder", "delete": "Delete folder", "expandOrSubfolder": "Expand folder / Go to first subfolder", "title": "Folders Pane"}, "keys": {"ctrl": "Control", "delete": "Delete", "downArrow": "Down arrow", "escape": "Escape", "leftArrow": "Left arrow", "question": "Question mark", "rightArrow": "Right arrow", "upArrow": "Up arrow"}, "keysAbbreviations": {"alt": "alt", "ctrl": "ctrl", "del": "del", "end": "end", "home": "home", "shift": "shift"}}}